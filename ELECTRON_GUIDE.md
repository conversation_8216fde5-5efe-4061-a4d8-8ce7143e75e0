# Electron 应用开发和部署指南

## 开发环境

### 启动开发环境
```bash
# 同时启动 Next.js 和 Electron
npm run electron-dev

# 或者分别启动
npm run dev    # 启动 Next.js 开发服务器
npm run electron  # 启动 Electron 应用
```

### 调试
- 开发环境下会自动打开开发者工具
- 可以在 Chrome DevTools 中调试 React 组件
- 使用 `console.log` 在主进程中调试

## 生产环境

### 构建和打包
```bash
# 构建 Next.js 应用并打包 Electron
npm run dist

# 或者分步执行
npm run build        # 构建 Next.js
npm run electron-build  # 打包 Electron
```

### 打包输出
- 打包后的文件在 `dist/` 目录中
- macOS: `.dmg` 安装包
- Windows: `.exe` 安装程序
- Linux: `.AppImage` 文件

## 自动更新

### 配置 GitHub Releases
1. 在 GitHub 创建仓库
2. 生成 GitHub Token
3. 配置 `package.json` 中的 publish 设置
4. 设置环境变量 `GH_TOKEN`

### 发布更新
```bash
# 发布到 GitHub Releases
npm run dist -- --publish=always

# 或者手动上传到 GitHub Releases
npm run dist
```

### 自动更新流程
1. 应用启动时检查更新
2. 发现新版本后后台下载
3. 下载完成后提示用户重启
4. 重启后自动安装更新

## 目录结构

```
emotion-recognition-app/
├── main.js                 # Electron 主进程
├── preload.js             # 预加载脚本
├── backend/               # Java 后端
│   ├── app.jar           # Java 应用 JAR 文件
│   └── README.md         # Java 集成说明
├── assets/               # 应用图标
│   ├── icon.png
│   ├── icon.icns
│   └── icon.ico
├── out/                  # Next.js 导出文件
├── dist/                 # 打包输出
└── app/                  # Next.js 应用
```

## 用户数据目录

应用会在用户目录创建以下文件夹：
- `~/.emotion-recognition-app/database/` - 数据库文件
- `~/.emotion-recognition-app/files/` - 上传文件
- `~/.emotion-recognition-app/models/` - 模型文件
- `~/.emotion-recognition-app/logs/` - 日志文件

## 性能优化

### 启动优化
- 使用 `show: false` 创建窗口，就绪后显示
- 并行启动 Next.js 和 Java 后端
- 使用 `wait-on` 等待服务就绪

### 打包优化
- 只打包必要的文件
- 使用 `files` 配置指定打包文件
- 考虑使用 `electron-builder` 的压缩选项

## 故障排除

### 常见问题
1. **Java 后端启动失败**
   - 检查 JRE 是否安装
   - 检查 JAR 文件是否存在
   - 查看控制台错误信息

2. **Next.js 服务器启动失败**
   - 检查端口 3000 是否被占用
   - 检查 Node.js 版本是否兼容

3. **打包失败**
   - 检查 electron-builder 配置
   - 确保所有依赖都已安装

### 调试技巧
- 使用 `console.log` 调试主进程
- 使用 Chrome DevTools 调试渲染进程
- 检查 `~/.emotion-recognition-app/logs/` 中的日志文件

## 安全考虑

1. **禁用 Node.js 集成**
   ```js
   webPreferences: {
     nodeIntegration: false,
     contextIsolation: true
   }
   ```

2. **使用预加载脚本**
   - 通过 `preload.js` 安全地暴露 API
   - 避免在渲染进程中直接使用 Node.js API

3. **验证外部链接**
   - 使用 `setWindowOpenHandler` 处理外部链接
   - 防止恶意链接在应用中打开

## 部署清单

- [ ] 设置应用图标
- [ ] 配置打包参数
- [ ] 测试自动更新
- [ ] 设置代码签名（可选）
- [ ] 配置 GitHub Actions 自动发布（可选）
- [ ] 准备 Java 后端 JAR 文件
- [ ] 测试不同平台的兼容性