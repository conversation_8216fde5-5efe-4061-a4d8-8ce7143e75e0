'use client'

import { Role, Permission } from '@/lib/mock/roleData'
import { Button } from '@/components/ui/button'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { useToast } from '@/components/ui/use-toast'
import { useState, useEffect } from 'react'
import { Checkbox } from '@/components/ui/checkbox'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'

interface PermissionDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  role: Role
  onSuccess: () => void
}

export function PermissionDialog({ open, onOpenChange, role, onSuccess }: PermissionDialogProps) {
  const { toast } = useToast()
  const [loading, setLoading] = useState(false)
  const [selectedPermissions, setSelectedPermissions] = useState<string[]>(
    role.permissions.map(p => p.id)
  )

  // 按模块分组权限
  const [permissions, setPermissions] = useState<{ [key: string]: Permission[] }>({})

  // 获取所有权限列表
  const fetchPermissions = async () => {
    try {
      const response = await fetch('/api/permissions')
      if (!response.ok) {
        throw new Error('获取权限列表失败')
      }
      const data = await response.json()

      // 按模块分组
      const groupedPermissions = data.reduce(
        (acc: { [key: string]: Permission[] }, curr: Permission) => {
          if (!acc[curr.module]) {
            acc[curr.module] = []
          }
          acc[curr.module].push(curr)
          return acc
        },
        {}
      )

      setPermissions(groupedPermissions)
    } catch (error) {
      toast({
        variant: 'destructive',
        description: '获取权限列表失败',
      })
    }
  }

  // 组件加载时获取权限列表
  useEffect(() => {
    fetchPermissions()
  }, [])

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    try {
      setLoading(true)
      const response = await fetch(`/api/roles/${role.id}/permissions`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ permissions: selectedPermissions }),
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.error || '更新权限失败')
      }

      toast({
        description: '更新权限成功',
      })
      onSuccess()
      onOpenChange(false)
    } catch (error: any) {
      toast({
        variant: 'destructive',
        description: error.message || '更新权限失败',
      })
    } finally {
      setLoading(false)
    }
  }

  const togglePermission = (permissionId: string) => {
    setSelectedPermissions(prev =>
      prev.includes(permissionId) ? prev.filter(id => id !== permissionId) : [...prev, permissionId]
    )
  }

  const toggleModule = (modulePermissions: Permission[]) => {
    const modulePermissionIds = modulePermissions.map(p => p.id)
    const allSelected = modulePermissionIds.every(id => selectedPermissions.includes(id))

    if (allSelected) {
      // 如果模块内所有权限都已选中，则取消全部
      setSelectedPermissions(prev => prev.filter(id => !modulePermissionIds.includes(id)))
    } else {
      // 否则选中模块内所有权限
      setSelectedPermissions(prev => [
        ...prev,
        ...modulePermissionIds.filter(id => !prev.includes(id)),
      ])
    }
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[600px]">
        <form onSubmit={handleSubmit}>
          <DialogHeader>
            <DialogTitle>配置角色权限</DialogTitle>
            <DialogDescription>为角色 "{role.name}" 配置权限</DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4 max-h-[400px] overflow-y-auto">
            {Object.entries(permissions).map(([module, modulePermissions]) => (
              <Card key={module}>
                <CardHeader className="pb-2">
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      checked={modulePermissions.every(p => selectedPermissions.includes(p.id))}
                      onClick={() => toggleModule(modulePermissions)}
                    />
                    <CardTitle className="text-base capitalize">{module}</CardTitle>
                  </div>
                </CardHeader>
                <CardContent className="grid gap-3">
                  {modulePermissions.map(permission => (
                    <div key={permission.id} className="flex items-center space-x-2">
                      <Checkbox
                        id={permission.id}
                        checked={selectedPermissions.includes(permission.id)}
                        onClick={() => togglePermission(permission.id)}
                      />
                      <div className="grid gap-1.5">
                        <label
                          htmlFor={permission.id}
                          className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                        >
                          {permission.name}
                        </label>
                        <p className="text-sm text-muted-foreground">{permission.description}</p>
                      </div>
                    </div>
                  ))}
                </CardContent>
              </Card>
            ))}
          </div>
          <DialogFooter>
            <Button type="button" variant="outline" onClick={() => onOpenChange(false)}>
              取消
            </Button>
            <Button type="submit" disabled={loading}>
              {loading ? '提交中...' : '保存'}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  )
}
