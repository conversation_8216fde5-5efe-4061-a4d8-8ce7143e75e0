'use client'

import { useState, useEffect } from 'react'
import { useAppContext } from '@/contexts/AppContext'

const models = [
  { id: 'model1', name: '基础表情识别模型' },
  { id: 'model2', name: '高级表情识别模型' },
  { id: 'model3', name: '多人表情识别模型' },
]

export default function ModelSelector() {
  const { selectedModel, setSelectedModel } = useAppContext()
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    // 模拟从服务器加载模型列表
    setTimeout(() => {
      setIsLoading(false)
      if (!selectedModel) {
        setSelectedModel(models[0].id)
      }
    }, 1000)
  }, [])

  const handleModelChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    setSelectedModel(e.target.value)
  }

  if (isLoading) {
    return <div className="text-center">加载模型列表中...</div>
  }

  return (
    <div className="mb-4">
      <label htmlFor="model-select" className="block text-sm font-medium text-gray-700 mb-2">
        选择表情识别模型
      </label>
      <select
        id="model-select"
        value={selectedModel}
        onChange={handleModelChange}
        className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md"
      >
        {models.map((model) => (
          <option key={model.id} value={model.id}>
            {model.name}
          </option>
        ))}
      </select>
    </div>
  )
}

