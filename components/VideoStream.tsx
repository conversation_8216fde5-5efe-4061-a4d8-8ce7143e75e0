'use client'

import { useRef, useEffect } from 'react'
import { useAppContext } from '@/contexts/AppContext'

export default function VideoStream() {
  const videoRef = useRef<HTMLVideoElement>(null)
  const { selectedDevice } = useAppContext()

  useEffect(() => {
    let stream: MediaStream | null = null

    async function startVideo() {
      if (videoRef.current && selectedDevice) {
        try {
          stream = await navigator.mediaDevices.getUserMedia({
            video: { deviceId: selectedDevice }
          })
          videoRef.current.srcObject = stream
        } catch (err) {
          console.error("Error accessing the camera:", err)
        }
      }
    }

    startVideo()

    return () => {
      if (stream) {
        stream.getTracks().forEach(track => track.stop())
      }
    }
  }, [selectedDevice])

  return (
    <div className="relative aspect-video">
      <video
        ref={videoRef}
        autoPlay
        playsInline
        muted
        className="w-full h-full object-cover rounded-lg"
      />
    </div>
  )
}

