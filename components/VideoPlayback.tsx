'use client'

import { useState, useEffect } from 'react'
import { getVideoById, HistoryVideo } from '@/lib/services/historyService'
import { recognizeEmotion } from '@/lib/services/emotionRecognition'

export default function VideoPlayback({ videoId }: { videoId: string }) {
  const [video, setVideo] = useState<HistoryVideo | null>(null)
  const [emotion, setEmotion] = useState<string | null>(null)
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    async function loadVideo() {
      const videoData = await getVideoById(videoId)
      if (videoData) {
        setVideo(videoData)
      }
      setIsLoading(false)
    }
    loadVideo()
  }, [videoId])

  useEffect(() => {
    let intervalId: NodeJS.Timeout

    async function updateEmotion() {
      const recognizedEmotion = await recognizeEmotion()
      setEmotion(recognizedEmotion)
    }

    if (video) {
      updateEmotion()
      intervalId = setInterval(updateEmotion, 2000)
    }

    return () => {
      if (intervalId) {
        clearInterval(intervalId)
      }
    }
  }, [video])

  if (isLoading) {
    return <div className="text-center">加载视频...</div>
  }

  if (!video) {
    return <div className="text-center">未找到视频</div>
  }

  return (
    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
      <div className="bg-white p-6 rounded-lg shadow-md">
        <h2 className="text-2xl font-bold mb-4">{video.title}</h2>
        <div className="aspect-video bg-gray-200 rounded-lg flex items-center justify-center">
          <p className="text-gray-500">视频播放区域</p>
        </div>
        <div className="mt-4">
          <p className="text-gray-600">日期：{video.date}</p>
          <p className="text-gray-600">时长：{video.duration}</p>
        </div>
      </div>
      <div className="bg-white p-6 rounded-lg shadow-md">
        <h2 className="text-2xl font-bold mb-4">表情识别结果</h2>
        {emotion ? (
          <div className="text-4xl font-bold text-center p-4 bg-gray-100 rounded-lg">
            {emotion}
          </div>
        ) : (
          <div className="text-center">正在分析...</div>
        )}
      </div>
    </div>
  )
}

