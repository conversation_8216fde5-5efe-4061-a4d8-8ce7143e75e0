'use client'

import Navigation from '@/components/Navigation'
import Sidebar from '@/components/Sidebar'
import { usePathname, useRouter } from 'next/navigation'
import { useAppContext } from '@/contexts/AppContext'
import { useEffect } from 'react'

export default function ClientLayout({
  children,
}: {
  children: React.ReactNode
}) {
  const pathname = usePathname()
  const { isLoggedIn } = useAppContext()
  const router = useRouter()
  const isLoginPage = pathname === '/login'
  const isRegisterPage = pathname === '/register'
  const isPublicPage = isLoginPage || isRegisterPage

  useEffect(() => {
    if (!isLoggedIn && !isPublicPage) {
      router.push('/login')
    }
  }, [isLoggedIn, isPublicPage, router])

  if (isPublicPage) {
    return <>{children}</>
  }

  return (
    <div className="flex flex-col h-screen bg-gray-50">
      <Navigation />
      <div className="flex flex-1 overflow-hidden">
        <Sidebar />
        <main className="flex-1 overflow-y-auto bg-white">
          {children}
        </main>
      </div>
    </div>
  )
} 