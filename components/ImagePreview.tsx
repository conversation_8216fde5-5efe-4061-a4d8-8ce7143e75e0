import { cn } from '@/lib/utils'

interface ImagePreviewProps {
  itemId: string
  alt: string
  className?: string
}

export default function ImagePreview({ itemId, alt, className }: ImagePreviewProps) {
  const src = `/me-detection/data-item/image/${itemId}?imageType=thumbnail`
  return (
    <div className={cn('w-16 h-16 bg-gray-100 rounded overflow-hidden', className)}>
      {itemId && <img src={src} alt={alt} className="w-full h-full object-cover" />}
    </div>
  )
}
