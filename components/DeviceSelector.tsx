'use client'

import { useState, useEffect } from 'react'
import { useAppContext } from '@/contexts/AppContext'

interface MediaDeviceInfo {
  deviceId: string
  label: string
}

export default function DeviceSelector() {
  const { selectedDevice, setSelectedDevice } = useAppContext()
  const [devices, setDevices] = useState<MediaDeviceInfo[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    async function getDevices() {
      try {
        setIsLoading(true)
        setError(null)

        // 请求用户权限
        await navigator.mediaDevices.getUserMedia({ video: true })

        const deviceInfos = await navigator.mediaDevices.enumerateDevices()
        const videoDevices = deviceInfos
          .filter(device => device.kind === 'videoinput')
          .map(device => ({ deviceId: device.deviceId, label: device.label }))

        setDevices(videoDevices)

        if (videoDevices.length > 0 && !selectedDevice) {
          setSelectedDevice(videoDevices[0].deviceId)
        }
      } catch (err) {
        console.error('Error accessing media devices:', err)
        setError('无法访问摄像头设备。请确保您已授予必要的权限。')
      } finally {
        setIsLoading(false)
      }
    }

    getDevices()
  }, [])

  const handleDeviceChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    setSelectedDevice(e.target.value)
  }

  if (isLoading) {
    return <div className="text-center">正在加载可用设备...</div>
  }

  if (error) {
    return <div className="text-center text-red-500">{error}</div>
  }

  if (devices.length === 0) {
    return <div className="text-center">未找到可用的摄像头设备</div>
  }

  return (
    <div className="mb-4">
      <label htmlFor="device-select" className="block text-sm font-medium text-gray-700 mb-2">
        选择摄像头设备
      </label>
      <select
        id="device-select"
        value={selectedDevice}
        onChange={handleDeviceChange}
        className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md"
      >
        {devices.map((device) => (
          <option key={device.deviceId} value={device.deviceId}>
            {device.label || `摄像头 ${device.deviceId.substr(0, 5)}`}
          </option>
        ))}
      </select>
    </div>
  )
}

