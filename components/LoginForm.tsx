'use client'

import { useCallback, useState } from 'react'
import { useSearchParams } from 'next/navigation'
import { useAuthContext } from '@/contexts/AuthContext'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Eye, EyeOff, Loader2 } from 'lucide-react'
import Captcha from '@/components/ui/captcha'
import { toast } from '@/components/ui/use-toast'

export default function LoginForm() {
  const [username, setUsername] = useState('greema')
  const [password, setPassword] = useState('greema123')
  const [captcha, setCaptcha] = useState('')
  const [isLoading, setIsLoading] = useState(false)
  const [showPassword, setShowPassword] = useState(false)
  const { login } = useAuthContext()
  const searchParams = useSearchParams()

  const handleSubmit = useCallback(
    async (e: React.FormEvent) => {
      e.preventDefault()
      // 用户名验证
      if (!username.trim()) {
        console.log('username trim')
        toast({
          variant: 'destructive',
          description: '请输入用户名',
        })
        return
      }

      // 密码验证
      if (!password.trim()) {
        console.log('password trim')
        toast({
          variant: 'destructive',
          description: '请输入密码',
        })
        return
      }

      // 验证码验证
      if (!captcha.trim()) {
        console.log('captcha trim')
        toast({
          variant: 'destructive',
          description: '请输入验证码',
        })
        return
      }

      setIsLoading(true)

      try {
        // 这里需要根据你的登录接口调整，可能需要将验证码作为参数传递
        await login({
          userId: username,
          password,
          validateCode: captcha,
          redirectUrl: searchParams?.get('redirect') || '/',
        })
      } catch (err: any) {
        console.error('Login error:', err)
        toast({
          variant: 'destructive',
          description: err.message,
        })
        setCaptcha('') // 清空验证码输入
      } finally {
        setIsLoading(false)
      }
    },
    [username, password, captcha, login, searchParams]
  )

  const handleCaptchaRefresh = useCallback(() => {
    setCaptcha('')
  }, [])

  const handleUsernameChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    setUsername(e.target.value)
  }, [])

  const handlePasswordChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    setPassword(e.target.value)
  }, [])

  const handleCaptchaChange = useCallback((value: string) => {
    setCaptcha(value)
  }, [])

  const togglePasswordVisibility = useCallback(() => {
    setShowPassword(prev => !prev)
  }, [])

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      <div className="space-y-4">
        <div className="space-y-2">
          <Label
            htmlFor="username"
            className="text-sm font-medium text-slate-700 dark:text-slate-300"
          >
            用户名
          </Label>
          <Input
            type="text"
            id="username"
            value={username}
            onChange={handleUsernameChange}
            className="h-11 bg-white/50 dark:bg-slate-800/50 border-slate-200 dark:border-slate-700 focus:border-blue-500 dark:focus:border-blue-400 focus:ring-blue-500/20 dark:focus:ring-blue-400/20 transition-all duration-200"
            minLength={3}
            disabled={isLoading}
          />
        </div>

        <div className="space-y-2">
          <Label
            htmlFor="password"
            className="text-sm font-medium text-slate-700 dark:text-slate-300"
          >
            密码
          </Label>
          <div className="relative">
            <Input
              type={showPassword ? 'text' : 'password'}
              id="password"
              value={password}
              onChange={handlePasswordChange}
              className="h-11 bg-white/50 dark:bg-slate-800/50 border-slate-200 dark:border-slate-700 focus:border-blue-500 dark:focus:border-blue-400 focus:ring-blue-500/20 dark:focus:ring-blue-400/20 transition-all duration-200 pr-10"
              minLength={6}
              disabled={isLoading}
            />
            <button
              type="button"
              onClick={togglePasswordVisibility}
              className="absolute right-3 top-1/2 transform -translate-y-1/2 text-slate-400 hover:text-slate-600 dark:hover:text-slate-300 transition-colors"
              disabled={isLoading}
            >
              {showPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
            </button>
          </div>
        </div>

        <Captcha
          value={captcha}
          onChange={handleCaptchaChange}
          onRefresh={handleCaptchaRefresh}
          isLoading={isLoading}
        />
      </div>

      <Button
        type="submit"
        className="w-full h-11 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white font-medium shadow-lg hover:shadow-xl transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
        disabled={isLoading}
      >
        {isLoading ? (
          <>
            <Loader2 className="mr-2 h-4 w-4 animate-spin" />
            登录中...
          </>
        ) : (
          '登录'
        )}
      </Button>

      <div className="text-center">
        <p className="text-xs text-slate-500 dark:text-slate-400">
          登录即表示您同意我们的服务条款和隐私政策
        </p>
      </div>
    </form>
  )
}
