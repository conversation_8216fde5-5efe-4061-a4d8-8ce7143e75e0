'use client'

import * as React from 'react'
import { Button } from '@/components/ui/button'
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
} from '@/components/ui/command'
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover'
import { cn } from '@/lib/utils'
import { X } from 'lucide-react'
import { materialTagService } from '@/services/materialTagService'

export interface ComboboxOption {
  value: string
  label: string
}

interface ComboboxProps {
  options?: ComboboxOption[]
  value?: string
  onValueChange: (value: string) => void
  placeholder?: string
  searchPlaceholder?: string
  emptyText?: string
  disabled?: boolean
  className?: string
  loading?: boolean
  fetchUrl?: string
  onLoadError?: (error: Error) => void
}

export function Combobox({
  options: initialOptions = [],
  value,
  onValueChange,
  placeholder = '请选择',
  searchPlaceholder = '搜索...',
  emptyText = '未找到结果',
  disabled = false,
  className,
  loading: externalLoading = false,
  fetchUrl,
  onLoadError,
}: ComboboxProps) {
  const [open, setOpen] = React.useState(false)
  const [options, setOptions] = React.useState<ComboboxOption[]>(initialOptions)
  const [internalLoading, setInternalLoading] = React.useState(false)
  const loading = externalLoading || internalLoading

  React.useEffect(() => {
    if (!fetchUrl) {
      setOptions(initialOptions)
      return
    }

    const fetchOptions = async () => {
      setInternalLoading(true)
      try {
        const records = await materialTagService.listTags()
        const formattedOptions = Object.entries(records).map(([k, v]) => {
          return { value: k, label: v }
        })
        setOptions(formattedOptions)
      } catch (error) {
        console.error('Failed to fetch options:', error)
        if (error instanceof Error && onLoadError) {
          onLoadError(error)
        }
      } finally {
        setInternalLoading(false)
      }
    }

    fetchOptions()
  }, [])

  const selectedOption = options.find(option => option.value === value)

  return (
    <div className="relative">
      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger asChild>
          <Button
            variant="outline"
            role="combobox"
            aria-expanded={open}
            className={cn('w-full justify-between', !value && 'text-muted-foreground', className)}
            disabled={disabled}
          >
            <span className="flex-1 text-left truncate">
              {selectedOption ? selectedOption.label : placeholder}
            </span>
          </Button>
        </PopoverTrigger>
        <PopoverContent
          className={cn('p-0', className)}
          style={{ width: 'var(--radix-popover-trigger-width)' }}
        >
          <Command className="w-full">
            <CommandInput placeholder={searchPlaceholder} />
            <CommandEmpty>{emptyText}</CommandEmpty>
            <CommandGroup>
              {options.map(option => (
                <CommandItem
                  key={option.value}
                  value={option.value}
                  onSelect={() => {
                    onValueChange(option.value)
                    setOpen(false)
                  }}
                  keywords={[option.value, option.label]}
                >
                  {option.label}
                </CommandItem>
              ))}
            </CommandGroup>
          </Command>
        </PopoverContent>
      </Popover>
      {value && (
        <Button
          variant="ghost"
          size="sm"
          className="absolute right-1 top-1/2 -translate-y-1/2 h-6 w-6 p-0 hover:bg-transparent"
          onClick={e => {
            e.stopPropagation()
            onValueChange('')
          }}
        >
          <X className="h-4 w-4" />
        </Button>
      )}
    </div>
  )
}
