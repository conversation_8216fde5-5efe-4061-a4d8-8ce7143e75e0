'use client'

import { useState, useEffect, useCallback, memo } from 'react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { RefreshCw, Image } from 'lucide-react'

interface CaptchaProps {
  value: string
  onChange: (value: string) => void
  onRefresh: () => void
  isLoading?: boolean
}

export default function Captcha({ value, onChange, onRefresh, isLoading = false }: CaptchaProps) {
  const [captchaUrl, setCaptchaUrl] = useState('')
  const [isRefreshing, setIsRefreshing] = useState(false)
  const [imageError, setImageError] = useState(false)

  const refreshCaptcha = useCallback(async () => {
    setIsRefreshing(true)
    setImageError(false)
    try {
      // 添加时间戳防止缓存
      const timestamp = Date.now()
      const url = `/me-detection/authorization/auth/validateImage?t=${timestamp}`
      setCaptchaUrl(url)
      // 使用 setTimeout 避免同步调用导致的闪动
      setTimeout(() => {
        onRefresh()
      }, 0)
    } catch (error) {
      console.error('Failed to refresh captcha:', error)
      setImageError(true)
    } finally {
      setIsRefreshing(false)
    }
  }, [onRefresh])

  useEffect(() => {
    refreshCaptcha()
  }, [refreshCaptcha])

  const handleImageError = useCallback(() => {
    setImageError(true)
  }, [])

  const handleImageLoad = useCallback(() => {
    setImageError(false)
  }, [])

  const handleInputChange = useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      onChange(e.target.value)
    },
    [onChange]
  )

  return (
    <div className="space-y-2">
      <Label htmlFor="captcha" className="text-sm font-medium text-slate-700 dark:text-slate-300">
        验证码
      </Label>
      <div className="flex space-x-2">
        <Input
          type="text"
          id="captcha"
          value={value}
          onChange={handleInputChange}
          className="flex-1 h-11 bg-white/50 dark:bg-slate-800/50 border-slate-200 dark:border-slate-700 focus:border-blue-500 dark:focus:border-blue-400 focus:ring-blue-500/20 dark:focus:ring-blue-400/20 transition-all duration-200"
          placeholder="请输入验证码"
          disabled={isLoading}
          autoComplete="off"
          maxLength={6}
        />
        <div className="relative">
          {captchaUrl && !imageError ? (
            <img
              src={captchaUrl}
              alt="验证码"
              className="h-11 w-24 border border-slate-200 dark:border-slate-700 rounded-md cursor-pointer hover:opacity-80 transition-opacity"
              onClick={refreshCaptcha}
              onError={handleImageError}
              onLoad={handleImageLoad}
              title="点击刷新验证码"
            />
          ) : (
            <div className="h-11 w-24 border border-slate-200 dark:border-slate-700 rounded-md flex items-center justify-center bg-slate-50 dark:bg-slate-800">
              <Image className="h-4 w-4 text-slate-400" />
            </div>
          )}
          <Button
            type="button"
            variant="outline"
            size="sm"
            onClick={refreshCaptcha}
            disabled={isRefreshing || isLoading}
            className="absolute -top-3 -right-3 h-6 w-6 p-0 bg-white dark:bg-slate-800 border border-slate-200 dark:border-slate-700 hover:bg-slate-50 dark:hover:bg-slate-700"
          >
            <RefreshCw className={`h-2 w-2 ${isRefreshing ? 'animate-spin' : ''}`} />
          </Button>
        </div>
      </div>
    </div>
  )
}
