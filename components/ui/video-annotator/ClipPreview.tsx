'use client';

import { useEffect, useRef, useState } from 'react';
import { Button } from '@/components/ui/button';
import { Slider } from '@/components/ui/slider';
import { ChevronLeft, ChevronRight, Play, Pause, RefreshCw, TimerOff } from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from '@/components/ui/dropdown-menu';

interface ClipPreviewProps {
  src: string;
  startTime: number;
  endTime: number;
  frameRate: number;
  onStartFrameChange?: (frame: number) => void;
  onEndFrameChange?: (frame: number) => void;
}

export function ClipPreview({
  src,
  startTime,
  endTime,
  frameRate,
  onStartFrameChange,
  onEndFrameChange
}: ClipPreviewProps) {
  const videoRef = useRef<HTMLVideoElement>(null);
  const [isPlaying, setIsPlaying] = useState(false);
  const [currentTime, setCurrentTime] = useState(startTime);
  const [isLooping, setIsLooping] = useState(true);
  const [isDragging, setIsDragging] = useState(false);
  const [previewTime, setPreviewTime] = useState(startTime);
  const [tooltipPosition, setTooltipPosition] = useState({ x: 0, y: 0 });
  // 添加播放速度状态
  const [playbackSpeed, setPlaybackSpeed] = useState(1.0);

  // 添加拖动状态
  const [dragMode, setDragMode] = useState<'current' | 'start' | 'end' | null>(null);

  // 内部状态用于预览拖动时的开始和结束位置
  const [previewStartTime, setPreviewStartTime] = useState(startTime);
  const [previewEndTime, setPreviewEndTime] = useState(endTime);

  // 播放速度选项
  const speedOptions = [
    { value: 0.25, label: '0.25x' },
    { value: 0.5, label: '0.5x' },
    { value: 0.75, label: '0.75x' },
    { value: 1.0, label: '1x (正常)' },
    { value: 1.25, label: '1.25x' },
    { value: 1.5, label: '1.5x' },
    { value: 2.0, label: '2x' },
  ];

  // 时间格式化，加入毫秒
  const formatTime = (timeInSeconds: number) => {
    const minutes = Math.floor(timeInSeconds / 60);
    const seconds = Math.floor(timeInSeconds % 60);
    const milliseconds = Math.floor((timeInSeconds % 1) * 1000);
    return `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}.${milliseconds.toString().padStart(3, '0')}`;
  };

  // 帧计算
  const getCurrentFrame = () => Math.floor(currentTime * frameRate);
  const getFrameFromTime = (time: number) => Math.floor(time * frameRate);
  const getTotalFrames = () => Math.floor((endTime - startTime) * frameRate);

  const togglePlayPause = () => {
    if (!videoRef.current) return;

    if (isPlaying) {
      videoRef.current.pause();
    } else {
      if (currentTime >= endTime) {
        videoRef.current.currentTime = startTime;
      }
      videoRef.current.play();
    }
    setIsPlaying(!isPlaying);
  };

  const toggleLooping = () => {
    setIsLooping(!isLooping);
  };

  // 处理播放速度变化
  const handleSpeedChange = (value: string) => {
    const speed = parseFloat(value);
    if (videoRef.current && !isNaN(speed)) {
      videoRef.current.playbackRate = speed;
      setPlaybackSpeed(speed);
    }
  };

  // 逐帧前进或后退
  const seekFrame = (direction: 'prev' | 'next') => {
    if (!videoRef.current) return;

    const frameTime = 1 / frameRate;
    const newTime = currentTime + (direction === 'next' ? frameTime : -frameTime);

    // 确保在片段范围内
    if (newTime >= startTime && newTime <= endTime) {
      videoRef.current.currentTime = newTime;
      setCurrentTime(newTime);
    } else if (isLooping && direction === 'next' && newTime > endTime) {
      // 循环播放时从头开始
      videoRef.current.currentTime = startTime;
      setCurrentTime(startTime);
    }
  };

  // 调整开始帧
  const adjustStartFrame = () => {
    if (!videoRef.current) return;

    onStartFrameChange?.(getCurrentFrame());
  };

  // 调整结束帧
  const adjustEndFrame = () => {
    if (!videoRef.current) return;

    onEndFrameChange?.(getCurrentFrame());
  };

  // 进度条点击事件，用于定位播放位置
  const handleProgressBarClick = (e: React.MouseEvent<HTMLDivElement>) => {
    if (dragMode !== null || !videoRef.current) return;

    const rect = e.currentTarget.getBoundingClientRect();
    const ratio = (e.clientX - rect.left) / rect.width;
    const newTime = startTime + (endTime - startTime) * ratio;

    if (newTime >= startTime && newTime <= endTime) {
      videoRef.current.currentTime = newTime;
      setCurrentTime(newTime);
    }
  };

  // 处理进度条拖动过程中的事件
  const handleStartDragStart = (e: React.MouseEvent<HTMLDivElement>) => {
    e.stopPropagation();
    setDragMode('start');
    if (isPlaying) {
      videoRef.current?.pause();
      setIsPlaying(false);
    }

    // 立即触发一次计算确保拖动开始时位置正确
    handleDragMove(e.nativeEvent);

    document.addEventListener('mousemove', handleDragMove);
    document.addEventListener('mouseup', handleDragEnd);
  };

  const handleEndDragStart = (e: React.MouseEvent<HTMLDivElement>) => {
    e.stopPropagation();
    setDragMode('end');
    if (isPlaying) {
      videoRef.current?.pause();
      setIsPlaying(false);
    }

    // 立即触发一次计算确保拖动开始时位置正确
    handleDragMove(e.nativeEvent);

    document.addEventListener('mousemove', handleDragMove);
    document.addEventListener('mouseup', handleDragEnd);
  };

  const handleCurrentDragStart = () => {
    setDragMode('current');
    setIsDragging(true);
    if (isPlaying) {
      videoRef.current?.pause();
      setIsPlaying(false);
    }
  };

  const handleDragMove = (e: MouseEvent) => {
    if (!videoRef.current || !dragMode) return;

    // 获取进度条元素位置信息 - 修复选择器
    const progressBar = document.querySelector('.progress-bar-container');
    if (!progressBar) return;

    const rect = progressBar.getBoundingClientRect();
    const ratio = Math.min(Math.max((e.clientX - rect.left) / rect.width, 0), 1);

    // 修改计算区间时间的方式，现在使用起止时间之间的比例
    const newTime = startTime + (endTime - startTime) * ratio;

    if (dragMode === 'start') {
      // 确保开始时间小于结束时间（留有最小间隔）
      const minEndTime = Math.min(endTime, newTime + 0.1);
      if (newTime < minEndTime - 0.1) {
        setPreviewStartTime(newTime);
        setPreviewTime(newTime);

        // 实时更新视频位置以显示当前帧
        if (videoRef.current) {
          videoRef.current.currentTime = newTime;
          setCurrentTime(newTime);
        }
      }
    } else if (dragMode === 'end') {
      // 确保结束时间大于开始时间（留有最小间隔）
      const maxStartTime = Math.max(startTime, newTime - 0.1);
      if (newTime > maxStartTime + 0.1) {
        setPreviewEndTime(newTime);
        setPreviewTime(newTime);

        // 实时更新视频位置以显示当前帧
        if (videoRef.current) {
          videoRef.current.currentTime = newTime;
          setCurrentTime(newTime);
        }
      }
    }
  };

  const handleDragEnd = () => {
    if (!dragMode) return;

    if (dragMode === 'start') {
      onStartFrameChange?.(getFrameFromTime(previewStartTime));
    } else if (dragMode === 'end') {
      onEndFrameChange?.(getFrameFromTime(previewEndTime));
    } else if (dragMode === 'current') {
      setIsDragging(false);
    }

    setDragMode(null);
    document.removeEventListener('mousemove', handleDragMove);
    document.removeEventListener('mouseup', handleDragEnd);
  };

  // 处理鼠标在进度条上移动时的预览
  const handleSliderMouseMove = (e: React.MouseEvent<HTMLDivElement>) => {
    if (!videoRef.current) return;

    // 获取进度条元素位置信息
    const sliderRect = e.currentTarget.getBoundingClientRect();
    // 计算鼠标位置相对于进度条的比例
    const ratio = Math.min(Math.max((e.clientX - sliderRect.left) / sliderRect.width, 0), 1);
    // 转换为视频时间
    const time = startTime + (endTime - startTime) * ratio;

    setPreviewTime(time);
    setTooltipPosition({
      x: e.clientX,
      y: sliderRect.top - 35, // 定位在进度条上方
    });
  };

  useEffect(() => {
    const video = videoRef.current;
    if (!video) return;

    // 设置初始位置为起始时间
    video.currentTime = startTime;
    setCurrentTime(startTime);
    // 设置播放速度
    video.playbackRate = playbackSpeed;

    const handleTimeUpdate = () => {
      if (!dragMode) {
        setCurrentTime(video.currentTime);
      }

      // 如果超出范围且在播放中，则处理
      if (video.currentTime >= endTime && isPlaying) {
        if (isLooping) {
          // 循环播放
          video.currentTime = startTime;
          setCurrentTime(startTime);
        } else {
          // 停止播放
          video.pause();
          setIsPlaying(false);
        }
      }
    };

    video.addEventListener('timeupdate', handleTimeUpdate);

    return () => {
      video.removeEventListener('timeupdate', handleTimeUpdate);
      video.pause();
    };
  }, [startTime, endTime, isLooping, dragMode, playbackSpeed]);

  // 更新片段区间时重新定位
  useEffect(() => {
    setPreviewStartTime(startTime);
    setPreviewEndTime(endTime);

    const video = videoRef.current;
    if (!video) return;

    // 如果当前时间超出新范围，则重置
    if (currentTime < startTime || currentTime > endTime) {
      video.currentTime = startTime;
      setCurrentTime(startTime);
    }
  }, [startTime, endTime]);

  // 计算播放进度百分比
  const playbackPercent = ((currentTime - startTime) / (endTime - startTime)) * 100;
  const previewStartPercent = (dragMode === 'start' ?
    ((previewStartTime - startTime) / (endTime - startTime)) * 100 : 0);
  const previewEndPercent = (dragMode === 'end' ?
    ((previewEndTime - startTime) / (endTime - startTime)) * 100 : 100);

  return (
    <div className="flex flex-col">
      <div className="relative aspect-video">
        <video
          ref={videoRef}
          src={src}
          className="w-full h-full object-contain"
        />
        <div className="absolute bottom-4 left-1/2 -translate-x-1/2 flex items-center gap-2">
          <Button
            variant="ghost"
            size="icon"
            onClick={() => seekFrame('prev')}
            disabled={isPlaying}
            className="h-8 w-8 rounded-full bg-black/50 text-white hover:bg-black/70"
            title="上一帧"
          >
            <ChevronLeft className="h-4 w-4" />
          </Button>
          <Button
            variant="ghost"
            size="icon"
            onClick={togglePlayPause}
            className="h-8 w-8 rounded-full bg-black/50 text-white hover:bg-black/70"
            title="播放/暂停"
          >
            {isPlaying ? (
              <Pause className="h-4 w-4" />
            ) : (
              <Play className="h-4 w-4" />
            )}
          </Button>
          <Button
            variant="ghost"
            size="icon"
            onClick={() => seekFrame('next')}
            disabled={isPlaying}
            className="h-8 w-8 rounded-full bg-black/50 text-white hover:bg-black/70"
            title="下一帧"
          >
            <ChevronRight className="h-4 w-4" />
          </Button>
          <Button
            variant="ghost"
            size="icon"
            onClick={toggleLooping}
            className={`h-8 w-8 rounded-full ${isLooping ? 'bg-white/25' : 'bg-black/50'} text-white hover:bg-white/40`}
            title={isLooping ? "停用循环播放" : "启用循环播放"}
          >
            <RefreshCw className="h-4 w-4" />
          </Button>

          {/* 播放速度控制 */}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button
                variant="ghost"
                size="icon"
                className="h-8 w-8 rounded-full bg-black/50 text-white hover:bg-black/70 relative"
                title="播放速度"
              >
                <TimerOff className="h-4 w-4" />
                <span className="absolute -bottom-1 text-[8px] font-bold">{playbackSpeed}x</span>
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="center" className="min-w-[100px]">
              {speedOptions.map((option) => (
                <DropdownMenuItem
                  key={option.value}
                  className={playbackSpeed === option.value ? "bg-muted font-medium" : ""}
                  onClick={() => handleSpeedChange(option.value.toString())}
                >
                  {option.label}
                </DropdownMenuItem>
              ))}
            </DropdownMenuContent>
          </DropdownMenu>
        </div>

        {/* 帧位置指示器，添加播放速度显示 */}
        <div className="absolute top-2 left-2 text-xs bg-black/70 text-white px-2 py-1 rounded flex items-center gap-2">
          <span>帧: {getCurrentFrame() - Math.floor(startTime * frameRate)} / {getTotalFrames()}</span>
          <span className="border-l border-white/30 pl-2">{playbackSpeed}x</span>
        </div>
      </div>

      <div className="p-3 space-y-3 bg-white">
        {/* 进度条 */}
        <div
          className="relative h-8 group progress-bar-container"
          onMouseMove={handleSliderMouseMove}
          onMouseLeave={() => setIsDragging(false)}
          onClick={handleProgressBarClick}
        >
          {/* 进度条背景 */}
          <div className="absolute inset-y-0 left-0 right-0 top-2 bottom-2 bg-gray-200 rounded-full overflow-hidden">
            {/* 已播放进度 */}
            <div
              className="h-full bg-primary rounded-full"
              style={{ width: `${playbackPercent}%` }}
            />
          </div>

          {/* 开始帧控制滑块 - 增加可见性和交互性 */}
          <div
            className="absolute w-3 h-8 -ml-1.5 cursor-ew-resize flex items-center justify-center z-30 hover:scale-125 transition-transform"
            style={{ left: `${dragMode === 'start' ? previewStartPercent : 0}%` }}
            onMouseDown={handleStartDragStart}
            title="拖动调整开始帧位置"
          >
            <div className="w-1 h-6 bg-blue-600 rounded-full"></div>
            <div className="absolute -top-8 left-1/2 -translate-x-1/2 bg-blue-800 text-white text-xs py-1 px-2 rounded whitespace-nowrap opacity-0 group-hover:opacity-100 transition-opacity">
              开始: {formatTime(dragMode === 'start' ? previewStartTime : startTime)}
            </div>
          </div>

          {/* 结束帧控制滑块 - 增加可见性和交互性 */}
          <div
            className="absolute w-3 h-8 -ml-1.5 cursor-ew-resize flex items-center justify-center z-30 hover:scale-125 transition-transform"
            style={{ left: `${dragMode === 'end' ? previewEndPercent : 100}%` }}
            onMouseDown={handleEndDragStart}
            title="拖动调整结束帧位置"
          >
            <div className="w-1 h-6 bg-blue-600 rounded-full"></div>
            <div className="absolute -top-8 left-1/2 -translate-x-1/2 bg-blue-800 text-white text-xs py-1 px-2 rounded whitespace-nowrap opacity-0 group-hover:opacity-100 transition-opacity">
              结束: {formatTime(dragMode === 'end' ? previewEndTime : endTime)}
            </div>
          </div>

          {/* 添加当前选定区间的可视化显示 */}
          <div
            className="absolute top-2 bottom-2 bg-blue-100/30 border-l border-r border-blue-500/40"
            style={{
              left: `${dragMode === 'start' ? previewStartPercent : 0}%`,
              right: `${100 - (dragMode === 'end' ? previewEndPercent : 100)}%`,
              pointerEvents: 'none'
            }}
          />

          {/* 当前位置滑块 */}
          <Slider
            value={[currentTime]}
            min={startTime}
            max={endTime}
            step={1 / frameRate}
            className="absolute inset-0 top-2 bottom-2 z-20"
            onValueChange={([value]) => {
              if (videoRef.current) {
                videoRef.current.currentTime = value;
                setCurrentTime(value);
                setPreviewTime(value);
              }
            }}
            onValueCommit={handleDragEnd}
            onPointerDown={handleCurrentDragStart}
          />

          {/* 时间预览提示 */}
          {(isDragging || dragMode) && (
            <div
              className="absolute z-40 bg-black/80 text-white px-2 py-1 rounded text-xs transform -translate-x-1/2 pointer-events-none"
              style={{
                left: `${dragMode === 'start'
                  ? previewStartPercent
                  : dragMode === 'end'
                    ? previewEndPercent
                    : ((previewTime - startTime) / (endTime - startTime)) * 100}%`,
                bottom: '100%',
                marginBottom: '8px'
              }}
            >
              <div className="font-medium">
                {dragMode === 'start'
                  ? `开始: ${formatTime(previewStartTime)}`
                  : dragMode === 'end'
                    ? `结束: ${formatTime(previewEndTime)}`
                    : formatTime(previewTime)}
              </div>
              <div>
                {dragMode === 'start'
                  ? `帧: ${getFrameFromTime(previewStartTime)}`
                  : dragMode === 'end'
                    ? `帧: ${getFrameFromTime(previewEndTime)}`
                    : `帧: ${getFrameFromTime(previewTime) - Math.floor(startTime * frameRate)}`}
              </div>
              <div className="w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-black/80 absolute left-1/2 top-full transform -translate-x-1/2"></div>
            </div>
          )}
        </div>

        {/* 时间显示 */}
        <div className="flex justify-between text-xs">
          <span>{formatTime(currentTime)}</span>
          <span>区间: {formatTime(startTime)} - {formatTime(endTime)}</span>
        </div>

        {/* 保留原有的按钮，作为辅助控制 */}
        <div className="flex items-center justify-center gap-3 pt-2">
          <Button
            variant="outline"
            size="sm"
            onClick={adjustStartFrame}
            title="将当前位置设为开始帧"
            className="h-7 text-xs"
          >
            设为开始帧
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={adjustEndFrame}
            title="将当前位置设为结束帧"
            className="h-7 text-xs"
          >
            设为结束帧
          </Button>
        </div>
      </div>
    </div>
  );
}
