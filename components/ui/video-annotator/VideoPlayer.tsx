'use client'

import { useEffect, useRef, useState } from 'react'
import { Button } from '@/components/ui/button'
import { Slider } from '@/components/ui/slider'
import {
  ChevronLeft,
  ChevronRight,
  Gauge,
  Maximize,
  Pause,
  Play,
  RefreshCw,
  SkipBack,
  SkipForward,
  Volume2,
} from 'lucide-react'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'

interface VideoPlayerProps {
  src: string
  onFrameChange?: (currentTime: number) => void
  highlightMode?: boolean
  autoPlay?: boolean
  autoPause?: boolean
  onPlayStateChange?: (isPlaying: boolean) => void
  onFrameRateChange?: (frameRate: number) => void
}

// 类型声明，添加到全局Window接口
declare global {
  interface Window {
    __videoPlayerSetFrameRateCallback?: (callback: ((rate: number) => void) | null) => void
  }
}

export function VideoPlayer({
  src,
  onFrameChange,
  highlightMode = false,
  autoPlay = false,
  autoPause = false,
  onPlayStateChange,
  onFrameRateChange,
}: VideoPlayerProps) {
  const videoRef = useRef<HTMLVideoElement>(null)
  const [isPlaying, setIsPlaying] = useState(false)
  const [currentTime, setCurrentTime] = useState(0)
  const [duration, setDuration] = useState(0)
  const [frameRate, setFrameRate] = useState(29.97) // 默认帧率
  const [currentFrame, setCurrentFrame] = useState(0)
  const [totalFrames, setTotalFrames] = useState(0)
  // 添加播放速度状态
  const [playbackSpeed, setPlaybackSpeed] = useState(1.0)
  // 添加音量控制
  const [volume, setVolume] = useState(1.0)
  // 添加循环播放控制
  const [isLooping, setIsLooping] = useState(false)
  // 添加全屏状态
  const [isFullscreen, setIsFullscreen] = useState(false)
  // 添加精确位置控制
  const [precisePosition, setPrecisePosition] = useState(0)

  // 播放速度选项
  const speedOptions = [
    { value: 0.1, label: '0.1x (极慢)' },
    { value: 0.25, label: '0.25x' },
    { value: 0.5, label: '0.5x' },
    { value: 0.75, label: '0.75x' },
    { value: 1.0, label: '1x (正常)' },
    { value: 1.25, label: '1.25x' },
    { value: 1.5, label: '1.5x' },
    { value: 2.0, label: '2x' },
    { value: 4.0, label: '4x (快速)' },
  ]

  // 帧率选项
  const frameRateOptions = [
    { value: '15', label: '15 fps (低帧率)' },
    { value: '23.976', label: '23.976 fps (电影)' },
    { value: '24', label: '24 fps (电影)' },
    { value: '25', label: '25 fps (PAL)' },
    { value: '29.97', label: '29.97 fps (NTSC)' },
    { value: '30', label: '30 fps' },
    { value: '50', label: '50 fps' },
    { value: '59.94', label: '59.94 fps' },
    { value: '60', label: '60 fps' },
    { value: '120', label: '120 fps (高帧率)' },
  ]

  // 时间格式化，加入了毫秒
  const formatTime = (timeInSeconds: number) => {
    const minutes = Math.floor(timeInSeconds / 60)
    const seconds = Math.floor(timeInSeconds % 60)
    const milliseconds = Math.floor((timeInSeconds % 1) * 1000)
    return `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}.${milliseconds.toString().padStart(3, '0')}`
  }

  // 计算当前帧
  useEffect(() => {
    if (duration > 0) {
      const estimatedTotalFrames = Math.floor(duration * frameRate)
      setTotalFrames(estimatedTotalFrames)
      const estimatedCurrentFrame = Math.floor(currentTime * frameRate)
      setCurrentFrame(estimatedCurrentFrame)
    }
  }, [currentTime, duration, frameRate])

  const togglePlayPause = () => {
    if (!videoRef.current) return

    if (isPlaying) {
      videoRef.current.pause()
    } else {
      videoRef.current.play()
    }
    setIsPlaying(!isPlaying)
    onPlayStateChange?.(!isPlaying)
  }

  // 处理播放速度变化
  const handleSpeedChange = (value: string) => {
    const speed = parseFloat(value)
    if (videoRef.current && !isNaN(speed)) {
      videoRef.current.playbackRate = speed
      setPlaybackSpeed(speed)
    }
  }

  // 逐帧前进或后退
  const seekFrame = (direction: 'prev' | 'next') => {
    if (!videoRef.current) return

    const frameTime = 1 / frameRate
    const newTime = currentTime + (direction === 'next' ? frameTime : -frameTime)

    if (newTime >= 0 && newTime <= duration) {
      videoRef.current.currentTime = newTime
      setCurrentTime(newTime)
      onFrameChange?.(newTime)
    }
  }

  // 快进快退5帧
  const seekMultipleFrames = (direction: 'prev' | 'next', frameCount: number = 5) => {
    if (!videoRef.current) return

    const frameTime = frameCount / frameRate
    const newTime = currentTime + (direction === 'next' ? frameTime : -frameTime)

    if (newTime >= 0 && newTime <= duration) {
      videoRef.current.currentTime = newTime
      setCurrentTime(newTime)
      onFrameChange?.(newTime)
    }
  }

  // 直接跳到指定帧
  const goToFrame = (frame: number) => {
    if (!videoRef.current || frame < 0 || frame > totalFrames) return

    const newTime = frame / frameRate
    videoRef.current.currentTime = newTime
    setCurrentTime(newTime)
    onFrameChange?.(newTime)
  }

  // 处理帧率变化
  const handleFrameRateChange = (value: string) => {
    const newFrameRate = parseFloat(value)
    setFrameRate(newFrameRate)
    // 通知父组件帧率已更改
    onFrameRateChange?.(newFrameRate)
    // 重新计算当前帧
    if (duration > 0) {
      setTotalFrames(Math.floor(duration * newFrameRate))
      setCurrentFrame(Math.floor(currentTime * newFrameRate))
    }
  }

  // 处理音量变化
  const handleVolumeChange = (newVolume: number) => {
    setVolume(newVolume)
    if (videoRef.current) {
      videoRef.current.volume = newVolume
    }
  }

  // 切换循环播放
  const toggleLooping = () => {
    setIsLooping(!isLooping)
    if (videoRef.current) {
      videoRef.current.loop = !isLooping
    }
  }

  // 精确定位到指定时间（毫秒级）
  const seekToPreciseTime = (timeInSeconds: number) => {
    if (!videoRef.current) return

    const clampedTime = Math.max(0, Math.min(timeInSeconds, duration))
    videoRef.current.currentTime = clampedTime
    setCurrentTime(clampedTime)
    setPrecisePosition(clampedTime)
    onFrameChange?.(clampedTime)
  }

  // 精确帧跳转（支持小数帧）
  const seekToPreciseFrame = (frame: number) => {
    const timeInSeconds = frame / frameRate
    seekToPreciseTime(timeInSeconds)
  }

  useEffect(() => {
    const video = videoRef.current
    if (!video) return

    const handleTimeUpdate = () => {
      setCurrentTime(video.currentTime)
      onFrameChange?.(video.currentTime)
    }

    const handleLoadedMetadata = () => {
      setDuration(video.duration)
      setTotalFrames(Math.floor(video.duration * frameRate))
      // 设置初始播放速度和音量
      video.playbackRate = playbackSpeed
      video.volume = volume
      video.loop = isLooping
    }

    video.addEventListener('timeupdate', handleTimeUpdate)
    video.addEventListener('loadedmetadata', handleLoadedMetadata)

    return () => {
      video.removeEventListener('timeupdate', handleTimeUpdate)
      video.removeEventListener('loadedmetadata', handleLoadedMetadata)
    }
  }, [onFrameChange, frameRate, playbackSpeed])

  // 键盘事件处理
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      // 避免在输入框中触发快捷键
      if (e.target instanceof HTMLInputElement || e.target instanceof HTMLTextAreaElement) {
        return
      }

      if (e.code === 'Space') {
        e.preventDefault()
        togglePlayPause()
      } else if (e.code === 'ArrowLeft') {
        e.preventDefault()
        if (e.ctrlKey) {
          // Ctrl+Left: 跳转到开始
          seekToPreciseTime(0)
        } else if (e.shiftKey) {
          // Shift+Left: 后退5帧
          seekMultipleFrames('prev')
        } else {
          // Left: 后退1帧
          seekFrame('prev')
        }
      } else if (e.code === 'ArrowRight') {
        e.preventDefault()
        if (e.ctrlKey) {
          // Ctrl+Right: 跳转到结尾
          seekToPreciseTime(duration)
        } else if (e.shiftKey) {
          // Shift+Right: 前进5帧
          seekMultipleFrames('next')
        } else {
          // Right: 前进1帧
          seekFrame('next')
        }
      } else if (e.code === 'KeyL') {
        e.preventDefault()
        toggleLooping()
      } else if (e.code === 'ArrowUp') {
        e.preventDefault()
        // 音量增加
        handleVolumeChange(Math.min(1, volume + 0.1))
      } else if (e.code === 'ArrowDown') {
        e.preventDefault()
        // 音量减少
        handleVolumeChange(Math.max(0, volume - 0.1))
      } else if (e.code === 'KeyM') {
        e.preventDefault()
        // 静音切换
        handleVolumeChange(volume > 0 ? 0 : 1)
      }
    }

    window.addEventListener('keydown', handleKeyDown)
    return () => window.removeEventListener('keydown', handleKeyDown)
  }, [isPlaying, currentTime, duration, frameRate, volume])

  // 监听自动播放/暂停控制
  useEffect(() => {
    if (videoRef.current) {
      if (autoPlay && !isPlaying) {
        videoRef.current
          .play()
          .then(() => {
            setIsPlaying(true)
            onPlayStateChange?.(true)
          })
          .catch(error => {
            console.error('自动播放失败:', error)
          })
      } else if (autoPause && isPlaying) {
        videoRef.current.pause()
        setIsPlaying(false)
        onPlayStateChange?.(false)
      }
    }
  }, [autoPlay, autoPause, isPlaying, onPlayStateChange])

  return (
    <div className="flex flex-col h-full">
      <div className="relative flex-grow bg-black">
        <video ref={videoRef} src={src} className="w-full h-full object-contain" />
        {/* 控制按钮 */}
        <div className="absolute bottom-4 left-1/2 -translate-x-1/2 flex items-center gap-2">
          <Button
            variant="ghost"
            size="icon"
            onClick={() => seekToPreciseTime(0)}
            className="h-10 w-10 rounded-full bg-black/50 text-white hover:bg-black/70"
            title="跳转到开始 (Ctrl+←)"
          >
            <SkipBack className="h-6 w-6" />
          </Button>
          <Button
            variant="ghost"
            size="icon"
            onClick={() => seekFrame('prev')}
            className="h-10 w-10 rounded-full bg-black/50 text-white hover:bg-black/70"
            title="后退1帧 (←)"
          >
            <ChevronLeft className="h-6 w-6" />
          </Button>
          <Button
            variant="ghost"
            size="icon"
            onClick={togglePlayPause}
            className="h-10 w-10 rounded-full bg-black/50 text-white hover:bg-black/70"
            title="播放/暂停 (空格)"
          >
            {isPlaying ? <Pause className="h-6 w-6" /> : <Play className="h-6 w-6" />}
          </Button>
          <Button
            variant="ghost"
            size="icon"
            onClick={() => seekFrame('next')}
            className="h-10 w-10 rounded-full bg-black/50 text-white hover:bg-black/70"
            title="前进1帧 (→)"
          >
            <ChevronRight className="h-6 w-6" />
          </Button>
          <Button
            variant="ghost"
            size="icon"
            onClick={() => seekToPreciseTime(duration)}
            className="h-10 w-10 rounded-full bg-black/50 text-white hover:bg-black/70"
            title="跳转到结尾 (Ctrl+→)"
          >
            <SkipForward className="h-6 w-6" />
          </Button>
          {/* 循环播放控制 */}
          <Button
            variant="ghost"
            size="icon"
            onClick={toggleLooping}
            className={`h-10 w-10 rounded-full ${isLooping ? 'bg-white/25' : 'bg-black/50'} text-white hover:bg-white/40`}
            title={`${isLooping ? '关闭' : '开启'}循环播放 (L)`}
          >
            <RefreshCw className="h-5 w-5" />
          </Button>

          {/* 播放速度控制按钮 */}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button
                variant="ghost"
                size="icon"
                className="h-10 w-10 rounded-full bg-black/50 text-white hover:bg-black/70 relative"
                title="播放速度"
              >
                <Gauge className="h-5 w-5" />
                <span className="absolute -bottom-1 text-[10px] font-bold">{playbackSpeed}x</span>
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent className="min-w-[140px]">
              {speedOptions.map(option => (
                <DropdownMenuItem
                  key={option.value}
                  className={playbackSpeed === option.value ? 'bg-muted font-medium' : ''}
                  onClick={() => handleSpeedChange(option.value.toString())}
                >
                  {option.label}
                </DropdownMenuItem>
              ))}
            </DropdownMenuContent>
          </DropdownMenu>

          {/* 音量控制 */}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button
                variant="ghost"
                size="icon"
                className="h-10 w-10 rounded-full bg-black/50 text-white hover:bg-black/70"
                title="音量控制 (M切换静音, ↑↓调节)"
              >
                <Volume2 className="h-5 w-5" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent className="p-3">
              <div className="space-y-2">
                <div className="text-sm font-medium">音量: {Math.round(volume * 100)}%</div>
                <Slider
                  value={[volume]}
                  min={0}
                  max={1}
                  step={0.1}
                  className="w-32"
                  onValueChange={([value]) => handleVolumeChange(value)}
                />
              </div>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>

      {/* 帧信息和控制区域 */}
      <div className="p-4 space-y-3">
        <div className="flex justify-between items-center">
          <div className="text-sm font-medium flex items-center gap-2">
            帧率:
            <Select value={frameRate.toString()} onValueChange={handleFrameRateChange}>
              <SelectTrigger className="w-36 h-8">
                <SelectValue placeholder="选择帧率" />
              </SelectTrigger>
              <SelectContent>
                {frameRateOptions.map(option => (
                  <SelectItem key={option.value} value={option.value}>
                    {option.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
          <div className="text-sm font-medium flex items-center gap-2">
            速度:
            <Select value={playbackSpeed.toString()} onValueChange={handleSpeedChange}>
              <SelectTrigger className="w-28 h-8">
                <SelectValue placeholder="播放速度" />
              </SelectTrigger>
              <SelectContent>
                {speedOptions.map(option => (
                  <SelectItem key={option.value} value={option.value.toString()}>
                    {option.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <div className="bg-muted px-2 py-1 rounded">
              帧: {currentFrame} / {totalFrames}
            </div>
          </div>
        </div>

        <Slider
          value={[currentTime]}
          min={0}
          max={duration}
          step={1 / frameRate}
          className="h-4"
          onValueChange={([value]) => {
            if (videoRef.current) {
              videoRef.current.currentTime = value
              setCurrentTime(value)
              onFrameChange?.(value)
            }
          }}
        />
        <div className="flex justify-between text-sm">
          <span>{formatTime(currentTime)}</span>
          <span>{formatTime(duration)}</span>
        </div>

        {/* 精确定位控制区域 */}
        <div className="grid grid-cols-2 gap-4 mt-2 pt-2 border-t">
          {/* 帧数精确定位 */}
          <div className="space-y-2">
            <div className="text-sm font-medium">精确帧定位:</div>
            <div className="flex items-center gap-2">
              <input
                type="number"
                min="0"
                max={totalFrames}
                value={currentFrame}
                onChange={e => {
                  const frame = parseFloat(e.target.value)
                  if (!isNaN(frame)) {
                    seekToPreciseFrame(frame)
                  }
                }}
                className="w-20 h-8 px-2 border rounded text-sm"
                placeholder="帧数"
                step="0.1"
              />
              <Button
                variant="outline"
                size="sm"
                onClick={() => seekToPreciseFrame(currentFrame)}
                className="h-8"
              >
                跳转
              </Button>
            </div>
          </div>

          {/* 时间精确定位 */}
          <div className="space-y-2">
            <div className="text-sm font-medium">精确时间定位:</div>
            <div className="flex items-center gap-2">
              <input
                type="number"
                min="0"
                max={duration}
                value={currentTime.toFixed(3)}
                onChange={e => {
                  const time = parseFloat(e.target.value)
                  if (!isNaN(time)) {
                    seekToPreciseTime(time)
                  }
                }}
                className="w-20 h-8 px-2 border rounded text-sm"
                placeholder="秒"
                step="0.001"
              />
              <Button
                variant="outline"
                size="sm"
                onClick={() => seekToPreciseTime(currentTime)}
                className="h-8"
              >
                跳转
              </Button>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

// 导出一个自定义hook用于获取帧率通知
export function useVideoFrameRate(callback: (rate: number) => void) {
  useEffect(() => {
    // 查找VideoPlayer组件并注册回调
    const setFrameRateCallback = window.__videoPlayerSetFrameRateCallback
    if (setFrameRateCallback) {
      setFrameRateCallback(callback)
      // 确保在组件卸载时清理
      return () => setFrameRateCallback(null)
    }
  }, [callback])
}
