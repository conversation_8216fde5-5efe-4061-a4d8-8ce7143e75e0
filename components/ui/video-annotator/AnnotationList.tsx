'use client';

import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { emotionShortcuts } from '@/components/ui/image-annotator/ShortcutHints';
import { formatTime } from '@/lib/utils';
import { Trash2 } from 'lucide-react';
import { Button } from '@/components/ui/button';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { useState } from 'react';

export interface VideoAnnotation {
  id: string;
  startTime: number;
  endTime: number;
  emotion: string;
  previewUrl?: string;
}

interface AnnotationListProps {
  annotations: VideoAnnotation[];
  onEmotionChange: (id: string, emotion: string) => void;
  onDelete?: (id: string) => void;
}

export function AnnotationList({ annotations, onEmotionChange, onDelete }: AnnotationListProps) {
  const [deletingId, setDeletingId] = useState<string | null>(null);

  const handleDeleteClick = (id: string) => {
    setDeletingId(id);
  };

  const handleConfirmDelete = () => {
    if (deletingId && onDelete) {
      onDelete(deletingId);
    }
    setDeletingId(null);
  };

  const handleCancelDelete = () => {
    setDeletingId(null);
  };

  if (!annotations.length) {
    return <div className="p-4 text-center text-muted-foreground">暂无标注内容</div>;
  }

  return (
    <div className="space-y-2 p-4">
      {annotations.map((annotation) => (
        <div
          key={annotation.id}
          className="flex flex-col gap-2 border rounded-md p-3 bg-background shadow-sm"
        >
          <div className="flex items-center justify-between">
            <div className="text-xs font-medium">
              {formatTime(annotation.startTime)} - {formatTime(annotation.endTime)}
            </div>
            <div className="flex items-center gap-2">
              <div className="text-xs text-muted-foreground">
                {(annotation.endTime - annotation.startTime).toFixed(2)}s
              </div>
              {onDelete && (
                <Button
                  variant="ghost"
                  size="icon"
                  className="h-6 w-6 text-destructive hover:bg-destructive/10"
                  onClick={() => handleDeleteClick(annotation.id)}
                >
                  <Trash2 className="h-4 w-4" />
                </Button>
              )}
            </div>
          </div>

          <div className="flex gap-2">
            <Select
              value={annotation.emotion}
              onValueChange={(value) => onEmotionChange(annotation.id, value)}
            >
              <SelectTrigger className="w-full">
                <SelectValue placeholder="选择情绪" />
              </SelectTrigger>
              <SelectContent>
                {Object.values(emotionShortcuts).map((emotion) => (
                  <SelectItem key={emotion} value={emotion}>
                    {emotion}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {annotation.previewUrl && (
            <div
              className="h-16 bg-cover bg-center rounded"
              style={{ backgroundImage: `url(${annotation.previewUrl})` }}
            />
          )}
        </div>
      ))}

      <AlertDialog open={deletingId !== null} onOpenChange={(open) => !open && setDeletingId(null)}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>确认删除</AlertDialogTitle>
            <AlertDialogDescription>
              确定要删除这个标注吗？此操作无法撤消。
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel onClick={handleCancelDelete}>取消</AlertDialogCancel>
            <AlertDialogAction onClick={handleConfirmDelete} className="bg-destructive">
              删除
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
}