'use client'

import { useEffect, useRef, useState } from 'react'
import { format } from 'date-fns'
import { useAutoAnimate } from '@formkit/auto-animate/react'
import { EmotionDetectionResult, EmotionResultsContainerProps } from '@/lib/types/emotion'
import { getEmotionDetectionResult, getMockEmotionHistory } from '@/lib/services/emotionRecognition'
import { cn } from '@/lib/utils'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { ScrollArea } from '@/components/ui/scroll-area'

// 扩展EmotionDetectionResult类型，添加imageId字段
interface ExtendedEmotionResult extends EmotionDetectionResult {
  imageId: number;
}

export function EmotionResultsContainer({ className, isActive = false }: EmotionResultsContainerProps) {
  const [results, setResults] = useState<ExtendedEmotionResult[]>([])
  const [parent] = useAutoAnimate()
  const scrollAreaRef = useRef<HTMLDivElement>(null)
  const intervalRef = useRef<NodeJS.Timeout | null>(null)
  const isEmptyRef = useRef<boolean>(true)  // 使用ref追踪结果列表是否为空

  // 生成随机的图片ID
  const generateImageId = () => Math.floor(Math.random() * 1000)

  // Format time as HH:MM:SS
  function formatTime(date: Date): string {
    return format(date, 'HH:mm:ss')
  }

  // Format duration in seconds with 1 decimal place
  function formatDuration(ms: number): string {
    return (ms / 1000).toFixed(1)
  }

  // Get emotion color based on emotion type
  function getEmotionColor(emotion: string): string {
    // '高兴' | '生气' | '厌恶' | '恐惧' | '悲伤' | '惊讶'
    switch (emotion) {
      case '高兴':
        return 'bg-green-100 text-green-800 border-green-300'
      case '悲伤':
        return 'bg-blue-100 text-blue-800 border-blue-300'
      case '生气':
        return 'bg-red-100 text-red-800 border-red-300'
      case '惊讶':
        return 'bg-amber-100 text-amber-800 border-amber-300'
      case '恐惧':
        return 'bg-purple-100 text-purple-800 border-purple-300'
      case '厌恶':
        return 'bg-pink-100 text-pink-800 border-pink-300'
      case '开始检测':
        return 'bg-blue-100 text-blue-800 border-blue-300'
      case '继续检测':
        return 'bg-indigo-100 text-indigo-800 border-indigo-300'
      default:
        return 'bg-gray-100 text-gray-800 border-gray-300'
    }
  }

  // Get confidence color based on confidence level
  function getConfidenceColor(confidence: number): string {
    if (confidence >= 0.9) return 'text-green-600'
    if (confidence >= 0.8) return 'text-blue-600'
    if (confidence >= 0.7) return 'text-amber-600'
    return 'text-red-600'
  }

  // 初始化结果列表
  useEffect(() => {
    // 初始化为空列表，不预加载历史数据
    setResults([]);
    isEmptyRef.current = true;
  }, [])

  // 更新isEmptyRef值
  useEffect(() => {
    isEmptyRef.current = results.length === 0;
  }, [results.length]);

  // 处理检测状态变化
  useEffect(() => {
    // 清除现有的定时器
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
      intervalRef.current = null;
    }

    // 如果检测活跃，开始生成新结果
    if (isActive) {
      // 不再清空之前的结果
      
      // 仅当结果列表为空时，才添加开始检测的记录
      if (isEmptyRef.current) {
        // 添加一条检测开始的记录
        const startingResult: ExtendedEmotionResult = {
          id: `start-${Date.now()}`,
          emotion: '开始检测',
          confidence: 1,
          startTime: new Date(),
          duration: 0,
          imageId: generateImageId()
        };
        
        setResults([startingResult]);
      } else {
        // 如果已有结果，添加一条"继续检测"的记录
        const continueResult: ExtendedEmotionResult = {
          id: `continue-${Date.now()}`,
          emotion: '继续检测',
          confidence: 1,
          startTime: new Date(),
          duration: 0,
          imageId: generateImageId()
        };
        
        setResults(prev => [continueResult, ...prev]);
      }

      // 设置定时器定期生成新结果
      intervalRef.current = setInterval(async () => {
        const newResult = await getEmotionDetectionResult()
        // 为新结果添加随机的imageId
        const resultWithImageId = {
          ...newResult,
          imageId: generateImageId()
        }
        setResults(prevResults => [resultWithImageId, ...prevResults].slice(0, 50)) // Keep latest 50 results
      }, 1500); // 更快地生成结果
    }
    // 停止检测时不执行任何操作，保留现有结果

    // 组件卸载时清除定时器
    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, [isActive]);

  // Scroll to top when new results are added
  useEffect(() => {
    if (scrollAreaRef.current) {
      scrollAreaRef.current.scrollTop = 0
    }
  }, [results.length])

  return (
    <Card className={cn('w-full h-full', className)}>
      <CardHeader className="pb-3">
        <div className="flex justify-between items-center">
          <div>
            <CardTitle>表情检测结果流</CardTitle>
            <CardDescription>实时检测的表情结果</CardDescription>
          </div>
          {isActive && (
            <Badge className="bg-green-600 animate-pulse">检测中</Badge>
          )}
        </div>
      </CardHeader>
      <CardContent>
        {results.length === 0 ? (
          <div className="flex flex-col items-center justify-center h-[calc(100vh-20rem)] text-gray-400">
            <p className="text-center">点击左侧"开始检测"按钮以启动表情识别</p>
          </div>
        ) : (
          <ScrollArea className="h-[calc(100vh-13rem)]" ref={scrollAreaRef}>
            <div ref={parent} className="space-y-2">
              {!isActive && (
                <div className="bg-gray-50 border rounded-md p-3 mb-3 text-center text-gray-500">
                  检测已暂停，点击"开始检测"继续
                </div>
              )}
              {results.map(result => (
                <div className='flex gap-2 items-center' key={result.id}>
                  <div>
                    <img
                      src={`https://picsum.photos/seed/${result.imageId}/200/200`}
                      alt="Emotion Thumbnail"
                      className="w-16 h-16 object-cover rounded-md"
                    />
                  </div>
                  <div
                    className="flex-1 p-3 border rounded-md flex flex-col gap-1 transition-all duration-200 hover:bg-slate-50"
                  >
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-gray-500">{formatTime(result.startTime)}</span>
                      <Badge variant="outline" className={getEmotionColor(result.emotion)}>
                        {result.emotion}
                      </Badge>
                    </div>

                    <div className="flex justify-between items-center mt-1">
                      <div className="flex items-center gap-2">
                        <span className="text-xs text-gray-500">持续时间:</span>
                        <span className="text-sm font-medium">{formatDuration(result.duration)}s</span>
                      </div>

                      <div className="flex items-center gap-2">
                        <span className="text-xs text-gray-500">置信度:</span>
                        <span className={`text-sm font-bold ${getConfidenceColor(result.confidence)}`}>
                          {(result.confidence * 100).toFixed(0)}%
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </ScrollArea>
        )}
      </CardContent>
    </Card>
  )
}
