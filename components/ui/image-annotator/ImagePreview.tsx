'use client'

import { useEffect, useState } from 'react'
import { Button } from '@/components/ui/button'
import { ChevronLeft, ChevronRight, ZoomIn, ZoomOut, RotateCw } from 'lucide-react'
import { cn } from '@/lib/utils'
import { Skeleton } from '@/components/ui/skeleton'

interface ImagePreviewProps {
  images: string[]
  currentIndex: number
  onImageChange: (index: number) => void
  className?: string
}

export function ImagePreview({
  images,
  currentIndex,
  onImageChange,
  className,
}: ImagePreviewProps) {
  const [loading, setLoading] = useState(true)
  const [zoomLevel, setZoomLevel] = useState(1)
  const [rotation, setRotation] = useState(0)

  // 重置缩放和旋转当图片改变时
  useEffect(() => {
    setZoomLevel(1)
    setRotation(0)
    setLoading(true)
  }, [currentIndex])

  const handlePrevious = () => {
    if (currentIndex > 0) {
      onImageChange(currentIndex - 1)
    }
  }

  const handleNext = () => {
    if (currentIndex < images.length - 1) {
      onImageChange(currentIndex + 1)
    }
  }

  const handleZoomIn = () => {
    setZoomLevel(prev => Math.min(prev + 0.25, 3))
  }

  const handleZoomOut = () => {
    setZoomLevel(prev => Math.max(prev - 0.25, 0.5))
  }

  const handleRotate = () => {
    setRotation(prev => (prev + 90) % 360)
  }

  const handleImageLoad = () => {
    setLoading(false)
  }

  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === 'ArrowLeft') {
        handlePrevious()
      } else if (e.key === 'ArrowRight') {
        handleNext()
      } else if (e.key === '+' || e.key === '=') {
        handleZoomIn()
      } else if (e.key === '-') {
        handleZoomOut()
      } else if (e.key === 'r') {
        handleRotate()
      }
    }

    window.addEventListener('keydown', handleKeyDown)
    return () => window.removeEventListener('keydown', handleKeyDown)
  }, [currentIndex])

  return (
    <div className={cn('relative h-full w-full bg-black/5 dark:bg-white/5', className)}>
      {/* 图片容器 */}
      <div className="absolute inset-0 flex items-center justify-center overflow-hidden">
        {loading && <Skeleton className="absolute inset-0 z-0" />}
        <img
          src={images[currentIndex] || 'https://picsum.photos/seed/${currentIndex}/200/200'}
          alt={`图片 ${currentIndex + 1}`}
          className={cn(
            'max-h-full max-w-full object-contain transition-all duration-200',
            loading ? 'opacity-0' : 'opacity-100'
          )}
          style={{
            transform: `scale(${zoomLevel}) rotate(${rotation}deg)`,
            transition: 'transform 0.3s ease',
          }}
          onLoad={handleImageLoad}
        />
      </div>

      {/* 导航按钮 */}
      <div className="absolute inset-y-0 left-4 flex items-center">
        <Button
          variant="ghost"
          size="icon"
          onClick={handlePrevious}
          disabled={currentIndex === 0}
          className="h-12 w-12 rounded-full bg-black/50 text-white hover:bg-black/70 transition-colors"
        >
          <ChevronLeft className="h-8 w-8" />
        </Button>
      </div>

      <div className="absolute inset-y-0 right-4 flex items-center">
        <Button
          variant="ghost"
          size="icon"
          onClick={handleNext}
          disabled={currentIndex === images.length - 1}
          className="h-12 w-12 rounded-full bg-black/50 text-white hover:bg-black/70 transition-colors"
        >
          <ChevronRight className="h-8 w-8" />
        </Button>
      </div>

      {/* 工具栏 */}
      <div className="absolute bottom-4 left-1/2 -translate-x-1/2 flex items-center gap-2 bg-black/50 rounded-full p-2 backdrop-blur-sm">
        <Button
          variant="ghost"
          size="icon"
          onClick={handleZoomOut}
          disabled={zoomLevel <= 0.5}
          className="h-8 w-8 rounded-full text-white hover:bg-white/20"
        >
          <ZoomOut className="h-4 w-4" />
        </Button>
        <div className="text-xs text-white px-2">{Math.round(zoomLevel * 100)}%</div>
        <Button
          variant="ghost"
          size="icon"
          onClick={handleZoomIn}
          disabled={zoomLevel >= 3}
          className="h-8 w-8 rounded-full text-white hover:bg-white/20"
        >
          <ZoomIn className="h-4 w-4" />
        </Button>
        <div className="w-px h-6 bg-white/20 mx-1" />
        <Button
          variant="ghost"
          size="icon"
          onClick={handleRotate}
          className="h-8 w-8 rounded-full text-white hover:bg-white/20"
        >
          <RotateCw className="h-4 w-4" />
        </Button>
      </div>

      {/* 图片计数器 */}
      <div className="absolute top-4 right-4 bg-black/50 text-white text-xs px-3 py-1 rounded-full backdrop-blur-sm">
        {currentIndex + 1} / {images.length}
      </div>
    </div>
  )
}
