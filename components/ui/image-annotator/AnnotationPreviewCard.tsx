import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { Card, CardContent } from '@/components/ui/card'
import { emotionShortcuts } from './ShortcutHints'

interface AnnotationPreviewCardProps {
  imageUrl: string
  emotion: string
  onEmotionChange: (emotion: string) => void
}

export function AnnotationPreviewCard({
  imageUrl,
  emotion,
  onEmotionChange,
}: AnnotationPreviewCardProps) {
  return (
    <Card className="rounded-none border-0 shadow-none shrink-0 w-[280px]">
      <CardContent className="p-1 space-y-1">
        <div className="h-[160px] overflow-hidden">
          <img
            src={imageUrl}
            alt="标注图片"
            className="w-full h-full object-cover"
          />
        </div>
        <Select value={emotion} onValueChange={onEmotionChange}>
          <SelectTrigger className="w-full h-8 text-sm">
            <SelectValue placeholder="选择情绪" />
          </SelectTrigger>
          <SelectContent>
            {Object.entries(emotionShortcuts).map(([key, value]) => (
              <SelectItem key={key} value={value} className="text-sm">
                {value}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </CardContent>
    </Card>
  )
} 