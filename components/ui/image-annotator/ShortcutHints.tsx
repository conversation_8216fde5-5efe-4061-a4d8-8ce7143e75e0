import { cn } from '@/lib/utils'

export const emotionShortcuts = {
  h: '高兴',
  a: '生气',
  d: '厌恶',
  f: '恐惧',
  s: '悲伤',
  j: '惊讶',
} as const

export type EmotionType = keyof typeof emotionShortcuts

interface ShortcutHintsProps {
  className?: string
  selectedEmotion?: string | null
}

export function ShortcutHints({ className, selectedEmotion }: ShortcutHintsProps) {
  return (
    <div className={cn('grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-3 p-2', className)}>
      {Object.entries(emotionShortcuts).map(([key, emotion]) => {
        const isSelected = selectedEmotion === emotion

        return (
          <div
            key={key}
            className={cn(
              'flex flex-col items-center rounded-md p-2 transition-all hover:bg-muted/50',
              isSelected && 'ring-2 ring-primary bg-primary/10'
            )}
          >
            <kbd className="inline-flex h-8 w-8 select-none items-center justify-center rounded border bg-background font-mono text-[14px] font-bold text-foreground shadow-[0_2px_0_0_rgba(0,0,0,0.2)]">
              {key.toUpperCase()}
            </kbd>
            <span className="mt-1 font-medium text-center text-sm">{emotion}</span>
          </div>
        )
      })}
    </div>
  )
}
