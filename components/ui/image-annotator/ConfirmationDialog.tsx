'use client'

import { useEffect } from 'react'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { emotionShortcuts } from './ShortcutHints'
import { Check, X } from 'lucide-react'

interface ConfirmationDialogProps {
  open: boolean
  selectedEmotion: string | null
  onConfirm: () => void
  onCancel: () => void
}

export function ConfirmationDialog({
  open,
  selectedEmotion,
  onConfirm,
  onCancel,
}: ConfirmationDialogProps) {
  useEffect(() => {
    if (!open) return

    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === ' ' || e.key === 'Enter') {
        e.preventDefault()
        onConfirm()
      } else if (e.key === 'Escape') {
        e.preventDefault()
        onCancel()
      }
    }

    window.addEventListener('keydown', handleKeyDown)
    return () => window.removeEventListener('keydown', handleKeyDown)
  }, [open, onConfirm, onCancel])

  // 查找选中情绪的快捷键
  const selectedEmotionKey = selectedEmotion
    ? Object.entries(emotionShortcuts).find(([_, emotion]) => emotion === selectedEmotion)?.[0]
    : null

  return (
    <Dialog open={open} onOpenChange={() => onCancel()}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle className="text-xl">确认情绪标注</DialogTitle>
          <DialogDescription className="pt-4 text-lg">您选择的情绪是：</DialogDescription>
        </DialogHeader>

        {selectedEmotion && (
          <div className="flex items-center justify-center py-4">
            <div className={`px-6 py-3 rounded-lg text-xl font-medium`}>
              {selectedEmotion}
              {selectedEmotionKey && (
                <span className="ml-2 text-xs opacity-70">
                  (快捷键: {selectedEmotionKey.toUpperCase()})
                </span>
              )}
            </div>
          </div>
        )}

        <DialogFooter className="sm:justify-between">
          <div className="flex gap-2 items-center text-sm text-muted-foreground">
            <span className="hidden sm:inline">按下</span>
            <kbd className="px-2 py-1 text-xs font-semibold text-gray-800 bg-gray-100 border border-gray-200 rounded-lg dark:bg-gray-600 dark:text-gray-100 dark:border-gray-500">
              空格
            </kbd>
            <span className="hidden sm:inline">或</span>
            <kbd className="px-2 py-1 text-xs font-semibold text-gray-800 bg-gray-100 border border-gray-200 rounded-lg dark:bg-gray-600 dark:text-gray-100 dark:border-gray-500">
              Enter
            </kbd>
            <span>确认</span>
          </div>

          <div className="flex gap-4">
            <Button variant="outline" onClick={onCancel} className="gap-2">
              <X className="h-4 w-4" />
              取消
            </Button>
            <Button onClick={onConfirm} className="gap-2">
              <Check className="h-4 w-4" />
              确认
            </Button>
          </div>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
