'use client'

import * as React from 'react'
import { X, Plus } from 'lucide-react'
import { cn } from '@/lib/utils'
import { Input } from './input'
import { Button } from './button'

export interface DynamicTagsProps extends Omit<React.HTMLAttributes<HTMLDivElement>, 'onChange'> {
  value?: string[]
  onChange?: (value: string[]) => void
  placeholder?: string
  maxTags?: number
  disabled?: boolean
  readOnly?: boolean
  tagClassName?: string
  inputClassName?: string
  addButtonClassName?: string
}

export const DynamicTags = React.forwardRef<HTMLDivElement, DynamicTagsProps>(
  (
    {
      className,
      value = [],
      onChange,
      placeholder = '添加标签...',
      maxTags,
      disabled = false,
      readOnly = false,
      tagClassName,
      inputClassName,
      addButtonClassName,
      ...props
    },
    ref
  ) => {
    const [tags, setTags] = React.useState<string[]>(value)
    const [inputValue, setInputValue] = React.useState<string>('')
    const inputRef = React.useRef<HTMLInputElement>(null)

    // 同步外部value变化
    React.useEffect(() => {
      setTags(value)
    }, [value])

    // 当内部tags变化时，触发onChange
    React.useEffect(() => {
      onChange?.(tags)
    }, [tags, onChange])

    const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
      setInputValue(e.target.value)
    }

    const handleInputKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
      if (e.key === 'Enter' && inputValue.trim()) {
        e.preventDefault()
        addTag(inputValue.trim())
      }
    }

    const addTag = (tag: string) => {
      if (
        tag &&
        !tags.includes(tag) &&
        (!maxTags || tags.length < maxTags) &&
        !disabled &&
        !readOnly
      ) {
        setTags([...tags, tag])
        setInputValue('')
      }
    }

    const removeTag = (index: number) => {
      if (!disabled && !readOnly) {
        const newTags = [...tags]
        newTags.splice(index, 1)
        setTags(newTags)
      }
    }

    const handleAddButtonClick = () => {
      if (inputValue.trim()) {
        addTag(inputValue.trim())
      } else {
        inputRef.current?.focus()
      }
    }

    const canAddMoreTags = !maxTags || tags.length < maxTags

    return (
      <div
        ref={ref}
        className={cn(
          'flex flex-wrap gap-2 p-2 border rounded-md bg-background',
          disabled && 'opacity-50 cursor-not-allowed',
          className
        )}
        {...props}
      >
        {tags.map((tag, index) => (
          <div
            key={`${tag}-${index}`}
            className={cn(
              'flex items-center gap-1 px-2 py-1 text-sm rounded-md bg-muted',
              tagClassName
            )}
          >
            <span>{tag}</span>
            {!readOnly && !disabled && (
              <button
                type="button"
                onClick={() => removeTag(index)}
                className="text-muted-foreground hover:text-foreground"
              >
                <X className="h-3 w-3" />
              </button>
            )}
          </div>
        ))}

        {canAddMoreTags && !readOnly && (
          <div className="flex items-center gap-1">
            <Input
              ref={inputRef}
              type="text"
              value={inputValue}
              onChange={handleInputChange}
              onKeyDown={handleInputKeyDown}
              placeholder={placeholder}
              disabled={disabled}
              className={cn('h-8 min-w-[120px] w-auto', inputClassName)}
            />
            <Button
              type="button"
              variant="ghost"
              size="sm"
              onClick={handleAddButtonClick}
              disabled={disabled || !inputValue.trim()}
              className={cn('h-8 px-2', addButtonClassName)}
            >
              <Plus className="h-4 w-4" />
            </Button>
          </div>
        )}
      </div>
    )
  }
)

DynamicTags.displayName = 'DynamicTags'
