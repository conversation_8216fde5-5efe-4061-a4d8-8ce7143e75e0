'use client'

import * as React from 'react'
import { Copy, Minus, Square, X } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { cn } from '@/lib/utils'

interface TitleBarProps {
  title?: string
  className?: string
}

declare global {
  interface Window {
    electronAPI?: {
      windowMinimize: () => Promise<void>
      windowMaximize: () => Promise<void>
      windowClose: () => Promise<void>
      isWindowMaximized: () => Promise<boolean>
      platform: string
    }
  }
}

const TitleBar = React.forwardRef<HTMLDivElement, TitleBarProps>(
  ({ title = '表情识别应用', className }, ref) => {
    const [isMaximized, setIsMaximized] = React.useState(false)
    const [isMac, setIsMac] = React.useState(false)

    React.useEffect(() => {
      const checkPlatform = async () => {
        if (typeof window !== 'undefined' && window.electronAPI) {
          setIsMac(window.electronAPI.platform === 'darwin')
          const maximized = await window.electronAPI.isWindowMaximized()
          setIsMaximized(maximized)
        }
      }
      checkPlatform()
    }, [])

    const handleMinimize = async () => {
      if (window.electronAPI) {
        await window.electronAPI.windowMinimize()
      }
    }

    const handleMaximize = async () => {
      if (window.electronAPI) {
        await window.electronAPI.windowMaximize()
        const maximized = await window.electronAPI.isWindowMaximized()
        setIsMaximized(maximized)
      }
    }

    const handleClose = async () => {
      if (window.electronAPI) {
        await window.electronAPI.windowClose()
      }
    }

    return (
      <div
        ref={ref}
        className={cn(
          'absolute top-0 left-0 w-full flex items-center justify-between h-8 bg-background border-b border-border px-4 select-none',
          'drag-region z-[9999]',
          className
        )}
        style={{ WebkitAppRegion: 'drag' } as React.CSSProperties}
      >
        {/* macOS 风格：左侧按钮 */}
        {isMac ? (
          <>
            <div
              className="flex items-center space-x-2"
              style={{ WebkitAppRegion: 'no-drag' } as React.CSSProperties}
            >
              <Button
                variant="ghost"
                size="icon"
                className="h-3 w-3 rounded-full bg-red-500 hover:bg-red-600 p-0"
                onClick={handleClose}
              >
                <X className="h-2 w-2 text-white opacity-0 hover:opacity-100" />
              </Button>
              <Button
                variant="ghost"
                size="icon"
                className="h-3 w-3 rounded-full bg-yellow-500 hover:bg-yellow-600 p-0"
                onClick={handleMinimize}
              >
                <Minus className="h-2 w-2 text-white opacity-0 hover:opacity-100" />
              </Button>
              <Button
                variant="ghost"
                size="icon"
                className="h-3 w-3 rounded-full bg-green-500 hover:bg-green-600 p-0"
                onClick={handleMaximize}
              >
                {isMaximized ? (
                  <Copy className="h-2 w-2 text-white opacity-0 hover:opacity-100" />
                ) : (
                  <Square className="h-2 w-2 text-white opacity-0 hover:opacity-100" />
                )}
              </Button>
            </div>
            <div className="flex-1 flex justify-center">
              <span className="text-sm text-muted-foreground">{title}</span>
            </div>
            <div className="w-[60px]" />
          </>
        ) : (
          /* Windows/Linux 风格：右侧按钮 */
          <>
            <div className="flex-1">
              <span className="text-sm text-muted-foreground">{title}</span>
            </div>
            <div
              className="flex items-center"
              style={{ WebkitAppRegion: 'no-drag' } as React.CSSProperties}
            >
              <Button
                variant="ghost"
                size="icon"
                className="h-8 w-8 hover:bg-muted"
                onClick={handleMinimize}
              >
                <Minus className="h-4 w-4" />
              </Button>
              <Button
                variant="ghost"
                size="icon"
                className="h-8 w-8 hover:bg-muted"
                onClick={handleMaximize}
              >
                {isMaximized ? <Copy className="h-4 w-4" /> : <Square className="h-4 w-4" />}
              </Button>
              <Button
                variant="ghost"
                size="icon"
                className="h-8 w-8 hover:bg-destructive hover:text-destructive-foreground"
                onClick={handleClose}
              >
                <X className="h-4 w-4" />
              </Button>
            </div>
          </>
        )}
      </div>
    )
  }
)

TitleBar.displayName = 'TitleBar'

export { TitleBar }
