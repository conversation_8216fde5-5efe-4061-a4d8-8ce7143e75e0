'use client'

import { <PERSON><PERSON> } from '@/components/ui/button'
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { useState } from 'react'
import { Download, FileDown, CheckCircle2, AlertCircle, FolderArchive } from 'lucide-react'
import { cn } from '@/lib/utils'
import { Separator } from '@/components/ui/separator'

interface ExportDatasetDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  datasetName: string
  onConfirm: () => void
}

export function ExportDatasetDialog({
  open,
  onOpenChange,
  datasetName,
  onConfirm
}: ExportDatasetDialogProps) {
  const [isLoading, setIsLoading] = useState(false)
  const [exportStatus, setExportStatus] = useState<'idle' | 'loading' | 'success' | 'error'>('idle')
  
  const handleConfirm = async () => {
    try {
      setIsLoading(true)
      setExportStatus('loading')
      await onConfirm()
      setExportStatus('success')
      // 成功后2秒关闭对话框
      setTimeout(() => {
        onOpenChange(false)
        setExportStatus('idle')
      }, 2000)
    } catch (error) {
      console.error('导出失败:', error)
      setExportStatus('error')
    } finally {
      setIsLoading(false)
    }
  }

  const exportPath = `datasets/exports/${datasetName.replace(/\s+/g, '_').toLowerCase()}`

  return (
    <Dialog open={open} onOpenChange={(open) => {
      if (exportStatus !== 'loading') {
        onOpenChange(open)
        if (!open) setExportStatus('idle')
      }
    }}>
      <DialogContent className="sm:max-w-[480px] p-0 gap-0 overflow-hidden">
        <div className="bg-blue-50 dark:bg-blue-950/30 px-6 py-5 border-b">
          <DialogHeader className="gap-1.5">
            <DialogTitle className="text-xl flex items-center gap-2 text-blue-700 dark:text-blue-400">
              <FileDown className="h-5 w-5" />
              数据集导出
            </DialogTitle>
            <DialogDescription className="text-blue-700/70 dark:text-blue-400/70">
              系统将为您导出所选数据集的完整内容
            </DialogDescription>
          </DialogHeader>
        </div>
        
        <div className="px-6 py-5">
          <div className="flex items-start gap-4 mb-4">
            <div className="h-10 w-10 rounded-full bg-blue-50 dark:bg-blue-900/30 flex items-center justify-center flex-shrink-0">
              <FolderArchive className="h-5 w-5 text-blue-600 dark:text-blue-400" />
            </div>
            <div>
              <h3 className="font-medium text-lg mb-1">{datasetName}</h3>
              <p className="text-sm text-muted-foreground">
                导出后的数据将包含所有图像文件及其相关元数据信息
              </p>
            </div>
          </div>
          
          <div className="rounded-md border p-4 bg-muted/30 mb-4">
            <div className="flex flex-col gap-1">
              <div className="text-sm font-medium">导出位置</div>
              <div className="flex items-center gap-2">
                <code className="text-xs bg-muted px-1.5 py-1 rounded font-mono">
                  {exportPath}
                </code>
                {exportStatus === 'success' && (
                  <span className="text-xs text-green-600 dark:text-green-400 flex items-center gap-0.5">
                    <CheckCircle2 className="h-3 w-3" /> 已导出
                  </span>
                )}
              </div>
            </div>
          </div>
          
          {exportStatus === 'error' && (
            <div className="rounded-md bg-red-50 dark:bg-red-900/20 p-3 flex items-center gap-2 text-red-600 dark:text-red-400 text-sm mb-4">
              <AlertCircle className="h-4 w-4" />
              <span>导出过程中发生错误，请稍后重试</span>
            </div>
          )}
        </div>
        
        <Separator />
        
        <DialogFooter className="px-6 py-4">
          <Button 
            variant="outline" 
            onClick={() => onOpenChange(false)}
            disabled={isLoading}
            className="border-blue-200 dark:border-blue-800 hover:bg-blue-50 dark:hover:bg-blue-950/50"
          >
            取消
          </Button>
          <Button 
            onClick={handleConfirm} 
            disabled={isLoading || exportStatus === 'success'}
            className={cn(
              "bg-blue-600 hover:bg-blue-700 text-white",
              exportStatus === 'success' && "bg-green-600 hover:bg-green-600"
            )}
          >
            {exportStatus === 'loading' ? (
              <>
                <span className="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-white border-t-transparent"></span>
                正在导出...
              </>
            ) : exportStatus === 'success' ? (
              <>
                <CheckCircle2 className="mr-2 h-4 w-4" />
                导出成功
              </>
            ) : (
              <>
                <Download className="mr-2 h-4 w-4" />
                确认导出
              </>
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
} 