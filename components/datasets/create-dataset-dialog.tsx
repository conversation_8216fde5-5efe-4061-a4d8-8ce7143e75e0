'use client'

import { useState, useEffect } from 'react'
import {
  <PERSON><PERSON>,
  <PERSON>alog<PERSON>ontent,
  Dialog<PERSON>ooter,
  DialogHeader,
  DialogTitle,
  DialogDescription,
} from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Checkbox } from '@/components/ui/checkbox'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { MultiSelect } from '@/components/ui/multi-select'
import { format } from 'date-fns'
import { Calendar } from '@/components/ui/calendar'
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover'
import { cn } from '@/lib/utils'
import { CalendarIcon, Info, FileText, Database, Tag, Clock, RefreshCw } from 'lucide-react'
import { Badge } from '@/components/ui/badge'
import { ScrollArea } from '@/components/ui/scroll-area'
import { motion } from 'framer-motion'
import { Separator } from '@/components/ui/separator'
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip'

interface CreateDatasetDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  onConfirm: (data: {
    name: string
    type: string
    labels: string[]
    startTime: Date
    endTime: Date
    keepUpdating: boolean
  }) => void
}

// 情绪颜色映射 - 使用更柔和的政企风格颜色
const emotionColors: Record<string, string> = {
  happy: 'bg-blue-50 text-blue-700 border-blue-200',
  sad: 'bg-slate-50 text-slate-700 border-slate-200',
  angry: 'bg-amber-50 text-amber-700 border-amber-200',
  surprised: 'bg-indigo-50 text-indigo-700 border-indigo-200',
  neutral: 'bg-gray-50 text-gray-700 border-gray-200',
}

// 图片加载动画变体
const imageVariants = {
  hidden: { opacity: 0, scale: 0.8 },
  visible: {
    opacity: 1,
    scale: 1,
    transition: {
      duration: 0.5,
      ease: 'easeOut',
    },
  },
}

// 容器动画
const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.05,
    },
  },
}

// 项目动画
const itemVariants = {
  hidden: { opacity: 0, y: 20 },
  visible: {
    opacity: 1,
    y: 0,
    transition: { duration: 0.3 },
  },
}

// Mock data for preview
const mockData = Array.from({ length: 100 }, (_, i) => ({
  id: i + 1,
  image: `https://picsum.photos/seed/${i}/200/200`,
  emotion: ['happy', 'sad', 'angry', 'surprised', 'neutral'][Math.floor(Math.random() * 5)],
  labels: ['工作', '学习', '娱乐', '运动', '休息'].slice(0, Math.floor(Math.random() * 3) + 1),
  createdAt: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000),
}))

export function CreateDatasetDialog({ open, onOpenChange, onConfirm }: CreateDatasetDialogProps) {
  const [name, setName] = useState('')
  const [type, setType] = useState('')
  const [selectedLabels, setSelectedLabels] = useState<string[]>([])
  const [startDate, setStartDate] = useState<Date>()
  const [endDate, setEndDate] = useState<Date>()
  const [keepUpdating, setKeepUpdating] = useState(false)
  const [imageLoaded, setImageLoaded] = useState<Record<number, boolean>>({})

  const handleImageLoad = (id: number) => {
    setImageLoaded(prev => ({ ...prev, [id]: true }))
  }

  const handleConfirm = () => {
    if (!name || !type || !startDate || !endDate) return
    onConfirm({
      name,
      type,
      labels: selectedLabels,
      startTime: startDate,
      endTime: endDate,
      keepUpdating,
    })
    onOpenChange(false)
  }

  const labelOptions = [
    { label: '工作', value: '工作' },
    { label: '学习', value: '学习' },
    { label: '娱乐', value: '娱乐' },
    { label: '运动', value: '运动' },
    { label: '休息', value: '休息' },
  ]

  // Filter data based on selected conditions
  const filteredData = mockData.filter(item => {
    // Filter by labels if any are selected
    if (selectedLabels.length > 0 && !selectedLabels.some(label => item.labels.includes(label))) {
      return false
    }

    // Filter by date range
    if (startDate && new Date(item.createdAt) < startDate) {
      return false
    }
    if (endDate && new Date(item.createdAt) > endDate) {
      return false
    }

    return true
  })

  // Update calculateMatchingImages to use filteredData
  const calculateMatchingImages = () => {
    return filteredData.length
  }

  // 重置表单
  const resetForm = () => {
    setName('')
    setType('')
    setSelectedLabels([])
    setStartDate(undefined)
    setEndDate(undefined)
    setKeepUpdating(false)
  }

  // 当对话框关闭时重置表单
  useEffect(() => {
    if (!open) {
      resetForm()
    }
  }, [open])

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-3xl p-0 overflow-hidden max-h-[90vh]">
        <div className="bg-gradient-to-r from-blue-600 to-blue-700 p-4 text-white">
          <DialogHeader className="space-y-1">
            <DialogTitle className="text-xl font-semibold flex items-center">
              <Database className="mr-2 h-5 w-5" />
              新建数据集
            </DialogTitle>
            <DialogDescription className="text-blue-100 text-sm">
              创建一个新的数据集并配置筛选条件，以便更好地管理和分析数据
            </DialogDescription>
          </DialogHeader>
        </div>

        <ScrollArea className="max-h-[calc(90vh-180px)]">
          <div className="p-4 space-y-4">
            {/* Basic Info Section */}
            <motion.div
              className="space-y-3"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3 }}
            >
              <div className="flex items-center text-base font-medium text-blue-700">
                <FileText className="mr-2 h-4 w-4" />
                基本信息
              </div>
              <Separator className="my-2" />
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-1.5">
                  <Label htmlFor="name" className="text-sm font-medium">
                    数据集名称
                  </Label>
                  <Input
                    id="name"
                    value={name}
                    onChange={e => setName(e.target.value)}
                    placeholder="请输入数据集名称"
                    className="border-slate-200 focus:border-blue-500 focus:ring-blue-500"
                  />
                </div>
                <div className="space-y-1.5">
                  <Label htmlFor="type" className="text-sm font-medium">
                    数据集类型
                  </Label>
                  <Select value={type} onValueChange={setType}>
                    <SelectTrigger className="border-slate-200 focus:border-blue-500 focus:ring-blue-500">
                      <SelectValue placeholder="选择数据集类型" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="image">图片</SelectItem>
                      <SelectItem value="gif">动图</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </motion.div>

            {/* Filter Section */}
            <motion.div
              className="space-y-3"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3, delay: 0.1 }}
            >
              <div className="flex items-center text-base font-medium text-blue-700">
                <Tag className="mr-2 h-4 w-4" />
                筛选条件
              </div>
              <Separator className="my-2" />
              <div className="space-y-4">
                <div className="space-y-1.5">
                  <Label className="text-sm font-medium">标签</Label>
                  <MultiSelect
                    options={labelOptions}
                    selected={selectedLabels}
                    onChange={setSelectedLabels}
                    placeholder="选择标签"
                    className="border-slate-200 focus:border-blue-500 focus:ring-blue-500"
                  />
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-1.5">
                    <Label className="text-sm font-medium">起始时间</Label>
                    <Popover>
                      <PopoverTrigger asChild>
                        <Button
                          variant="outline"
                          className={cn(
                            'w-full justify-start text-left font-normal border-slate-200 hover:bg-slate-50',
                            !startDate && 'text-muted-foreground'
                          )}
                        >
                          <CalendarIcon className="mr-2 h-4 w-4" />
                          {startDate ? format(startDate, 'yyyy-MM-dd') : '选择时间'}
                        </Button>
                      </PopoverTrigger>
                      <PopoverContent className="w-auto p-0" align="start">
                        <Calendar
                          mode="single"
                          selected={startDate}
                          onSelect={setStartDate}
                          initialFocus
                          className="rounded-md border border-slate-200"
                        />
                      </PopoverContent>
                    </Popover>
                  </div>
                  <div className="space-y-1.5">
                    <Label className="text-sm font-medium">终止时间</Label>
                    <Popover>
                      <PopoverTrigger asChild>
                        <Button
                          variant="outline"
                          className={cn(
                            'w-full justify-start text-left font-normal border-slate-200 hover:bg-slate-50',
                            !endDate && 'text-muted-foreground'
                          )}
                        >
                          <CalendarIcon className="mr-2 h-4 w-4" />
                          {endDate ? format(endDate, 'yyyy-MM-dd') : '选择时间'}
                        </Button>
                      </PopoverTrigger>
                      <PopoverContent className="w-auto p-0" align="start">
                        <Calendar
                          mode="single"
                          selected={endDate}
                          onSelect={setEndDate}
                          initialFocus
                          className="rounded-md border border-slate-200"
                        />
                      </PopoverContent>
                    </Popover>
                  </div>
                </div>
              </div>
              <div className="flex items-center justify-between pt-1">
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <div className="flex items-center space-x-2 cursor-pointer">
                        <Checkbox
                          id="keepUpdating"
                          checked={keepUpdating}
                          onCheckedChange={checked => setKeepUpdating(checked as boolean)}
                          className="data-[state=checked]:bg-blue-600 data-[state=checked]:border-blue-600"
                        />
                        <label
                          htmlFor="keepUpdating"
                          className="text-sm font-medium leading-none flex items-center peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                        >
                          保持更新
                          <RefreshCw className="ml-1 h-3 w-3 text-muted-foreground" />
                        </label>
                      </div>
                    </TooltipTrigger>
                    <TooltipContent>
                      <p className="text-xs">启用后，数据集将根据筛选条件自动更新</p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
                <div className="text-xs bg-blue-50 text-blue-700 px-2.5 py-1 rounded-full flex items-center">
                  <Clock className="h-3 w-3 mr-1" />
                  预计满足条件的图片数量：
                  <span className="font-semibold ml-1">{calculateMatchingImages()}</span> 张
                </div>
              </div>
            </motion.div>

            {/* Preview Section */}
            <motion.div
              className="space-y-3"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3, delay: 0.2 }}
            >
              <div className="flex items-center justify-between">
                <div className="flex items-center text-base font-medium text-blue-700">
                  <Info className="mr-2 h-4 w-4" />
                  数据预览
                  <Badge
                    variant="outline"
                    className="ml-2 bg-blue-50 text-blue-700 border-blue-200 text-xs"
                  >
                    {filteredData.length} 项
                  </Badge>
                </div>
              </div>
              <Separator className="my-2" />
              <div className="border border-slate-200 rounded-lg shadow-sm bg-white">
                {filteredData.length > 0 ? (
                  <ScrollArea className="h-[200px]" scrollHideDelay={100}>
                    <motion.div
                      className="grid grid-cols-4 gap-3 p-3"
                      variants={containerVariants}
                      initial="hidden"
                      animate="visible"
                    >
                      {filteredData.map(item => (
                        <motion.div
                          key={item.id}
                          variants={itemVariants}
                          className={cn(
                            'overflow-hidden rounded-lg border border-slate-200 shadow-sm transition-all',
                            'hover:shadow-md hover:border-blue-200'
                          )}
                          whileHover={{ scale: 1.02, boxShadow: '0 4px 12px rgba(0,0,0,0.08)' }}
                          whileTap={{ scale: 0.98 }}
                        >
                          <div className="relative aspect-[4/3] overflow-hidden bg-slate-50">
                            {!imageLoaded[item.id] && (
                              <div className="absolute inset-0 flex items-center justify-center">
                                <div className="w-4 h-4 border-2 border-blue-500 border-t-transparent rounded-full animate-spin"></div>
                              </div>
                            )}
                            <motion.img
                              src={item.image}
                              alt={`Preview ${item.id}`}
                              className={cn(
                                'object-cover w-full h-full transition-transform hover:scale-105',
                                !imageLoaded[item.id] && 'opacity-0'
                              )}
                              loading="lazy"
                              onLoad={() => handleImageLoad(item.id)}
                              onError={() => handleImageLoad(item.id)}
                              variants={imageVariants}
                              initial="hidden"
                              animate={imageLoaded[item.id] ? 'visible' : 'hidden'}
                            />
                            <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-slate-900/70 to-transparent p-1.5 pt-6">
                              <div className="flex items-center justify-between">
                                <span className="text-[10px] font-medium text-white">
                                  ID: {item.id}
                                </span>
                                <Badge
                                  className={cn(
                                    'text-[10px] px-1 py-0',
                                    emotionColors[item.emotion]
                                  )}
                                >
                                  {item.emotion}
                                </Badge>
                              </div>
                            </div>
                          </div>
                          <div className="p-1.5 bg-slate-50">
                            {item.labels.length > 0 && (
                              <div className="flex flex-wrap gap-1">
                                {item.labels.map(label => (
                                  <Badge
                                    key={label}
                                    variant="outline"
                                    className="text-[9px] px-1 py-0 bg-white"
                                  >
                                    {label}
                                  </Badge>
                                ))}
                              </div>
                            )}
                            <div className="text-[9px] text-slate-500 mt-0.5 flex justify-between items-center">
                              <span>{format(item.createdAt, 'yyyy-MM-dd')}</span>
                            </div>
                          </div>
                        </motion.div>
                      ))}
                    </motion.div>
                  </ScrollArea>
                ) : (
                  <div className="flex flex-col items-center justify-center h-[150px] text-slate-500 bg-slate-50/50">
                    <Database className="h-8 w-8 text-slate-300 mb-2" />
                    <p className="text-sm">没有符合条件的数据</p>
                    <p className="text-xs text-slate-400 mt-1">请调整筛选条件后重试</p>
                  </div>
                )}
              </div>
            </motion.div>
          </div>
        </ScrollArea>

        <DialogFooter className="bg-slate-50 p-3 border-t border-slate-200">
          <div className="flex items-center justify-between w-full">
            <div className="text-xs text-slate-500">
              {!name || !type || !startDate || !endDate
                ? '请填写必填项以继续'
                : '所有必填项已填写完成，可以创建数据集'}
            </div>
            <div className="flex gap-2">
              <Button
                variant="outline"
                onClick={() => onOpenChange(false)}
                className="border-slate-200 hover:bg-slate-100 hover:text-slate-700 h-8 text-sm"
              >
                取消
              </Button>
              <Button
                onClick={handleConfirm}
                disabled={!name || !type || !startDate || !endDate}
                className="bg-blue-600 hover:bg-blue-700 text-white h-8 text-sm"
              >
                创建数据集
              </Button>
            </div>
          </div>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
