'use client'

import { useEffect, useState } from 'react'
import { <PERSON><PERSON> } from '@/components/ui/button'
import { ScrollArea } from '@/components/ui/scroll-area'
import { ChevronLeft, ChevronRight, Info, RotateCw, X, ZoomIn, ZoomOut } from 'lucide-react'
import { cn } from '@/lib/utils'
import { Badge } from '@/components/ui/badge'
import { format } from 'date-fns'
import { <PERSON>bs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Card, CardContent } from '@/components/ui/card'
import { AnimatePresence, motion } from 'framer-motion'
import { Separator } from '@/components/ui/separator'

// Mock data for preview
const mockData = Array.from({ length: 100 }, (_, i) => ({
  id: i + 1,
  image: `https://picsum.photos/seed/${i}/200/200`,
  emotion: ['happy', 'sad', 'angry', 'surprised', 'neutral'][Math.floor(Math.random() * 5)],
  labels: ['工作', '学习', '娱乐', '运动', '休息'].slice(0, Math.floor(Math.random() * 3) + 1),
  confidence: Math.random().toFixed(2),
  createdAt: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000),
  metadata: {
    width: 400,
    height: 300,
    format: 'jpg',
    size: `${Math.floor(Math.random() * 500) + 100}KB`,
  },
}))

// 情绪颜色映射
const emotionColors: Record<string, string> = {
  happy: 'bg-green-100 text-green-800 border-green-200',
  sad: 'bg-blue-100 text-blue-800 border-blue-200',
  angry: 'bg-red-100 text-red-800 border-red-200',
  surprised: 'bg-purple-100 text-purple-800 border-purple-200',
  neutral: 'bg-gray-100 text-gray-800 border-gray-200',
}

// 图片加载动画变体
const imageVariants = {
  hidden: { opacity: 0, scale: 0.8 },
  visible: {
    opacity: 1,
    scale: 1,
    transition: {
      duration: 0.5,
      ease: 'easeOut',
    },
  },
}

interface DatasetInfo {
  name: string
  type: string
  totalImages: number
  createdAt: Date
  labels: string[]
}

interface DatasetPreviewDrawerProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  datasetInfo?: DatasetInfo
}

export function DatasetPreviewDrawer({
  open,
  onOpenChange,
  datasetInfo = {
    name: '情绪识别数据集',
    type: '图像',
    totalImages: mockData.length,
    createdAt: new Date(),
    labels: ['happy', 'sad', 'angry', 'surprised', 'neutral'],
  },
}: DatasetPreviewDrawerProps) {
  const [selectedImage, setSelectedImage] = useState<number | null>(null)
  const [zoomLevel, setZoomLevel] = useState(1)
  const [rotation, setRotation] = useState(0)
  const [viewMode, setViewMode] = useState<'grid' | 'detail'>('grid')
  const [isVisible, setIsVisible] = useState(false)
  const [imageLoaded, setImageLoaded] = useState<Record<number, boolean>>({})

  // 控制抽屉的显示和隐藏
  useEffect(() => {
    if (open) {
      setIsVisible(true)
      document.body.style.overflow = 'hidden'
    } else {
      const timer = setTimeout(() => {
        setIsVisible(false)
      }, 300) // 与动画持续时间相同
      document.body.style.overflow = ''

      return () => clearTimeout(timer)
    }

    return () => {
      document.body.style.overflow = ''
    }
  }, [open])

  const selectedItem =
    selectedImage !== null ? mockData.find(item => item.id === selectedImage) : null

  const handleZoomIn = () => {
    setZoomLevel(prev => Math.min(prev + 0.25, 3))
  }

  const handleZoomOut = () => {
    setZoomLevel(prev => Math.max(prev - 0.25, 0.5))
  }

  const handleRotate = () => {
    setRotation(prev => (prev + 90) % 360)
  }

  const handlePrevious = () => {
    if (selectedImage && selectedImage > 1) {
      setSelectedImage(selectedImage - 1)
    }
  }

  const handleNext = () => {
    if (selectedImage && selectedImage < mockData.length) {
      setSelectedImage(selectedImage + 1)
    }
  }

  const handleSelectImage = (id: number) => {
    if (selectedImage === id) {
      if (viewMode === 'grid') {
        setViewMode('detail')
      } else {
        setViewMode('grid')
        setSelectedImage(null)
      }
    } else {
      setSelectedImage(id)
      setViewMode('detail')
    }

    // Reset zoom and rotation
    setZoomLevel(1)
    setRotation(0)
  }

  const handleBack = () => {
    setViewMode('grid')
    setZoomLevel(1)
    setRotation(0)
  }

  const handleImageLoad = (id: number) => {
    setImageLoaded(prev => ({ ...prev, [id]: true }))
  }

  if (!isVisible && !open) return null

  return (
    <>
      {/* 背景遮罩 */}
      <AnimatePresence>
        {open && (
          <motion.div
            className="fixed inset-0 bg-black/50 z-50 backdrop-blur-sm"
            onClick={() => onOpenChange(false)}
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            transition={{ duration: 0.2 }}
          />
        )}
      </AnimatePresence>

      {/* 抽屉内容 */}
      <div
        className={cn(
          'fixed right-0 top-0 bottom-0 z-50 w-2/5 bg-background shadow-xl',
          'border-l border-t border-b rounded-l-lg',
          'transition-transform duration-300 ease-in-out',
          open ? 'translate-x-0' : 'translate-x-full'
        )}
      >
        <div className="flex flex-col h-full">
          <div className="border-b p-6">
            <div className="flex items-center justify-between mb-4">
              <h2 className="text-2xl font-bold tracking-tight">{datasetInfo.name}</h2>
              <Button
                variant="ghost"
                size="icon"
                onClick={() => onOpenChange(false)}
                className="hover:bg-muted"
              >
                <X className="h-4 w-4" />
              </Button>
            </div>
            <div className="grid grid-cols-2 gap-x-8 gap-y-3">
              <div>
                <p className="text-sm text-muted-foreground">类型</p>
                <p className="font-medium">{datasetInfo.type}</p>
              </div>
              <div>
                <p className="text-sm text-muted-foreground">图片数量</p>
                <p className="font-medium">{datasetInfo.totalImages}</p>
              </div>
              <div>
                <p className="text-sm text-muted-foreground">创建时间</p>
                <p className="font-medium">{format(datasetInfo.createdAt, 'yyyy-MM-dd')}</p>
              </div>
              <div>
                <p className="text-sm text-muted-foreground">标签</p>
                <div className="flex flex-wrap gap-1 mt-1">
                  {datasetInfo.labels.map(label => (
                    <Badge key={label} variant="outline" className="text-xs">
                      {label}
                    </Badge>
                  ))}
                </div>
              </div>
            </div>
          </div>

          <AnimatePresence mode="wait">
            {viewMode === 'grid' ? (
              <motion.div
                key="grid-view"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                exit={{ opacity: 0 }}
                transition={{ duration: 0.2 }}
                className="h-[calc(100%-var(--header-height))]"
                style={{ '--header-height': '180px' } as React.CSSProperties}
              >
                <div className="flex items-center justify-between p-2 border-b bg-muted/10">
                  <div className="text-sm font-medium flex items-center">
                    <Info className="h-4 w-4 mr-1 text-muted-foreground" />
                    预览图片 ({mockData.length})
                  </div>
                </div>
                <ScrollArea className="h-[calc(100%-40px)]" scrollHideDelay={100}>
                  <div className="grid grid-cols-4 gap-3 p-3">
                    {mockData.map((item, index) => (
                      <motion.div
                        key={item.id}
                        initial={{ opacity: 0, y: 20 }}
                        animate={{
                          opacity: 1,
                          y: 0,
                          transition: { delay: Math.min(index * 0.01, 0.5), duration: 0.3 },
                        }}
                        className={cn(
                          'overflow-hidden rounded-lg border cursor-pointer transition-all hover:shadow-md',
                          selectedImage === item.id && 'ring-2 ring-primary',
                          'bg-white'
                        )}
                        onClick={() => handleSelectImage(item.id)}
                        whileHover={{ scale: 1.02, boxShadow: '0 4px 12px rgba(0,0,0,0.1)' }}
                        whileTap={{ scale: 0.98 }}
                      >
                        <div className="relative aspect-[4/3] overflow-hidden bg-muted/30">
                          {!imageLoaded[item.id] && (
                            <div className="absolute inset-0 flex items-center justify-center">
                              <div className="w-5 h-5 border-2 border-primary border-t-transparent rounded-full animate-spin"></div>
                            </div>
                          )}
                          <motion.img
                            src={item.image}
                            alt={`Preview ${item.id}`}
                            className={cn(
                              'object-cover w-full h-full transition-transform hover:scale-105',
                              !imageLoaded[item.id] && 'opacity-0'
                            )}
                            loading="lazy"
                            onLoad={() => handleImageLoad(item.id)}
                            onError={() => handleImageLoad(item.id)}
                            variants={imageVariants}
                            initial="hidden"
                            animate={imageLoaded[item.id] ? 'visible' : 'hidden'}
                          />
                          <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/60 to-transparent p-2 pt-6">
                            <div className="flex items-center justify-between">
                              <span className="text-xs font-medium text-white">ID: {item.id}</span>
                              <Badge
                                className={cn('text-xs px-1.5 py-0', emotionColors[item.emotion])}
                              >
                                {item.emotion}
                              </Badge>
                            </div>
                          </div>
                        </div>
                        <div className="p-2 bg-muted/10">
                          {item.labels.length > 0 && (
                            <div className="flex flex-wrap gap-1">
                              {item.labels.map(label => (
                                <Badge
                                  key={label}
                                  variant="outline"
                                  className="text-[10px] px-1 py-0"
                                >
                                  {label}
                                </Badge>
                              ))}
                            </div>
                          )}
                          <div className="text-[10px] text-muted-foreground mt-1 flex justify-between items-center">
                            <span>{format(item.createdAt, 'yyyy-MM-dd')}</span>
                            <span className="text-primary hover:underline">查看详情</span>
                          </div>
                        </div>
                      </motion.div>
                    ))}
                  </div>
                </ScrollArea>
              </motion.div>
            ) : selectedItem ? (
              <motion.div
                key="detail-view"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                exit={{ opacity: 0 }}
                transition={{ duration: 0.2 }}
                className="flex flex-col h-[calc(100%-var(--header-height))]"
                style={{ '--header-height': '180px' } as React.CSSProperties}
              >
                <div className="flex items-center justify-between p-2 border-b bg-muted/20">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={handleBack}
                    className="gap-1 hover:bg-muted"
                  >
                    <ChevronLeft className="h-4 w-4" />
                    返回
                  </Button>
                  <div className="flex items-center space-x-1">
                    <Button
                      variant="ghost"
                      size="icon"
                      onClick={handleZoomOut}
                      className="h-8 w-8 hover:bg-muted"
                    >
                      <ZoomOut className="h-4 w-4" />
                    </Button>
                    <Button
                      variant="ghost"
                      size="icon"
                      onClick={handleZoomIn}
                      className="h-8 w-8 hover:bg-muted"
                    >
                      <ZoomIn className="h-4 w-4" />
                    </Button>
                    <Button
                      variant="ghost"
                      size="icon"
                      onClick={handleRotate}
                      className="h-8 w-8 hover:bg-muted"
                    >
                      <RotateCw className="h-4 w-4" />
                    </Button>
                  </div>
                  <div className="flex items-center space-x-1">
                    <Button
                      variant="ghost"
                      size="icon"
                      onClick={handlePrevious}
                      disabled={selectedImage === 1}
                      className="h-8 w-8 hover:bg-muted"
                    >
                      <ChevronLeft className="h-4 w-4" />
                    </Button>
                    <span className="text-xs font-medium">
                      {selectedImage} / {mockData.length}
                    </span>
                    <Button
                      variant="ghost"
                      size="icon"
                      onClick={handleNext}
                      disabled={selectedImage === mockData.length}
                      className="h-8 w-8 hover:bg-muted"
                    >
                      <ChevronRight className="h-4 w-4" />
                    </Button>
                  </div>
                </div>

                <div className="flex-1 overflow-auto">
                  <motion.div
                    className="p-4 flex justify-center"
                    key={`image-${selectedItem?.id}`}
                    initial={{ opacity: 0, scale: 0.9 }}
                    animate={{ opacity: 1, scale: 1 }}
                    transition={{ duration: 0.3 }}
                  >
                    <div
                      className="relative overflow-hidden bg-[url('/grid-pattern.svg')] bg-muted/20 rounded-lg p-2 shadow-md"
                      style={{
                        maxWidth: '100%',
                        maxHeight: '400px',
                      }}
                    >
                      {selectedItem && (
                        <>
                          <motion.img
                            src={selectedItem.image}
                            alt={`Image ${selectedItem.id}`}
                            className="object-contain rounded shadow-sm"
                            style={{
                              transformOrigin: 'center',
                            }}
                            animate={{
                              scale: zoomLevel,
                              rotate: rotation,
                            }}
                            transition={{ type: 'spring', stiffness: 200, damping: 20 }}
                          />
                          <div className="absolute top-3 right-3 bg-black/70 text-white text-xs px-2 py-1 rounded-full">
                            {selectedItem.metadata.width} × {selectedItem.metadata.height}
                          </div>
                        </>
                      )}
                    </div>
                  </motion.div>

                  <Tabs defaultValue="info" className="px-4">
                    <TabsList className="w-full grid grid-cols-2">
                      <TabsTrigger value="info">基本信息</TabsTrigger>
                      <TabsTrigger value="metadata">元数据</TabsTrigger>
                    </TabsList>
                    <AnimatePresence mode="wait">
                      <TabsContent value="info" className="pt-4">
                        <motion.div
                          initial={{ opacity: 0, y: 10 }}
                          animate={{ opacity: 1, y: 0 }}
                          exit={{ opacity: 0, y: -10 }}
                          transition={{ duration: 0.2 }}
                        >
                          <Card className="overflow-hidden shadow-sm">
                            <CardContent className="p-0">
                              {selectedItem && (
                                <>
                                  <div className="p-3 bg-muted/10 border-b">
                                    <div className="flex items-center justify-between">
                                      <h3 className="font-medium">图片信息</h3>
                                      <Badge
                                        className={cn(
                                          'text-xs',
                                          emotionColors[selectedItem.emotion]
                                        )}
                                      >
                                        {selectedItem.emotion}
                                      </Badge>
                                    </div>
                                  </div>
                                  <div className="p-4 space-y-4">
                                    <div className="grid grid-cols-2 gap-3">
                                      <div className="space-y-1">
                                        <p className="text-xs text-muted-foreground">ID</p>
                                        <p className="font-medium">{selectedItem.id}</p>
                                      </div>
                                      <div className="space-y-1">
                                        <p className="text-xs text-muted-foreground">置信度</p>
                                        <div className="flex items-center gap-2">
                                          <div className="w-full bg-muted rounded-full h-2">
                                            <div
                                              className="bg-primary h-2 rounded-full"
                                              style={{
                                                width: `${Number(selectedItem.confidence) * 100}%`,
                                              }}
                                            ></div>
                                          </div>
                                          <span className="text-xs font-medium">
                                            {Number(selectedItem.confidence) * 100}%
                                          </span>
                                        </div>
                                      </div>
                                      <div className="space-y-1">
                                        <p className="text-xs text-muted-foreground">创建时间</p>
                                        <p className="font-medium">
                                          {format(selectedItem.createdAt, 'yyyy-MM-dd HH:mm')}
                                        </p>
                                      </div>
                                    </div>

                                    <Separator />

                                    <div className="space-y-1">
                                      <p className="text-xs text-muted-foreground">标签</p>
                                      <div className="flex flex-wrap gap-1">
                                        {selectedItem.labels.map(label => (
                                          <Badge key={label} variant="outline">
                                            {label}
                                          </Badge>
                                        ))}
                                      </div>
                                    </div>
                                  </div>
                                </>
                              )}
                            </CardContent>
                          </Card>
                        </motion.div>
                      </TabsContent>
                      <TabsContent value="metadata" className="pt-4">
                        <motion.div
                          initial={{ opacity: 0, y: 10 }}
                          animate={{ opacity: 1, y: 0 }}
                          exit={{ opacity: 0, y: -10 }}
                          transition={{ duration: 0.2 }}
                        >
                          <Card className="overflow-hidden shadow-sm">
                            <CardContent className="p-0">
                              {selectedItem && (
                                <>
                                  <div className="p-3 bg-muted/10 border-b">
                                    <h3 className="font-medium">技术元数据</h3>
                                  </div>
                                  <div className="p-4 space-y-4">
                                    <div className="grid grid-cols-2 gap-3">
                                      <div className="space-y-1">
                                        <p className="text-xs text-muted-foreground">宽度</p>
                                        <p className="font-medium">
                                          {selectedItem.metadata.width}px
                                        </p>
                                      </div>
                                      <div className="space-y-1">
                                        <p className="text-xs text-muted-foreground">高度</p>
                                        <p className="font-medium">
                                          {selectedItem.metadata.height}px
                                        </p>
                                      </div>
                                      <div className="space-y-1">
                                        <p className="text-xs text-muted-foreground">格式</p>
                                        <p className="font-medium uppercase">
                                          {selectedItem.metadata.format}
                                        </p>
                                      </div>
                                      <div className="space-y-1">
                                        <p className="text-xs text-muted-foreground">文件大小</p>
                                        <p className="font-medium">{selectedItem.metadata.size}</p>
                                      </div>
                                    </div>

                                    <Separator />

                                    <div className="space-y-1">
                                      <p className="text-xs text-muted-foreground">图片路径</p>
                                      <div className="bg-muted/30 p-2 rounded text-xs font-mono overflow-x-auto">
                                        /datasets/emotions/
                                        {selectedItem.id.toString().padStart(5, '0')}.
                                        {selectedItem.metadata.format}
                                      </div>
                                    </div>
                                  </div>
                                </>
                              )}
                            </CardContent>
                          </Card>
                        </motion.div>
                      </TabsContent>
                    </AnimatePresence>
                  </Tabs>
                </div>
              </motion.div>
            ) : null}
          </AnimatePresence>
        </div>
      </div>
    </>
  )
}
