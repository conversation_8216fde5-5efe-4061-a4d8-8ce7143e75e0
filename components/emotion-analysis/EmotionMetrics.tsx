interface EmotionData {
  neutral: number // 中性
  happiness: number // 快乐
  sadness: number // 悲伤
  anger: number // 愤怒
  fear: number // 恐惧
  disgust: number // 厌恶
  surprise: number // 惊讶
}

interface EmotionMetricsProps {
  data: EmotionData
}

const emotionColors = {
  neutral: 'bg-gray-50 text-gray-700 border-gray-200',
  happiness: 'bg-blue-50 text-blue-700 border-blue-200',
  sadness: 'bg-gray-50 text-gray-700 border-gray-200',
  anger: 'bg-gray-50 text-gray-700 border-gray-200',
  fear: 'bg-gray-50 text-gray-700 border-gray-200',
  disgust: 'bg-gray-50 text-gray-700 border-gray-200',
  surprise: 'bg-gray-50 text-gray-700 border-gray-200',
}

const emotionLabels = {
  neutral: '中性',
  happiness: '快乐',
  sadness: '悲伤',
  anger: '愤怒',
  fear: '恐惧',
  disgust: '厌恶',
  surprise: '惊讶',
}

export function EmotionMetrics({ data }: EmotionMetricsProps) {
  return (
    <div className="h-full">
      <div className="grid grid-cols-2 gap-4">
        {(Object.keys(data) as Array<keyof EmotionData>).map(key => (
          <div
            key={key}
            className={`p-3 rounded-lg border ${emotionColors[key]} transition-all duration-300`}
          >
            <div className="flex items-center justify-between mb-2">
              <span className="text-sm font-medium">{emotionLabels[key]}</span>
              <span className="font-mono text-sm">{data[key]}%</span>
            </div>
            <div className="w-full h-1.5 bg-gray-100 rounded-full overflow-hidden">
              <div
                className="h-full rounded-full transition-all duration-300"
                style={{
                  width: `${data[key]}%`,
                  backgroundColor: 'currentColor',
                }}
              />
            </div>
          </div>
        ))}
      </div>
    </div>
  )
}
