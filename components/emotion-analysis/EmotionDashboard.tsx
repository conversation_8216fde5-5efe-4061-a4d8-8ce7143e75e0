'use client'

import { useEffect, useState } from 'react'
import { Card } from '@/components/ui/card'
import { FaceTracking } from './FaceTracking'
import { EmotionMetrics } from './EmotionMetrics'
import { RealTimeChart } from './RealTimeChart'
import * as faceapi from 'face-api.js'
import { Progress } from '@/components/ui/progress'

interface EmotionData {
  neutral: number // 中性
  happiness: number // 快乐
  sadness: number // 悲伤
  anger: number // 愤怒
  fear: number // 恐惧
  disgust: number // 厌恶
  surprise: number // 惊讶
}

interface FaceFeatures {
  eyeAperture: number
  mouthOpenness: number
  headPose: {
    pitch: number
    yaw: number
    roll: number
  }
}

export function EmotionDashboard() {
  const [emotionData, setEmotionData] = useState<EmotionData>({
    neutral: 0,
    happiness: 0,
    sadness: 0,
    anger: 0,
    fear: 0,
    disgust: 0,
    surprise: 0,
  })

  const [faceFeatures, setFaceFeatures] = useState<FaceFeatures>({
    eyeAperture: 0,
    mouthOpenness: 0,
    headPose: {
      pitch: 0,
      yaw: 0,
      roll: 0,
    },
  })

  const handleEmotionDetected = (expressions: faceapi.FaceExpressions) => {
    setEmotionData({
      neutral: Math.round(expressions.neutral * 100),
      happiness: Math.round(expressions.happy * 100),
      sadness: Math.round(expressions.sad * 100),
      anger: Math.round(expressions.angry * 100),
      fear: Math.round(expressions.fearful * 100),
      disgust: Math.round(expressions.disgusted * 100),
      surprise: Math.round(expressions.surprised * 100),
    })
  }

  const handleFaceFeaturesDetected = (features: FaceFeatures) => {
    setFaceFeatures(features)
  }

  return (
    <div className="grid grid-cols-12 gap-2 h-full p-2 bg-gray-50 overflow-auto">
      <div className="col-span-4 grid grid-rows-2 gap-2">
        <Card className="p-3 shadow-sm border-gray-200">
          <div className="h-full flex flex-col">
            <h3 className="text-sm font-medium text-gray-700 mb-2">面部追踪</h3>
            <div className="flex-1 min-h-0">
              <FaceTracking
                onEmotionDetected={handleEmotionDetected}
                onFaceFeaturesDetected={handleFaceFeaturesDetected}
              />
            </div>
          </div>
        </Card>

        <Card className="p-3 shadow-sm border-gray-200">
          <div className="h-full flex flex-col">
            <h3 className="text-sm font-medium text-gray-700 mb-2">面部特征</h3>
            <div className="space-y-2 overflow-auto">
              <div className="space-y-1">
                <div className="flex justify-between text-xs text-gray-600">
                  <span>眼睛开合度</span>
                  <span className="font-mono">{Math.round(faceFeatures.eyeAperture * 100)}%</span>
                </div>
                <Progress value={faceFeatures.eyeAperture * 100} className="h-1.5 bg-gray-100" />
              </div>

              <div className="space-y-1">
                <div className="flex justify-between text-xs text-gray-600">
                  <span>嘴巴开合度</span>
                  <span className="font-mono">{Math.round(faceFeatures.mouthOpenness * 100)}%</span>
                </div>
                <Progress value={faceFeatures.mouthOpenness * 100} className="h-1.5 bg-gray-100" />
              </div>

              <div className="space-y-1">
                <div className="text-xs text-gray-600 mb-1">头部姿态</div>
                <div className="grid grid-cols-3 gap-1">
                  <div className="p-1 bg-gray-50 rounded-lg text-center border border-gray-200">
                    <div className="text-[10px] text-gray-500">俯仰角</div>
                    <div className="font-mono text-xs">
                      {Math.round(faceFeatures.headPose.pitch)}°
                    </div>
                  </div>
                  <div className="p-1 bg-gray-50 rounded-lg text-center border border-gray-200">
                    <div className="text-[10px] text-gray-500">偏航角</div>
                    <div className="font-mono text-xs">
                      {Math.round(faceFeatures.headPose.yaw)}°
                    </div>
                  </div>
                  <div className="p-1 bg-gray-50 rounded-lg text-center border border-gray-200">
                    <div className="text-[10px] text-gray-500">翻滚角</div>
                    <div className="font-mono text-xs">
                      {Math.round(faceFeatures.headPose.roll)}°
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </Card>
      </div>

      <div className="col-span-8 grid grid-rows-2 gap-2">
        <Card className="p-3 shadow-sm border-gray-200">
          <div className="h-full flex flex-col">
            <h3 className="text-sm font-medium text-gray-700 mb-2">情绪分布</h3>
            <div className="flex-1 min-h-0">
              <RealTimeChart data={emotionData} />
            </div>
          </div>
        </Card>

        <Card className="p-3 shadow-sm border-gray-200">
          <div className="h-full flex flex-col">
            <h3 className="text-sm font-medium text-gray-700 mb-2">情绪指标</h3>
            <div className="flex-1 min-h-0 overflow-auto">
              <EmotionMetrics data={emotionData} />
            </div>
          </div>
        </Card>
      </div>
    </div>
  )
}
