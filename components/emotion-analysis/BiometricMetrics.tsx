interface BiometricData {
  heartRate: number
  bloodPressure: {
    systolic: number
    diastolic: number
  }
  breathing: number
}

interface BiometricMetricsProps {
  data: BiometricData
}

export function BiometricMetrics({ data }: BiometricMetricsProps) {
  return (
    <div className="space-y-4">
      <h3 className="text-lg font-semibold">生理指标</h3>
      
      <div className="grid gap-4">
        <div className="flex items-center justify-between p-3 bg-blue-50 rounded-lg">
          <span className="text-blue-700">心率</span>
          <span className="font-mono">{data.heartRate} BPM</span>
        </div>
        
        <div className="flex items-center justify-between p-3 bg-green-50 rounded-lg">
          <span className="text-green-700">血压</span>
          <span className="font-mono">
            {data.bloodPressure.systolic}/{data.bloodPressure.diastolic} mmHg
          </span>
        </div>
        
        <div className="flex items-center justify-between p-3 bg-purple-50 rounded-lg">
          <span className="text-purple-700">呼吸</span>
          <span className="font-mono">{data.breathing} 次/分</span>
        </div>
      </div>
    </div>
  )
} 