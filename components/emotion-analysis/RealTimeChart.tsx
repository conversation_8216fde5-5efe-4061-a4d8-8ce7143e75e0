'use client'

import { useEffect, useRef } from 'react'
import { Chart, ChartConfiguration, ChartData } from 'chart.js/auto'

interface EmotionData {
  neutral: number // 中性
  happiness: number // 快乐
  sadness: number // 悲伤
  anger: number // 愤怒
  fear: number // 恐惧
  disgust: number // 厌恶
  surprise: number // 惊讶
}

interface RealTimeChartProps {
  data: EmotionData
}

export function RealTimeChart({ data }: RealTimeChartProps) {
  const chartRef = useRef<HTMLCanvasElement>(null)
  const chartInstance = useRef<Chart | null>(null)

  useEffect(() => {
    if (!chartRef.current) return

    const ctx = chartRef.current.getContext('2d')
    if (!ctx) return

    const labels = ['中性', '快乐', '悲伤', '愤怒', '恐惧', '厌恶', '惊讶']
    const values = [
      data.neutral,
      data.happiness,
      data.sadness,
      data.anger,
      data.fear,
      data.disgust,
      data.surprise,
    ]

    const chartData: ChartData = {
      labels,
      datasets: [
        {
          label: '情绪指标',
          data: values,
          fill: true,
          backgroundColor: 'rgba(54, 162, 235, 0.2)',
          borderColor: 'rgb(54, 162, 235)',
          pointBackgroundColor: 'rgb(54, 162, 235)',
          pointBorderColor: '#fff',
          pointHoverBackgroundColor: '#fff',
          pointHoverBorderColor: 'rgb(54, 162, 235)',
        },
      ],
    }

    const config: ChartConfiguration = {
      type: 'radar',
      data: chartData,
      options: {
        responsive: true,
        maintainAspectRatio: false,
        elements: {
          line: {
            borderWidth: 3,
          },
        },
        scales: {
          r: {
            angleLines: {
              display: true,
            },
            suggestedMin: 0,
            suggestedMax: 100,
            ticks: {
              display: false,
            },
          },
        },
        plugins: {
          legend: {
            display: false,
          },
        },
        animation: {
          duration: 500,
        },
      },
    }

    if (chartInstance.current) {
      chartInstance.current.destroy()
    }

    chartInstance.current = new Chart(ctx, config)

    return () => {
      if (chartInstance.current) {
        chartInstance.current.destroy()
      }
    }
  }, [data])

  return (
    <div className="h-full w-full flex items-center justify-center">
      <div className="w-full h-[calc(100%-2rem)]">
        <canvas ref={chartRef} />
      </div>
    </div>
  )
}
