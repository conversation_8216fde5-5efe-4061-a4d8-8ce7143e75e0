'use client'

import { useEffect, useRef, useState } from 'react'
import * as faceapi from 'face-api.js'

interface FaceTrackingProps {
  onEmotionDetected?: (expressions: faceapi.FaceExpressions) => void
  onFaceFeaturesDetected?: (features: {
    eyeAperture: number
    mouthOpenness: number
    headPose: {
      pitch: number
      yaw: number
      roll: number
    }
  }) => void
}

export function FaceTracking({ onEmotionDetected, onFaceFeaturesDetected }: FaceTrackingProps) {
  const videoRef = useRef<HTMLVideoElement>(null)
  const canvasRef = useRef<HTMLCanvasElement>(null)
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    const loadModels = async () => {
      try {
        await Promise.all([
          faceapi.nets.tinyFaceDetector.loadFromUri('/models'),
          faceapi.nets.faceLandmark68Net.loadFromUri('/models'),
          faceapi.nets.faceExpressionNet.loadFromUri('/models'),
          faceapi.nets.faceRecognitionNet.loadFromUri('/models'),
        ])
        setIsLoading(false)
      } catch (error) {
        console.error('Error loading face detection models:', error)
      }
    }

    loadModels()
  }, [])

  useEffect(() => {
    if (isLoading) return

    const startVideo = async () => {
      try {
        const stream = await navigator.mediaDevices.getUserMedia({
          video: {
            width: { ideal: 320 },
            height: { ideal: 240 },
          },
        })
        if (videoRef.current) {
          videoRef.current.srcObject = stream
        }
      } catch (error) {
        console.error('Error accessing webcam:', error)
      }
    }

    startVideo()

    return () => {
      const stream = videoRef.current?.srcObject as MediaStream
      stream?.getTracks().forEach(track => track.stop())
    }
  }, [isLoading])

  useEffect(() => {
    if (isLoading) return

    const detectFace = async () => {
      if (!videoRef.current || !canvasRef.current) return

      const detections = await faceapi
        .detectAllFaces(videoRef.current, new faceapi.TinyFaceDetectorOptions())
        .withFaceLandmarks()
        .withFaceExpressions()

      if (detections.length > 0) {
        const detection = detections[0]

        // 处理情绪数据
        if (onEmotionDetected) {
          onEmotionDetected(detection.expressions)
        }

        // 处理面部特征
        if (onFaceFeaturesDetected) {
          const landmarks = detection.landmarks

          // 计算眼睛开合程度
          const leftEye = landmarks.getLeftEye()
          const rightEye = landmarks.getRightEye()
          const eyeAperture = (calculateEyeAperture(leftEye) + calculateEyeAperture(rightEye)) / 2

          // 计算嘴巴开合程度
          const mouth = landmarks.getMouth()
          const mouthOpenness = calculateMouthOpenness(mouth)

          // 计算头部姿态
          const headPose = calculateHeadPose(landmarks)

          onFaceFeaturesDetected({
            eyeAperture,
            mouthOpenness,
            headPose,
          })
        }

        // 绘制检测结果
        const canvas = canvasRef.current
        const displaySize = {
          width: videoRef.current.width,
          height: videoRef.current.height,
        }

        faceapi.matchDimensions(canvas, displaySize)
        const resizedDetections = faceapi.resizeResults(detections, displaySize)

        canvas.getContext('2d')?.clearRect(0, 0, canvas.width, canvas.height)
        faceapi.draw.drawDetections(canvas, resizedDetections)
        faceapi.draw.drawFaceLandmarks(canvas, resizedDetections)
      }
    }

    const interval = setInterval(detectFace, 100)
    return () => clearInterval(interval)
  }, [isLoading, onEmotionDetected, onFaceFeaturesDetected])

  // 计算眼睛开合程度
  const calculateEyeAperture = (eye: faceapi.Point[]) => {
    const top = eye[1].y
    const bottom = eye[5].y
    const height = bottom - top
    return Math.min(1, Math.max(0, height / 20)) // 归一化到 0-1 范围
  }

  // 计算嘴巴开合程度
  const calculateMouthOpenness = (mouth: faceapi.Point[]) => {
    const top = mouth[13].y
    const bottom = mouth[19].y
    const height = bottom - top
    return Math.min(1, Math.max(0, height / 30)) // 归一化到 0-1 范围
  }

  // 计算头部姿态
  const calculateHeadPose = (landmarks: faceapi.FaceLandmarks68) => {
    const nose = landmarks.positions[30]
    const leftEye = landmarks.positions[36]
    const rightEye = landmarks.positions[45]

    // 简化的头部姿态计算
    const eyeCenter = {
      x: (leftEye.x + rightEye.x) / 2,
      y: (leftEye.y + rightEye.y) / 2,
    }

    const pitch = (Math.atan2(nose.y - eyeCenter.y, 100) * 180) / Math.PI
    const yaw = (Math.atan2(nose.x - eyeCenter.x, 100) * 180) / Math.PI
    const roll = (Math.atan2(rightEye.y - leftEye.y, rightEye.x - leftEye.x) * 180) / Math.PI

    return { pitch, yaw, roll }
  }

  return (
    <div className="relative h-full flex items-center justify-center">
      {isLoading && (
        <div className="absolute inset-0 flex items-center justify-center bg-black/50 text-white">
          加载模型中...
        </div>
      )}
      <div className="relative w-full max-w-[320px] aspect-[4/3]">
        <video
          ref={videoRef}
          autoPlay
          muted
          className="w-full h-full object-cover"
          width={320}
          height={240}
        />
        <canvas
          ref={canvasRef}
          className="absolute top-0 left-0 w-full h-full"
          width={320}
          height={240}
        />
      </div>
    </div>
  )
}
