import {
  Menu,
  Settings,
  Users,
  Shield,
  Tag,
  Database,
  PenTool,
  ChevronDown,
  ChevronRight,
  Plus,
  Edit,
  Trash,
  Smile,
  type LucideIcon,
  type LucideProps,
} from 'lucide-react'
import { createElement } from 'react'

export const EmotionIcon = Smile

// 创建一个函数来渲染图标
export const renderIcon = (Icon: LucideIcon, props: LucideProps = {}) => {
  return createElement(Icon, props)
}

export const Icons = {
  Menu,
  Settings,
  Users,
  Shield,
  Tag,
  Database,
  PenTool,
  ChevronDown,
  ChevronRight,
  Plus,
  Edit,
  Trash,
} as const

export type IconKey = keyof typeof Icons
