'use client'

import { But<PERSON> } from '@/components/ui/button'
import { Calendar } from '@/components/ui/calendar'
import { Combobox } from '@/components/ui/combobox'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover'
import { useToast } from '@/components/ui/use-toast'
import { cn } from '@/lib/utils'
import { format } from 'date-fns'
import { Search } from 'lucide-react'
import * as React from 'react'
import { DateRange } from 'react-day-picker'
import { Locale } from 'date-fns'

export interface FilterOption {
  value: string
  label: string
}

export interface InputFilter {
  type: 'input'
  name: string
  label: string
  placeholder: string
  value?: string
}

export interface SelectFilter {
  type: 'select'
  name: string
  label: string
  placeholder: string
  options?: FilterOption[]
  value?: string
  fetchUrl?: string
}

export interface DateRangeFilter {
  type: 'dateRange'
  name: string
  label: string
  placeholder?: string
  value?: DateRange | undefined
  locale?: Locale
}

export type Filter = InputFilter | SelectFilter | DateRangeFilter

interface FilterSectionProps {
  filters: Filter[]
  onSearch: (filters: Record<string, any>) => void
  className?: string
  actions?: React.ReactNode
}

export function TableFilterSection({
  filters: initialFilters,
  onSearch,
  className,
  actions,
}: FilterSectionProps) {
  const [filters, setFilters] = React.useState<Filter[]>(initialFilters)
  const hasFilters = filters && filters.length > 0
  const gridCols = hasFilters ? Math.min(filters.length + 1, 4) : 1

  const handleFilterChange = (name: string, value: any) => {
    setFilters(prev =>
      prev.map(filter => {
        if (filter.name === name) {
          return { ...filter, value }
        }
        return filter
      })
    )
  }

  const handleSearch = () => {
    const filterValues = filters.reduce(
      (acc, filter) => {
        if (filter.value) {
          acc[filter.name] = filter.value
        }
        return acc
      },
      {} as Record<string, any>
    )

    onSearch(filterValues)
  }

  const handleReset = () => {
    const resetFilters = initialFilters.map(filter => {
      if (filter.type === 'select') {
        return { ...filter, value: '' }
      }
      if (filter.type === 'dateRange') {
        return { ...filter, value: undefined }
      }
      // input type
      return { ...filter, value: '' }
    }) as Filter[]

    setFilters(resetFilters)
    // 重置时调用 onSearch，传入空的过滤条件
    onSearch({})
  }

  return (
    <div className={cn('space-y-1 py-4', className)}>
      <div className={`grid grid-cols-1 md:grid-cols-2 lg:grid-cols-${gridCols} gap-4`}>
        {filters.map((filter, index) => {
          if (filter.type === 'input') {
            return (
              <div key={filter.name} className="space-y-2">
                <Label className="text-sm text-muted-foreground">{filter.label}</Label>
                <div className="relative">
                  <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                  <Input
                    placeholder={filter.placeholder}
                    value={filter.value || ''}
                    onChange={e => handleFilterChange(filter.name, e.target.value)}
                    className="pl-8"
                  />
                </div>
              </div>
            )
          }

          if (filter.type === 'select') {
            return (
              <div key={filter.name} className="space-y-2">
                <Label className="text-sm text-muted-foreground">{filter.label}</Label>
                <Combobox
                  options={filter.options}
                  fetchUrl={filter.fetchUrl}
                  value={filter.value || ''}
                  onValueChange={value => handleFilterChange(filter.name, value)}
                  placeholder={filter.placeholder}
                  searchPlaceholder={`搜索${filter.label}...`}
                />
              </div>
            )
          }

          if (filter.type === 'dateRange') {
            const [open, setOpen] = React.useState(false)

            return (
              <div key={filter.name} className="space-y-2">
                <Label className="text-sm text-muted-foreground">{filter.label}</Label>
                <Popover open={open} onOpenChange={setOpen}>
                  <PopoverTrigger asChild>
                    <Button
                      variant="outline"
                      className="w-full justify-start text-left font-normal relative"
                    >
                      <span className="text-muted-foreground">
                        {filter.value?.from ? (
                          filter.value.to ? (
                            <>
                              {format(filter.value.from, 'yyyy-MM-dd')} -{' '}
                              {format(filter.value.to, 'yyyy-MM-dd')}
                            </>
                          ) : (
                            format(filter.value.from, 'yyyy-MM-dd')
                          )
                        ) : (
                          filter.placeholder || filter.label
                        )}
                      </span>
                      {filter.value && (
                        <div
                          className="absolute right-2 top-1/2 -translate-y-1/2 h-5 w-5 rounded-full hover:bg-gray-200 flex items-center justify-center"
                          onClick={e => {
                            e.stopPropagation()
                            handleFilterChange(filter.name, undefined)
                          }}
                        >
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            width="24"
                            height="24"
                            viewBox="0 0 24 24"
                            fill="none"
                            stroke="currentColor"
                            strokeWidth="2"
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            className="h-3 w-3"
                          >
                            <path d="M18 6 6 18" />
                            <path d="m6 6 12 12" />
                          </svg>
                        </div>
                      )}
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0" align="start">
                    <Calendar
                      initialFocus
                      mode="range"
                      defaultMonth={filter.value?.from}
                      selected={filter.value}
                      onSelect={range => handleFilterChange(filter.name, range)}
                      numberOfMonths={2}
                      locale={filter.locale}
                    />
                  </PopoverContent>
                </Popover>
              </div>
            )
          }
        })}
      </div>

      <div className="flex flex-col sm:flex-row items-center justify-between gap-4 pt-4">
        {actions && <div className="flex items-center gap-2">{actions}</div>}
        <div className="flex items-center gap-2">
          <Button variant="outline" size="sm" className="h-8" onClick={handleReset}>
            重置
          </Button>
          <Button
            variant="default"
            size="sm"
            className="h-8 bg-blue-600 hover:bg-blue-700 text-white"
            onClick={handleSearch}
          >
            查询
          </Button>
        </div>
      </div>
    </div>
  )
}
