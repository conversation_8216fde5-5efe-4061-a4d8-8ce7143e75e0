'use client'

import { useEffect, useState } from 'react'
import { useRouter, useSearchParams } from 'next/navigation'
import { DataTable, DataTableProps } from './index'
import { ColumnDef } from '@tanstack/react-table'

export interface SortableDataTableProps<TData> extends Omit<DataTableProps<TData>, 'defaultSort'> {
  initialSort?: {
    sortBy: string
    sortOrder: 'asc' | 'desc'
  }
  // highlight 回调函数，用于高亮新创建的行
  highlightRow?: (row: TData, highlightId?: string | number) => boolean
}

export function SortableDataTable<TData>({
  url,
  columns,
  filters,
  actions,
  pageSize: initialPageSize = 10,
  pageSizeOptions = [10, 20, 50],
  onSelectionChange,
  initialSort,
  highlightRow,
  ...rest
}: SortableDataTableProps<TData>) {
  const router = useRouter()
  const searchParams = useSearchParams()
  
  // 从 URL 参数中获取排序和分页信息
  const sortBy = searchParams.get('sortBy') || initialSort?.sortBy
  const sortOrder = searchParams.get('sortOrder') as 'asc' | 'desc' || initialSort?.sortOrder
  const page = searchParams.get('page')
  const newTaskId = searchParams.get('newTaskId')
  
  // 根据 URL 参数设置默认排序
  const defaultSort = sortBy && sortOrder 
    ? { sortBy, sortOrder } 
    : initialSort
  
  // 当路由参数变化时更新组件状态
  const [refreshKey, setRefreshKey] = useState(Date.now())
  
  useEffect(() => {
    // 当 URL 参数变化时，更新组件状态
    setRefreshKey(Date.now())
  }, [searchParams])

  // 行渲染器，用于高亮新创建的行
  const rowClassNameFn = (row: TData): string => {
    if (newTaskId && highlightRow && highlightRow(row, newTaskId)) {
      return 'bg-blue-50 transition-colors duration-1000'
    }
    return ''
  }
  
  return (
    <DataTable
      url={url}
      columns={columns}
      filters={filters}
      actions={actions}
      pageSize={initialPageSize}
      pageSizeOptions={pageSizeOptions}
      onSelectionChange={onSelectionChange}
      defaultSort={defaultSort}
      rowClassName={rowClassNameFn}
      key={`sortable-table-${refreshKey}`}
      {...rest}
    />
  )
} 