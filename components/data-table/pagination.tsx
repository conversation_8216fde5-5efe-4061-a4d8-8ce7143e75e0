import { Table } from '@tanstack/react-table'
import { Button } from '@/components/ui/button'
import { ChevronLeft, ChevronRight, ChevronsLeft, ChevronsRight } from 'lucide-react'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'

interface PaginationProps<TData> {
  table: Table<TData>
  totalRows?: number
  pageSizeOptions?: number[]
  onPageChange?: (page: number, size: number) => void
}

export function DataTablePagination<TData>({
  table,
  totalRows = 0,
  pageSizeOptions = [10, 20, 30, 40, 50],
  onPageChange,
}: PaginationProps<TData>) {
  const { pageIndex, pageSize } = table.getState().pagination
  const totalPages = table.getPageCount()

  return (
    <div className="flex items-center justify-between px-2 py-4">
      <div className="flex-1 text-sm text-muted-foreground">共 {totalRows} 条记录</div>
      <div className="flex items-center gap-6 lg:gap-8">
        {/* 每页记录数选择器 */}
        <div className="flex items-center gap-2">
          <span className="text-sm">每页</span>
          <Select
            value={`${pageSize}`}
            onValueChange={value => {
              const newSize = Number(value)
              onPageChange?.(1, newSize)
            }}
          >
            <SelectTrigger className="h-8 w-[70px]">
              <SelectValue placeholder={pageSize} />
            </SelectTrigger>
            <SelectContent side="top">
              {pageSizeOptions.map(size => (
                <SelectItem key={size} value={`${size}`}>
                  {size}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          <span className="text-sm">条</span>
        </div>

        {/* 分页按钮组 */}
        <div className="flex items-center gap-1">
          <Button
            variant="outline"
            size="sm"
            onClick={() => onPageChange?.(1, pageSize)}
            disabled={pageIndex === 0}
            className="h-8 w-8 p-0 border-gray-300"
          >
            <ChevronsLeft className="h-4 w-4" />
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => onPageChange?.(pageIndex, pageSize)}
            disabled={pageIndex === 0}
            className="h-8 w-8 p-0 border-gray-300"
          >
            <ChevronLeft className="h-4 w-4" />
          </Button>

          {/* 页码按钮组 */}
          <div className="flex items-center gap-1">
            {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
              let pageNum
              if (totalPages <= 5) {
                pageNum = i + 1
              } else if (pageIndex < 3) {
                pageNum = i + 1
              } else if (pageIndex > totalPages - 3) {
                pageNum = totalPages - 4 + i
              } else {
                pageNum = pageIndex - 1 + i
              }

              return (
                <Button
                  key={i}
                  variant={pageNum === pageIndex + 1 ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => onPageChange?.(pageNum, pageSize)}
                  className={`h-8 min-w-[2rem] border-gray-300 ${
                    pageNum === pageIndex + 1
                      ? 'bg-blue-600 text-white hover:bg-blue-700'
                      : 'hover:bg-gray-100'
                  }`}
                >
                  {pageNum}
                </Button>
              )
            })}
          </div>

          <Button
            variant="outline"
            size="sm"
            onClick={() => onPageChange?.(pageIndex + 2, pageSize)}
            disabled={pageIndex >= totalPages - 1}
            className="h-8 w-8 p-0 border-gray-300"
          >
            <ChevronRight className="h-4 w-4" />
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => onPageChange?.(totalPages, pageSize)}
            disabled={pageIndex >= totalPages - 1}
            className="h-8 w-8 p-0 border-gray-300"
          >
            <ChevronsRight className="h-4 w-4" />
          </Button>
        </div>

        {/* 总页数显示 */}
        <div className="flex items-center text-sm">
          <span>共 {totalPages} 页</span>
        </div>
      </div>
    </div>
  )
}
