'use client'

import {
  ColumnDef,
  flexRender,
  getCoreRowModel,
  getFilteredRowModel,
  getSortedRowModel,
  RowSelectionState,
  useReactTable,
} from '@tanstack/react-table'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import { cn } from '@/lib/utils'
import { DataTablePagination } from './pagination'
import { DataTableEmpty } from './table-empty'
import { DataTableLoading } from './table-loading'
import { forwardRef, useEffect, useImperativeHandle, useState } from 'react'
import { Filter, TableFilterSection } from '@/components/data-table/table-filter-section'
import { useToast } from '@/components/ui/use-toast'
import { Button } from '@/components/ui/button'
import { Checkbox } from '@/components/ui/checkbox'
import { httpClient } from '@/utils/HttpClient'

export interface DataTableProps<TData> {
  url: string
  columns: ColumnDef<TData, any>[]
  filters?: Filter[]
  actions?: React.ReactNode
  pageSize?: number
  pageSizeOptions?: number[]
  onSelectionChange?: (rows: TData[]) => void
  getRowId?: (row: TData) => string
}

export interface DataTableRef<TData> {
  handleSearch: (filters?: Record<string, any>) => void
  refresh: () => void
  getCurrentData: () => TData[]
}

export const DataTable = forwardRef(function DataTable<TData>(
  {
    url,
    columns,
    filters,
    actions,
    pageSize: initialPageSize = 10,
    pageSizeOptions = [10, 20, 50, 100],
    onSelectionChange,
    getRowId: getRowIdProp,
  }: DataTableProps<TData>,
  ref: React.ForwardedRef<DataTableRef<TData>>
) {
  const { toast } = useToast()

  const [searchParams, setSearchParams] = useState<Record<string, any>>({})
  const [data, setData] = useState<TData[]>([])
  const [total, setTotal] = useState(0)
  const [pageIndex, setPageIndex] = useState(1)
  const [pageSize, setPageSize] = useState(initialPageSize)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [rowSelection, setRowSelection] = useState<RowSelectionState>({})

  const table = useReactTable({
    data,
    columns: [
      {
        id: 'select',
        header: ({ table }) => (
          <Checkbox
            checked={table.getIsAllPageRowsSelected()}
            onCheckedChange={value => table.toggleAllPageRowsSelected(!!value)}
            aria-label="全选"
          />
        ),
        cell: ({ row }) => (
          <Checkbox
            checked={row.getIsSelected()}
            onCheckedChange={value => row.toggleSelected(!!value)}
            aria-label="选择行"
          />
        ),
      },
      ...columns,
    ],
    getCoreRowModel: getCoreRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    getRowId: (originalRow, index, _parent) => {
      if (typeof getRowIdProp === 'function') {
        const id = getRowIdProp(originalRow as TData)
        return id != null ? String(id) : String(index)
      }
      const candidate: any = originalRow as any
      if (candidate && (candidate.itemId != null || candidate.id != null)) {
        return String(candidate.itemId ?? candidate.id)
      }
      return String(index)
    },
    state: {
      pagination: {
        pageIndex: pageIndex - 1, // tanstack table 使用 0-based 索引
        pageSize,
      },
      rowSelection,
    },
    pageCount: Math.ceil(total / pageSize),
    manualPagination: true,
    enableRowSelection: true,
    onRowSelectionChange: setRowSelection,
  })

  const fetchData = async (params: Record<string, any>) => {
    try {
      setLoading(true)
      const queryParams = {
        ...params,
        page: params.page?.toString() || '1',
        pageSize: params.pageSize?.toString() || pageSize.toString(),
      }

      const response = await httpClient.post(`${url}`, queryParams)
      setData(response.records)
      setTotal(response.total)
      // 同步本地分页状态：优先使用后端返回，其次使用查询参数
      const nextPageIndex = params.page ?? 1
      const nextPageSize = params.pageSize ?? pageSize
      setPageIndex(nextPageIndex)
      setPageSize(nextPageSize)
    } catch (error) {
      setError('Failed to fetch data')
      toast({
        title: '错误',
        description: '加载数据失败，请重试',
        variant: 'destructive',
      })
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchData(searchParams)
  }, [searchParams])

  useEffect(() => {
    if (onSelectionChange) {
      const selectedRows = table.getFilteredSelectedRowModel().rows.map(row => row.original)
      onSelectionChange(selectedRows)
    }
  }, [rowSelection])

  // 暴露方法给父组件
  useImperativeHandle(
    ref,
    () => ({
      handleSearch: (filters?: Record<string, any>) => {
        // 新查询时清空选中行，避免跨查询残留
        setRowSelection({})
        setPageIndex(1)
        if (filters) {
          setSearchParams({
            ...filters,
            page: 1,
            pageSize,
          })
        } else {
          setSearchParams({
            page: 1,
            pageSize,
          })
        }
      },
      refresh: () => {
        fetchData(searchParams)
      },
      getCurrentData: () => data,
    }),
    [pageSize, searchParams, data]
  )

  const handleSearch = (filters: Record<string, any>) => {
    // 新查询时清空选中行，避免跨查询残留
    setRowSelection({})
    setPageIndex(1)
    setSearchParams({
      ...filters,
      page: 1,
      pageSize,
    })
  }

  const handlePageChange = (page: number, newPageSize: number) => {
    // 切换分页时清空当页选中，避免因 rowIndex 复用导致错选
    setRowSelection({})
    // 先同步本地分页状态，确保 UI 立即反映变更
    setPageIndex(page)
    setPageSize(newPageSize)
    // 触发数据请求
    setSearchParams(prev => ({
      ...prev,
      page,
      pageSize: newPageSize,
    }))
  }

  // 计算实际的列数（包括选择列）
  const totalColumns = columns.length + 1 // +1 for selection column

  return (
    <div className="px-8 py-2">
      <div className={cn('space-y-1')}>
        <TableFilterSection filters={filters || []} onSearch={handleSearch} actions={actions} />

        <div className="rounded-md border">
          <Table>
            <TableHeader className="bg-gray-50">
              {table.getHeaderGroups().map(headerGroup => (
                <TableRow key={headerGroup.id}>
                  {headerGroup.headers.map(header => (
                    <TableHead key={header.id}>
                      {flexRender(header.column.columnDef.header, header.getContext())}
                    </TableHead>
                  ))}
                </TableRow>
              ))}
            </TableHeader>
            <TableBody>
              {loading ? (
                <DataTableLoading colSpan={totalColumns} />
              ) : error ? (
                <tr>
                  <td colSpan={totalColumns} className="p-4 text-center">
                    <div className="text-red-600">
                      <p>{error}</p>
                      <Button
                        variant="outline"
                        onClick={() => fetchData(searchParams)}
                        className="mt-4"
                      >
                        重试
                      </Button>
                    </div>
                  </td>
                </tr>
              ) : table.getRowModel().rows.length === 0 ? (
                <DataTableEmpty colSpan={totalColumns} />
              ) : (
                table.getRowModel().rows.map(row => (
                  <TableRow key={row.id} className={cn('cursor-pointer hover:bg-muted/50')}>
                    {row.getVisibleCells().map(cell => (
                      <TableCell key={cell.id}>
                        {flexRender(cell.column.columnDef.cell, cell.getContext())}
                      </TableCell>
                    ))}
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </div>

        <DataTablePagination
          table={table}
          totalRows={total}
          pageSizeOptions={pageSizeOptions}
          onPageChange={handlePageChange}
        />
      </div>
    </div>
  )
}) as <TData>(
  props: DataTableProps<TData> & { ref?: React.ForwardedRef<DataTableRef<TData>> }
) => JSX.Element
