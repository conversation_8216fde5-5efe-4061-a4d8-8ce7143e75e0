'use client'

import { Home, LogOut, Menu } from 'lucide-react'
import { useRouter } from 'next/navigation'
import { useLayout } from './layout-context'
import { Button } from './ui/button'
import { useAuthContext } from '@/contexts/AuthContext'

export default function Navigation() {
  const { isLogin, logout } = useAuthContext()
  const router = useRouter()
  const { isSidebarCollapsed, toggleSidebar } = useLayout()

  const handleLogout = () => {
    logout()
  }

  return (
    <div className="h-14 bg-white shadow-sm flex items-center justify-between px-6 z-10 flex-shrink-0">
      <div className="flex items-center space-x-3">
        <button
          onClick={toggleSidebar}
          className="p-2 hover:bg-gray-50 rounded-lg transition-colors duration-200"
        >
          <Menu className="w-5 h-5 text-gray-600" />
        </button>
        {!isSidebarCollapsed && (
          <span className="font-semibold text-gray-900 text-lg">情绪识别系统 - 管理后台</span>
        )}
      </div>
      {isLogin && (
        <div className="flex items-center gap-4">
          <Button
            variant="ghost"
            size="sm"
            className="flex items-center text-gray-600 hover:text-gray-900"
            onClick={() => router.push('/')}
          >
            <Home className="w-4 h-4 mr-2" />
            返回主页
          </Button>
          <Button
            variant="ghost"
            size="sm"
            className="flex items-center text-gray-600 hover:text-gray-900"
            onClick={handleLogout}
          >
            <LogOut className="w-4 h-4 mr-2" />
            登出
          </Button>
        </div>
      )}
    </div>
  )
}
