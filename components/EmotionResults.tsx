'use client'

import { useState, useEffect } from 'react'
import { useAppContext } from '@/contexts/AppContext'
import { recognizeEmotion } from '@/lib/services/emotionRecognition'

export default function EmotionResults() {
  const [emotion, setEmotion] = useState<string | null>(null)
  const { selectedModel, selectedDevice } = useAppContext()

  useEffect(() => {
    let intervalId: NodeJS.Timeout

    async function updateEmotion() {
      if (selectedModel && selectedDevice) {
        const recognizedEmotion = await recognizeEmotion()
        setEmotion(recognizedEmotion)
      }
    }

    if (selectedModel && selectedDevice) {
      updateEmotion()
      intervalId = setInterval(updateEmotion, 2000)
    }

    return () => {
      if (intervalId) {
        clearInterval(intervalId)
      }
    }
  }, [selectedModel, selectedDevice])

  if (!selectedModel || !selectedDevice) {
    return <div className="text-center">请选择模型和设备以开始表情识别</div>
  }

  return (
    <div className="bg-white p-6 rounded-lg shadow-md">
      <h2 className="text-2xl font-bold mb-4">表情识别结果</h2>
      {emotion ? (
        <div className="text-4xl font-bold text-center p-4 bg-gray-100 rounded-lg">
          {emotion}
        </div>
      ) : (
        <div className="text-center">正在分析...</div>
      )}
    </div>
  )
}

