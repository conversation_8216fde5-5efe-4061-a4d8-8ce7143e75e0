'use client'

import { useAuthContext } from '@/contexts/AuthContext'
import { usePathname, useRouter } from 'next/navigation'
import React, { useEffect } from 'react'

const RouteGuard = ({ children }: { children: React.ReactNode }) => {
  const { isLogin, loading } = useAuthContext()
  const router = useRouter()
  const pathname = usePathname()

  // 公开路由列表
  const publicRoutes = ['/login']

  useEffect(() => {
    const checkAuth = () => {
      // 如果还在加载中，不做任何处理
      if (loading) return

      // 检查是否为公开路由
      const isPublicRoute = publicRoutes.includes(pathname)

      // 如果用户未登录且不在公开路由中，重定向到登录页
      if (!isLogin && !isPublicRoute) {
        console.log('用户未登录，跳转登录页面')
        router.push('/login')
        return
      }

      // 如果用户已登录且在登录页，重定向到首页
      if (isLogin && pathname === '/login') {
        console.log('用户已登录且在登录页，自动跳转到首页')
        router.push('/')
        return
      }
    }

    checkAuth()
  }, [isLogin, loading, router, pathname])

  return (
    <>
      {/* 顶部进度条 - 只在loading时显示 */}
      {loading && (
        <div className="fixed top-0 left-0 w-full h-1 bg-gray-200 z-50">
          <div className="h-full bg-gradient-to-r from-blue-500 via-blue-400 to-blue-500 animate-shimmer"></div>
        </div>
      )}

      {/* 始终渲染children，避免重新挂载 */}
      <div className="h-screen w-full relative">{children}</div>
    </>
  )
}

export default RouteGuard
// // 2. 路由守卫组件
// // components/RouteGuard.js
// import { useAuth } from '../contexts/AuthContext';
// import { useRouter } from 'next/router';
// import { useEffect } from 'react';
//
// const RouteGuard = ({ children }) => {
//   const { isAuthenticated, loading } = useAuth();
//   const router = useRouter();
//
//   // 公开路由列表
//   const publicRoutes = ['/login', '/register', '/forgot-password', '/'];
//
//   useEffect(() => {
//     const checkAuth = () => {
//       const { pathname } = router;
//
//       // 如果还在加载中，不做任何处理
//       if (loading) return;
//
//       // 检查是否为公开路由
//       const isPublicRoute = publicRoutes.includes(pathname);
//
//       // 如果用户未登录且不在公开路由中，重定向到登录页
//       if (!isAuthenticated && !isPublicRoute) {
//         router.push('/login');
//         return;
//       }
//
//       // 如果用户已登录且在登录页，重定向到首页
//       if (isAuthenticated && pathname === '/login') {
//         router.push('/dashboard');
//         return;
//       }
//     };
//
//     checkAuth();
//   }, [isAuthenticated, loading, router]);
//
//   // 显示加载状态
//   if (loading) {
//     return (
//       <div className="flex items-center justify-center min-h-screen">
//         <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-500"></div>
//       </div>
//     );
//   }
//
//   return children;
// };
//
// export default RouteGuard;
