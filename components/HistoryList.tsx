'use client'

import { useState, useEffect } from 'react'
import Link from 'next/link'
import { getHistoryVideos, HistoryVideo } from '@/lib/services/historyService'

export default function HistoryList() {
  const [videos, setVideos] = useState<HistoryVideo[]>([])
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    async function fetchVideos() {
      const historyVideos = await getHistoryVideos()
      setVideos(historyVideos)
      setIsLoading(false)
    }
    fetchVideos()
  }, [])

  if (isLoading) {
    return <div className="text-center">加载历史视频...</div>
  }

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      {videos.map((video) => (
        <Link href={`/history/${video.id}`} key={video.id} className="block">
          <div className="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow">
            <img src={video.thumbnailUrl} alt={video.title} className="w-full h-40 object-cover" />
            <div className="p-4">
              <h3 className="font-bold text-lg mb-2">{video.title}</h3>
              <p className="text-gray-600 text-sm">{video.date}</p>
              <p className="text-gray-600 text-sm">{video.duration}</p>
            </div>
          </div>
        </Link>
      ))}
    </div>
  )
}

