'use client'

import * as React from 'react'
import { BarChart3, ChevronDown, Database, Settings, User } from 'lucide-react'
import Link from 'next/link'
import { usePathname } from 'next/navigation'
import { useLayout } from './layout-context'

const sidebarData = {
  navMain: [
    {
      title: '模型训练',
      icon: BarChart3,
      items: [
        {
          title: '任务管理',
          url: '/admin/training/tasks',
        },
        {
          title: '数据集管理',
          url: '/admin/training/datasets',
        },
        {
          title: '模型仓库',
          url: '/admin/training/models',
        },
      ],
    },
    {
      title: '数据标注',
      icon: Database,
      items: [
        {
          title: '标注工作台',
          url: '/admin/annotation/workbench',
        },
        {
          title: '样本采集',
          url: '/admin/annotation/collection',
        },
      ],
    },
    {
      title: '系统设置',
      icon: Settings,
      items: [
        {
          title: '场景管理',
          url: '/admin/settings/scenes',
        },
        {
          title: '通知设置',
          url: '/admin/settings/notifications',
        },
      ],
    },
    {
      title: '用户中心',
      icon: User,
      items: [
        {
          title: '用户管理',
          url: '/admin/user/management',
        },
        {
          title: '角色管理',
          url: '/admin/user/roles',
        },
        {
          title: '操作日志',
          url: '/admin/user/log',
        },
      ],
    },
  ],
}

export default function AppSidebar() {
  const pathname = usePathname()
  const [openGroups, setOpenGroups] = React.useState<string[]>(() => {
    // 初始化时展开所有菜单
    return sidebarData.navMain.map(section => section.title)
  })
  const { isSidebarCollapsed, toggleSidebar } = useLayout()

  const toggleGroup = (title: string) => {
    setOpenGroups(prev => (prev.includes(title) ? prev.filter(t => t !== title) : [...prev, title]))
  }

  return (
    <div
      className={`${isSidebarCollapsed ? 'w-16' : 'w-64'}  h-full bg-white border-r border-gray-100 transition-all duration-300 shadow-sm`}
    >
      <div className="h-full overflow-y-auto">
        <nav className="py-3 px-3">
          {sidebarData.navMain.map(section => {
            const isOpen = openGroups.includes(section.title)
            const hasActiveChild = section.items.some(item => item.url === pathname)

            return (
              <div key={section.title} className="mb-1">
                <button
                  onClick={() => toggleGroup(section.title)}
                  className={`flex items-center w-full px-2 py-2 text-sm transition-all duration-200 rounded-lg
                    ${hasActiveChild ? 'text-blue-600 bg-blue-50 font-semibold' : 'text-gray-700 hover:bg-gray-50 font-medium'}`}
                >
                  <div className="flex items-center min-w-0">
                    <section.icon
                      className={`w-4 h-4 flex-shrink-0 ${!isSidebarCollapsed && 'mr-2'} ${hasActiveChild ? 'text-blue-600' : 'text-gray-500'}`}
                    />
                    {!isSidebarCollapsed && <span className="truncate">{section.title}</span>}
                  </div>
                  {!isSidebarCollapsed && (
                    <ChevronDown
                      className={`w-5 h-5 ml-auto transition-transform duration-200 ${isOpen ? 'rotate-180' : ''}
                      ${hasActiveChild ? 'text-blue-600' : 'text-gray-400'}`}
                    />
                  )}
                </button>
                {isOpen && !isSidebarCollapsed && (
                  <div className="mt-0.5 space-y-0.5">
                    {section.items.map(item => (
                      <Link
                        key={item.title}
                        href={item.url}
                        className={`block px-2 py-1.5 text-sm transition-all duration-200 rounded-lg
                          ${pathname === item.url
                            ? 'text-blue-600 bg-blue-50 font-medium'
                            : 'text-gray-500 hover:bg-gray-50'
                          }`}
                      >
                        <span className="inline-block ml-6">{item.title}</span>
                      </Link>
                    ))}
                  </div>
                )}
              </div>
            )
          })}
        </nav>
      </div>
    </div>
  )
}
