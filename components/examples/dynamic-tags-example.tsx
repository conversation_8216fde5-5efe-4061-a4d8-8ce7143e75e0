'use client'

import { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { DynamicTags } from '@/components/ui/dynamic-tags'

export function DynamicTagsExample() {
  const [tags, setTags] = useState<string[]>(['教师', '程序员'])
  const [limitedTags, setLimitedTags] = useState<string[]>(['教师'])
  const [readOnlyTags, setReadOnlyTags] = useState<string[]>(['教师', '程序员', '地方'])

  return (
    <div className="space-y-8">
      <Card>
        <CardHeader>
          <CardTitle>基础用法</CardTitle>
          <CardDescription>可以动态添加和删除标签，按回车或点击加号添加标签</CardDescription>
        </CardHeader>
        <CardContent>
          <DynamicTags value={tags} onChange={setTags} placeholder="添加标签..." />
          <div className="mt-2 text-sm text-muted-foreground">当前标签: {tags.join(', ')}</div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>限制最大数量</CardTitle>
          <CardDescription>设置maxTags属性可以限制最多添加的标签数量</CardDescription>
        </CardHeader>
        <CardContent>
          <DynamicTags
            value={limitedTags}
            onChange={setLimitedTags}
            placeholder="最多添加3个标签..."
            maxTags={3}
          />
          <div className="mt-2 text-sm text-muted-foreground">
            当前标签: {limitedTags.join(', ')} (已添加 {limitedTags.length}/3)
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>只读模式</CardTitle>
          <CardDescription>设置readOnly属性可以禁止添加和删除标签</CardDescription>
        </CardHeader>
        <CardContent>
          <DynamicTags value={readOnlyTags} onChange={setReadOnlyTags} readOnly />
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>禁用状态</CardTitle>
          <CardDescription>设置disabled属性可以禁用整个组件</CardDescription>
        </CardHeader>
        <CardContent>
          <DynamicTags value={['教师', '程序员']} disabled />
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>自定义样式</CardTitle>
          <CardDescription>
            可以通过className、tagClassName、inputClassName自定义样式
          </CardDescription>
        </CardHeader>
        <CardContent>
          <DynamicTags
            value={['教师', '程序员']}
            onChange={setTags}
            className="border-blue-200 bg-blue-50"
            tagClassName="bg-blue-100 text-blue-800 border-blue-200"
            inputClassName="border-blue-200"
            addButtonClassName="text-blue-600 hover:bg-blue-50"
          />
        </CardContent>
      </Card>
    </div>
  )
}
