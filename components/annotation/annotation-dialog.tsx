import { useState } from 'react'
import { <PERSON><PERSON>, <PERSON>alog<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>er, DialogTitle } from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { DataItem } from '@/types/models/data'
import { ChevronLeft, ChevronRight } from 'lucide-react'
import { cn } from '@/lib/utils'
import { Badge } from '@/components/ui/badge'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'

interface AnnotationDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  data: DataItem[]
  onSave: (id: string, annotation: { emotion: string; description: string }) => void
}

export function AnnotationDialog({ open, onOpenChange, data, onSave }: AnnotationDialogProps) {
  const [currentIndex, setCurrentIndex] = useState(0)
  const [emotion, setEmotion] = useState('')
  const [description, setDescription] = useState('')

  const currentItem = data[currentIndex]

  const handleNext = () => {
    if (currentIndex < data.length - 1) {
      setCurrentIndex(currentIndex + 1)
      setEmotion('')
      setDescription('')
    }
  }

  const handlePrevious = () => {
    if (currentIndex > 0) {
      setCurrentIndex(currentIndex - 1)
      setEmotion('')
      setDescription('')
    }
  }

  const handleSave = () => {
    onSave(currentItem.id, {
      emotion,
      description,
    })
    handleNext()
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl">
        <DialogHeader>
          <DialogTitle>
            标注数据 - {currentIndex + 1}/{data.length}
          </DialogTitle>
        </DialogHeader>
        <div className="grid grid-cols-2 gap-6">
          <div className="relative">
            <div className="aspect-square bg-gray-100 rounded-lg overflow-hidden">
              <img
                src={currentItem?.preview}
                alt={currentItem?.name}
                className="w-full h-full object-cover"
              />
            </div>
            <div className="absolute top-1/2 -translate-y-1/2 left-4">
              <Button
                variant="secondary"
                size="icon"
                onClick={handlePrevious}
                disabled={currentIndex === 0}
              >
                <ChevronLeft className="h-4 w-4" />
              </Button>
            </div>
            <div className="absolute top-1/2 -translate-y-1/2 right-4">
              <Button
                variant="secondary"
                size="icon"
                onClick={handleNext}
                disabled={currentIndex === data.length - 1}
              >
                <ChevronRight className="h-4 w-4" />
              </Button>
            </div>
          </div>
          <div className="space-y-4">
            <div className="space-y-2">
              <Label>标签</Label>
              <div className="flex flex-wrap gap-1">
                {currentItem?.tags && currentItem.tags.length > 0 ? (
                  currentItem.tags.map((tag: string) => (
                    <Badge key={tag} variant="secondary">
                      {tag}
                    </Badge>
                  ))
                ) : (
                  <div className="text-sm text-muted-foreground">未设置标签</div>
                )}
              </div>
            </div>
            <div className="space-y-2">
              <Label htmlFor="emotion">情绪标签</Label>
              <Select value={emotion} onValueChange={setEmotion}>
                <SelectTrigger>
                  <SelectValue placeholder="选择情绪标签" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="happy">高兴</SelectItem>
                  <SelectItem value="sad">悲伤</SelectItem>
                  <SelectItem value="angry">愤怒</SelectItem>
                  <SelectItem value="surprised">惊讶</SelectItem>
                  <SelectItem value="fearful">恐惧</SelectItem>
                  <SelectItem value="disgusted">厌恶</SelectItem>
                  <SelectItem value="neutral">中性</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-2">
              <Label htmlFor="description">详细描述</Label>
              <Textarea
                id="description"
                placeholder="请描述图像中的表情和情绪细节..."
                value={description}
                onChange={e => setDescription(e.target.value)}
                rows={8}
              />
            </div>
            <div className="flex justify-end space-x-2">
              <Button variant="outline" onClick={() => handleNext()}>
                跳过
              </Button>
              <Button onClick={handleSave} disabled={!emotion || !description}>
                保存并继续
              </Button>
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}
