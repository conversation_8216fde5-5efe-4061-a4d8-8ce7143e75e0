{"name": "emotion-recognition-app", "version": "0.1.0", "private": true, "main": "main.js", "homepage": "./", "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "find:unused": "next-unused", "electron": "electron .", "electron-dev": "concurrently \"npm run dev\" \"wait-on http://localhost:3000 && electron .\"", "electron-build": "cross-env ELECTRON_BUILD=true npm run build && electron-builder", "dist": "cross-env ELECTRON_BUILD=true npm run build && electron-builder --publish=never"}, "dependencies": {"@formkit/auto-animate": "^0.8.2", "@hookform/resolvers": "^3.9.1", "@radix-ui/react-accordion": "^1.2.2", "@radix-ui/react-alert-dialog": "^1.1.4", "@radix-ui/react-aspect-ratio": "^1.1.1", "@radix-ui/react-avatar": "^1.1.2", "@radix-ui/react-checkbox": "^1.1.3", "@radix-ui/react-collapsible": "^1.1.2", "@radix-ui/react-context-menu": "^2.2.4", "@radix-ui/react-dialog": "^1.1.4", "@radix-ui/react-dropdown-menu": "^2.1.4", "@radix-ui/react-hover-card": "^1.1.4", "@radix-ui/react-label": "^2.1.1", "@radix-ui/react-menubar": "^1.1.4", "@radix-ui/react-navigation-menu": "^1.2.3", "@radix-ui/react-popover": "^1.1.4", "@radix-ui/react-progress": "^1.1.1", "@radix-ui/react-radio-group": "^1.2.2", "@radix-ui/react-scroll-area": "^1.2.2", "@radix-ui/react-select": "^2.1.4", "@radix-ui/react-separator": "^1.1.1", "@radix-ui/react-slider": "^1.2.2", "@radix-ui/react-slot": "^1.1.1", "@radix-ui/react-switch": "^1.1.2", "@radix-ui/react-tabs": "^1.1.2", "@radix-ui/react-toast": "^1.2.4", "@radix-ui/react-toggle": "^1.1.1", "@radix-ui/react-toggle-group": "^1.1.1", "@radix-ui/react-tooltip": "^1.1.6", "@tanstack/react-table": "^8.20.6", "@types/chart.js": "^2.9.41", "@types/uuid": "^10.0.0", "autoprefixer": "^10.4.20", "axios": "^1.10.0", "bcryptjs": "^2.4.3", "chart.js": "^4.4.8", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "1.0.4", "date-fns": "^4.1.0", "electron-is-dev": "^3.0.1", "electron-updater": "^6.6.2", "embla-carousel-react": "8.5.1", "face-api.js": "^0.22.2", "framer-motion": "^12.5.0", "input-otp": "1.4.1", "js-cookie": "latest", "jsonwebtoken": "^9.0.2", "lodash.debounce": "^4.0.8", "lucide-react": "^0.454.0", "next": "14.2.16", "next-themes": "^0.4.6", "react": "^18", "react-day-picker": "^8.10.1", "react-dom": "^18", "react-hook-form": "^7.54.1", "react-hot-toast": "^2.5.2", "react-resizable-panels": "^2.1.7", "recharts": "latest", "sonner": "^1.7.1", "tailwind-merge": "^2.5.5", "tailwindcss-animate": "^1.0.7", "uuid": "^11.1.0", "vaul": "^0.9.6", "zod": "^3.24.1"}, "devDependencies": {"@faker-js/faker": "^9.5.0", "@types/bcryptjs": "^2.4.6", "@types/js-cookie": "^3.0.6", "@types/lodash.debounce": "^4.0.9", "@types/node": "^22.10.7", "@types/react": "^18", "@types/react-dom": "^18", "@typescript-eslint/eslint-plugin": "^8.21.0", "@typescript-eslint/parser": "^8.21.0", "concurrently": "^9.2.0", "cross-env": "^7.0.3", "electron": "^37.2.0", "electron-builder": "^26.0.12", "eslint": "^8.57.1", "eslint-config-next": "14.1.0", "eslint-config-prettier": "^10.0.1", "eslint-plugin-prettier": "^5.2.3", "eslint-plugin-react": "^7.37.4", "eslint-plugin-react-hooks": "^5.1.0", "next-unused": "^0.0.6", "postcss": "^8", "prettier": "^3.4.2", "tailwindcss": "^3.4.17", "ts-node": "^10.9.2", "typescript": "^5", "wait-on": "^8.0.3"}, "build": {"appId": "com.emotionrecognition.app", "productName": "Emotion Recognition App", "directories": {"output": "dist"}, "files": ["main.js", "preload.js", "out/**/*", "backend/**/*", "node_modules/**/*", "public/models/**/*"], "mac": {"target": "dmg", "icon": "assets/icon.icns"}, "win": {"target": "nsis", "icon": "assets/icon.ico"}, "linux": {"target": "AppImage", "icon": "assets/icon.png"}, "publish": {"provider": "github", "owner": "your-username", "repo": "emotion-recognition-app"}}}