const { app, BrowserWindow, ipcMain, dialog, shell } = require('electron')
const { autoUpdater } = require('electron-updater')
const path = require('path')
const isDev = require('electron-is-dev')
const { spawn } = require('child_process')
const fs = require('fs')

let mainWindow
let nextjsServer
let nextjsServerReady = false

// 开发环境配置
const DEV_PORT = 3000
const JAVA_PORT = 8080

// 创建主窗口
function createWindow() {
  mainWindow = new BrowserWindow({
    width: 1200,
    height: 800,

    // frame: false,
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      enableRemoteModule: false,
      preload: path.join(__dirname, 'preload.js'),
    },
    icon: path.join(__dirname, 'assets/icon.png'),
    show: false,
  })

  // 加载应用
  const startUrl = isDev
    ? `http://localhost:${DEV_PORT}`
    : `file://${path.join(__dirname, '../out/index.html')}`

  mainWindow.loadURL(startUrl)

  // 开发工具
  if (isDev) {
    mainWindow.webContents.openDevTools()
  }

  // 窗口就绪后显示
  mainWindow.once('ready-to-show', () => {
    mainWindow.show()

    // 检查更新
    if (!isDev) {
      autoUpdater.checkForUpdatesAndNotify()
    }
  })

  // 处理外部链接
  mainWindow.webContents.setWindowOpenHandler(({ url }) => {
    shell.openExternal(url)
    return { action: 'deny' }
  })

  mainWindow.on('closed', () => {
    mainWindow = null
  })
}

// 启动 Next.js 服务器
async function startNextjsServer() {
  if (isDev) {
    // 开发环境：等待 Next.js 开发服务器启动
    const waitOn = require('wait-on')
    try {
      await waitOn({
        resources: [`http://localhost:${DEV_PORT}`],
        timeout: 30000,
      })
      nextjsServerReady = true
      console.log('Next.js 开发服务器已就绪')
    } catch (error) {
      console.error('等待 Next.js 开发服务器启动超时:', error)
    }
  } else {
    // 生产环境：启动 Next.js 服务器
    const nextPath = path.join(__dirname, 'node_modules/.bin/next')
    const nextArgs = ['start', '-p', DEV_PORT.toString()]

    nextjsServer = spawn(nextPath, nextArgs, {
      cwd: __dirname,
      stdio: 'inherit',
    })

    nextjsServer.on('error', error => {
      console.error('Next.js 服务器启动失败:', error)
    })

    // 等待服务器启动
    const waitOn = require('wait-on')
    try {
      await waitOn({
        resources: [`http://localhost:${DEV_PORT}`],
        timeout: 30000,
      })
      nextjsServerReady = true
      console.log('Next.js 服务器已启动')
    } catch (error) {
      console.error('Next.js 服务器启动超时:', error)
    }
  }
}

// 创建用户数据目录
function createUserDataDirectories() {
  const userDataPath = app.getPath('userData')
  const directories = [
    path.join(userDataPath, 'database'),
    path.join(userDataPath, 'files'),
    path.join(userDataPath, 'models'),
    path.join(userDataPath, 'logs'),
  ]

  directories.forEach(dir => {
    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true })
    }
  })
}

// 应用事件处理
app.whenReady().then(async () => {
  createUserDataDirectories()

  // 并行启动服务
  await Promise.all([startNextjsServer()])

  createWindow()

  app.on('activate', () => {
    if (BrowserWindow.getAllWindows().length === 0) {
      createWindow()
    }
  })
})

app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    app.quit()
  }
})

app.on('before-quit', () => {
  // 清理资源
  if (nextjsServer) {
    nextjsServer.kill()
  }
  if (javaBackendProcess) {
    javaBackendProcess.kill()
  }
})

// 自动更新事件
autoUpdater.on('checking-for-update', () => {
  console.log('检查更新中...')
})

autoUpdater.on('update-available', info => {
  console.log('发现新版本')
  dialog.showMessageBox(mainWindow, {
    type: 'info',
    title: '更新可用',
    message: '发现新版本，将在后台下载更新。',
    buttons: ['确定'],
  })
})

autoUpdater.on('update-not-available', info => {
  console.log('当前已是最新版本')
})

autoUpdater.on('error', err => {
  console.log('更新错误:', err)
})

autoUpdater.on('download-progress', progressObj => {
  let log_message = '下载速度: ' + progressObj.bytesPerSecond
  log_message = log_message + ' - 已下载 ' + progressObj.percent + '%'
  log_message = log_message + ' (' + progressObj.transferred + '/' + progressObj.total + ')'
  console.log(log_message)
})

autoUpdater.on('update-downloaded', info => {
  console.log('更新下载完成')
  dialog
    .showMessageBox(mainWindow, {
      type: 'info',
      title: '更新就绪',
      message: '更新已下载完成，应用将重启以应用更新。',
      buttons: ['立即重启', '稍后重启'],
    })
    .then(result => {
      if (result.response === 0) {
        autoUpdater.quitAndInstall()
      }
    })
})

// IPC 处理
ipcMain.handle('get-app-version', () => {
  return app.getVersion()
})

ipcMain.handle('get-user-data-path', () => {
  return app.getPath('userData')
})

// 窗口控制 IPC 处理
ipcMain.handle('window-minimize', () => {
  if (mainWindow) {
    mainWindow.minimize()
  }
})

ipcMain.handle('window-maximize', () => {
  if (mainWindow) {
    if (mainWindow.isMaximized()) {
      mainWindow.restore()
    } else {
      mainWindow.maximize()
    }
  }
})

ipcMain.handle('window-close', () => {
  if (mainWindow) {
    mainWindow.close()
  }
})

ipcMain.handle('is-window-maximized', () => {
  return mainWindow ? mainWindow.isMaximized() : false
})

// 确认对话框 IPC 处理
ipcMain.handle('show-confirm-dialog', async (event, options) => {
  const {
    title = '确认',
    message,
    confirmText = '确定',
    cancelText = '取消',
    type = 'question',
  } = options

  // 根据类型设置对话框图标
  let dialogType = 'none'
  switch (type) {
    case 'info':
      dialogType = 'info'
      break
    case 'warning':
      dialogType = 'warning'
      break
    case 'error':
      dialogType = 'error'
      break
    case 'question':
      dialogType = 'question'
      break
    default:
      dialogType = 'none'
  }

  try {
    const result = await dialog.showMessageBox(mainWindow, {
      type: dialogType,
      title,
      message,
      detail: message,
      buttons: [confirmText, cancelText],
      defaultId: 1, // 默认选中取消按钮
      cancelId: 1, // 按 ESC 键时选择取消
    })

    // 返回 true 表示用户点击了确认按钮
    return result.response === 0
  } catch (error) {
    console.error('显示确认对话框失败:', error)
    return false
  }
})
