# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is an Emotion Recognition App built as an Electron desktop application with Next.js as the frontend framework. The app provides facial emotion recognition capabilities through computer vision models and includes comprehensive data annotation, training, and management features.

## Development Commands

### Core Development
- `npm run dev` - Start Next.js development server (port 3000)
- `npm run build` - Build Next.js application for production
- `npm run start` - Start Next.js production server
- `npm run lint` - Run ESLint for code quality checks

### Electron Development
- `npm run electron` - Start Electron app (requires Next.js server running)
- `npm run electron-dev` - Start both Next.js dev server and Electron concurrently
- `npm run electron-build` - Build and package Electron app for production
- `npm run dist` - Build Next.js and package Electron app for distribution

### Utilities
- `npm run find:unused` - Find unused components and files with next-unused

## Architecture Overview

### Tech Stack
- **Frontend**: Next.js 14 with <PERSON>pp Router, React 18, TypeScript
- **UI Components**: Radix UI primitives with shadcn/ui components
- **Styling**: Tailwind CSS with custom animations and themes
- **Desktop**: Electron with secure preload scripts
- **Data Fetching**: Axios for HTTP requests
- **Forms**: React Hook Form with Zod validation
- **Charts**: Chart.js and Recharts for data visualization
- **Computer Vision**: face-api.js for facial recognition

### Project Structure
- `app/` - Next.js App Router pages and layouts
- `components/` - Reusable UI components organized by type/function
- `contexts/` - React contexts for global state (Auth, App)
- `lib/` - Utility functions, API clients, and type definitions
- `services/` - Service layer for API interactions
- `hooks/` - Custom React hooks
- `public/models/` - Face recognition model files
- `main.js` - Electron main process
- `preload.js` - Electron preload script for secure IPC

### Key Features
1. **Emotion Recognition**: Real-time facial emotion detection using face-api.js
2. **Data Annotation**: Image and video annotation tools with timeline scrubbing
3. **Training Management**: Dataset management, model training tasks, and progress tracking
4. **User Management**: Authentication, roles, and permissions
5. **Admin Dashboard**: Analytics, user management, and system settings
6. **Stream Processing**: Live camera feed emotion analysis

## Backend Integration

The app integrates with Java backend services on port 8080. API routes are proxied through Next.js:
- `/api/:path*` → `http://localhost:8080/api/:path*`
- `/me-detection/:path*` → `http://localhost:8000/me-detection/:path*`

## Electron Configuration

- Main process handles window management, auto-updates, and system integration
- Secure configuration with `nodeIntegration: false` and `contextIsolation: true`
- User data stored in OS-specific directories under `~/.emotion-recognition-app/`
- Supports auto-updates via electron-updater and GitHub releases

## Development Guidelines

### Code Style (from .cursorrules)
- Use TypeScript throughout; prefer interfaces over types
- Functional and declarative programming patterns
- Named exports for components
- Directory naming: lowercase with hyphens
- Component naming: PascalCase (e.g., `NewComponent.tsx`)

### Component Organization
- `components/ui/` - Base UI components (buttons, forms, etc.)
- `components/[feature]/` - Feature-specific components
- `app/[route]/_components/` - Page-specific components
- Use shadcn/ui components as foundation

### Performance
- Use Suspense for loading states
- Dynamic imports for non-critical components
- Optimize images with WebP format and lazy loading

## Testing and Quality

Run linting before committing changes:
```bash
npm run lint
```

The project uses ESLint with TypeScript, React, and Prettier configurations for code quality.

## Model Files

Face recognition models are stored in `public/models/`:
- Face detection: `tiny_face_detector_model-*`
- Landmarks: `face_landmark_68_model-*`
- Recognition: `face_recognition_model-*`
- Expressions: `face_expression_model-*`

These are loaded dynamically by face-api.js for emotion recognition features.
