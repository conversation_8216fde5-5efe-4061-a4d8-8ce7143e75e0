{"extends": ["next/core-web-vitals", "plugin:@typescript-eslint/recommended", "prettier"], "plugins": ["@typescript-eslint", "prettier"], "rules": {"prettier/prettier": ["error", {}, {"usePrettierrc": true}], "@typescript-eslint/no-unused-vars": ["warn", {"argsIgnorePattern": "^_"}], "@typescript-eslint/no-explicit-any": "off", "@typescript-eslint/ban-ts-comment": "off", "no-console": ["warn", {"allow": ["warn", "error"]}]}}