import { useAuthContext } from '@/contexts/AuthContext'
import { useRouter } from 'next/navigation'
import { useEffect } from 'react'

export const useRequireAuth = (redirectTo = '/login') => {
  const { isLogin } = useAuthContext()
  const router = useRouter()

  useEffect(() => {
    if (!isLogin) {
      router.push(redirectTo)
    }
  }, [isLogin, router, redirectTo])
  return { isLogin }
}

// // 4. 页面级别的认证Hook
// // hooks/useRequireAuth.js
// import { useEffect } from 'react';
// import { useAuth } from '../contexts/AuthContext';
// import { useRouter } from 'next/router';
//
// export const useRequireAuth = (redirectTo = '/login') => {
//   const { isAuthenticated, loading } = useAuth();
//   const router = useRouter();
//
//   useEffect(() => {
//     if (!loading && !isAuthenticated) {
//       router.push(redirectTo);
//     }
//   }, [isAuthenticated, loading, router, redirectTo]);
//
//   return { isAuthenticated, loading };
// };
