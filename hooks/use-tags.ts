'use client'

import { useCallback, useEffect, useState } from 'react'
import { materialTagService } from '@/services/materialTagService'

export interface TagOption {
  value: string
  label: string
}

interface UseTagsResult {
  tags: TagOption[]
  loading: boolean
  error: Error | null
  refetch: () => Promise<void>
  addTag: (tag: TagOption) => void
}

// 全局标签缓存
let globalTagsCache: TagOption[] = []
let globalLoadingState = false
let globalError: Error | null = null
const listeners = new Set<() => void>()

// 通知所有监听器
const notifyListeners = () => {
  listeners.forEach(listener => listener())
}

// 从服务器获取标签列表
const fetchTags = async (): Promise<TagOption[]> => {
  if (globalLoadingState) {
    // 如果正在加载，等待加载完成
    return new Promise(resolve => {
      const checkLoading = () => {
        if (!globalLoadingState) {
          resolve(globalTagsCache)
        } else {
          setTimeout(checkLoading, 50)
        }
      }
      checkLoading()
    })
  }

  if (globalTagsCache.length > 0) {
    return globalTagsCache
  }

  globalLoadingState = true
  globalError = null
  notifyListeners()

  try {
    const records = await materialTagService.listTags()

    // 检查返回数据格式
    if (records && typeof records === 'object') {
      globalTagsCache = Object.entries(records).map(([k, v]) => ({
        value: v as string,
        label: v as string,
      }))
    } else {
      console.warn('Invalid tags data format:', records)
      // 如果API返回格式不对，提供一些默认测试数据
      globalTagsCache = []
    }

    globalError = null
  } catch (error) {
    console.error('Failed to fetch tags:', error) // 调试日志
    globalError = error instanceof Error ? error : new Error('Failed to fetch tags')
    // 网络错误时也提供测试数据
    globalTagsCache = []
  } finally {
    globalLoadingState = false
    notifyListeners()
  }

  return globalTagsCache
}

export const useTags = (): UseTagsResult => {
  const [tags, setTags] = useState<TagOption[]>(globalTagsCache)
  const [loading, setLoading] = useState(globalLoadingState)
  const [error, setError] = useState<Error | null>(globalError)

  // 更新本地状态的监听器
  const updateState = useCallback(() => {
    setTags([...globalTagsCache])
    setLoading(globalLoadingState)
    setError(globalError)
  }, [])

  useEffect(() => {
    // 初始化状态
    setTags([...globalTagsCache])
    setLoading(globalLoadingState)
    setError(globalError)

    // 注册监听器
    listeners.add(updateState)

    // 如果还没有数据，触发加载
    if (globalTagsCache.length === 0 && !globalLoadingState) {
      fetchTags()
    }

    // 清理监听器
    return () => {
      listeners.delete(updateState)
    }
  }, [updateState])

  const refetch = useCallback(async () => {
    globalTagsCache = []
    await fetchTags()
  }, [])

  const addTag = useCallback((newTag: TagOption) => {
    // 检查是否已存在相同的标签
    const existingTag = globalTagsCache.find(tag => tag.value === newTag.value)
    if (!existingTag) {
      globalTagsCache = [...globalTagsCache, newTag]
      console.log('Added new tag to global cache:', newTag)
      notifyListeners()
    } else {
    }
  }, [])

  return {
    tags,
    loading,
    error,
    refetch,
    addTag,
  }
}
