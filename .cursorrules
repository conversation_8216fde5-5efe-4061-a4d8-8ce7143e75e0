- 你是一位精通 TypeScript、Node.js、Next.js App Router、React、Shadcn UI、Tailwind 和 Framer Motion 的专家。

- 代码风格和结构

  - 编写简洁、技术性的 TypeScript 代码，提供准确的示例。
  - 使用函数式和声明式编程模式；避免使用类。
  - 优先考虑迭代和模块化，避免代码重复。
  - 使用描述性的变量名，包含辅助动词（如 isLoading、hasError）。
  - 文件结构：导出组件、子组件、辅助函数、静态内容、类型。

- 命名规范

  - 所有组件都应放在 components 目录下，命名如 NewComponent.tsx
  - 目录使用小写字母和连字符（如 components/auth-wizard）。
  - 优先使用命名导出组件。

- TypeScript 使用规范

  - 所有代码都使用 TypeScript；优先使用接口而不是类型。
  - 避免使用枚举；使用映射对象代替。
  - 使用带有 TypeScript 接口的函数组件。

- 语法和格式

  - 使用 "function" 关键字定义纯函数。
  - 避免不必要的花括号；对简单语句使用简洁语法。
  - 使用声明式 JSX。

- UI 和样式

  - 使用 Shadcn UI 和 Tailwind 进行组件开发和样式设计。
  - 使用 Tailwind CSS 实现响应式设计；采用移动优先方法。

- 性能优化

  - 最小化使用 'use client'、'useEffect' 和 'setState'；优先使用 React 服务器组件（RSC）。
  - 使用 Suspense 包装客户端组件，并提供 fallback。
  - 对非关键组件使用动态加载。
  - 图片优化：使用 WebP 格式，包含尺寸数据，实现懒加载。

- 关键约定

  - 使用 'nuqs' 管理 URL 搜索参数状态。
  - 优化网页性能指标（LCP、CLS、FID）。
  - 限制 'use client' 使用：
    - 优先使用服务器组件和 Next.js SSR。
    - 仅在小型组件中访问 Web API。
    - 避免用于数据获取或状态管理。
  - 遵循 Next.js 文档进行数据获取、渲染和路由。
  - 创建占位图片时使用 https://picsum.photos/seed/picsum/200/200
  - 在应用逻辑（/app）和 UI 组件（/components）之间提供清晰的分离

## 组件组织

在 components 文件夹内，按类型或功能组织组件：

按类型：组织表单、按钮、布局元素等。

按功能：对于大型应用，按特定功能或领域组织组件。

例如：

components
├── /ui
│ ├── /Button
│ ├── /Modal
│ └── /Card
├── /forms
│ ├── /TextField
│ └── /Select
└── /layout
├── /Navbar
└── /Footer

- 私有组件：对于仅在特定页面使用的组件，可以在相关 /app 子目录中创建 \_components 文件夹。

- 共享组件：components 文件夹应包含在多个页面或功能中重用的组件。

- 模块化方法：随着项目增长，考虑采用更模块化的结构，每个功能或领域都有自己的文件夹，包含该功能特定的组件、钩子和工具。
