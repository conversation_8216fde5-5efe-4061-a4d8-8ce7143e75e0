'use client'

import { createContext, ReactN<PERSON>, useContext, useEffect, useState } from 'react'
import { userService } from '@/services/userService'
import { useRouter } from 'next/navigation'

interface LoginParams {
  userId: string
  password: string
  validateCode: string
  redirectUrl: string
}

export interface UserInfo {
  id: number
  userId: string
  password: string
  realName: string
  phoneNumber: string
  birthday: string
  gender: number
  email: string
  idcard: string
  locked: boolean
  createUser: string
  expireTime: string
  createTime: string
  updateTime: string
}

interface AuthContextType {
  user: UserInfo | null
  isLogin: boolean
  loading: boolean
  login: (credentials: LoginParams) => Promise<void>
  logout: () => Promise<void>
}

const AuthContext = createContext<AuthContextType | undefined>(undefined)

export const useAuthContext = () => {
  const context = useContext(AuthContext)
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  return context
}

export const AuthProvider = ({ children }: { children: ReactNode }) => {
  const [isLogin, setIsLogin] = useState(false)
  const [loading, setLoading] = useState(true)
  const [user, setUser] = useState<UserInfo | null>(null)
  const router = useRouter()

  const login = async (credentials: LoginParams) => {
    setLoading(true)
    try {
      // 调用登录接口
      const { userId, password, validateCode, redirectUrl } = credentials
      const { success, message, userInfo } = await userService.login({
        userId,
        password,
        validateCode,
      })
      if (success) {
        console.log('用户登录成功')
        setUser(userInfo)
        setIsLogin(true)
        // 跳转到指定页面
        router.push(redirectUrl)
      } else {
        console.log('用户登录失败')
        setIsLogin(false)
        // 抛出错误以便在组件中处理
        throw new Error(message || '登录失败')
      }
    } catch (error: any) {
      setIsLogin(false)
      // 重新抛出错误以便在组件中处理
      throw error
    } finally {
      setLoading(false)
    }
  }

  const logout = async () => {
    setLoading(true)
    try {
      await userService.logout()
    } finally {
      setUser(null)
      setIsLogin(false)
      setLoading(false)
    }
  }

  // 页面初始化时，判断用户是否登录，并加载用户信息
  useEffect(() => {
    const initializeAuth = async () => {
      try {
        const userInfo = await userService.fetchUserInfo()
        console.log('用户信息加载成功', userInfo)
        // setIsLogin(!!userInfo)
        setIsLogin(true)
      } catch (error) {
        console.error('Error checking authentication:', error)
        setIsLogin(false)
      } finally {
        setLoading(false)
      }
    }

    initializeAuth()
  }, [])

  const value = {
    user,
    isLogin,
    loading,
    login,
    logout,
  }
  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>
}

// // 1. 认证Context和Hook
// // contexts/AuthContext.js
// import React, { createContext, useContext, useState, useEffect } from 'react';
// import { useRouter } from 'next/router';
//
// const AuthContext = createContext();
//
// export const useAuth = () => {
//   const context = useContext(AuthContext);
//   if (!context) {
//     throw new Error('useAuth must be used within an AuthProvider');
//   }
//   return context;
// };
//
// export const AuthProvider = ({ children }) => {
//   const [user, setUser] = useState(null);
//   const [loading, setLoading] = useState(true);
//   const [isAuthenticated, setIsAuthenticated] = useState(false);
//   const router = useRouter();
//
//   // 检查Token有效性
//   const checkTokenValidity = async (token) => {
//     try {
//       const response = await fetch('/api/verify-token', {
//         headers: {
//           'Authorization': `Bearer ${token}`
//         }
//       });
//       return response.ok;
//     } catch (error) {
//       console.error('Token validation failed:', error);
//       return false;
//     }
//   };
//
//   // 获取用户信息
//   const fetchUserInfo = async (token) => {
//     try {
//       const response = await fetch('/api/user/profile', {
//         headers: {
//           'Authorization': `Bearer ${token}`
//         }
//       });
//
//       if (response.ok) {
//         const userData = await response.json();
//         return userData;
//       }
//       return null;
//     } catch (error) {
//       console.error('Failed to fetch user info:', error);
//       return null;
//     }
//   };
//
//   // 初始化认证状态
//   useEffect(() => {
//     const initializeAuth = async () => {
//       try {
//         const token = localStorage.getItem('token');
//         const refreshToken = localStorage.getItem('refreshToken');
//
//         if (!token) {
//           setLoading(false);
//           return;
//         }
//
//         // 验证Token
//         const isValid = await checkTokenValidity(token);
//
//         if (isValid) {
//           // Token有效，获取用户信息
//           const userData = await fetchUserInfo(token);
//           if (userData) {
//             setUser(userData);
//             setIsAuthenticated(true);
//           }
//         } else if (refreshToken) {
//           // Token过期，尝试刷新
//           const success = await refreshAccessToken(refreshToken);
//           if (!success) {
//             logout();
//           }
//         } else {
//           // 无有效Token，清除本地存储
//           logout();
//         }
//       } catch (error) {
//         console.error('Auth initialization failed:', error);
//         logout();
//       } finally {
//         setLoading(false);
//       }
//     };
//
//     initializeAuth();
//   }, []);
//
//   // 登录
//   const login = async (credentials) => {
//     try {
//       const response = await fetch('/api/auth/login', {
//         method: 'POST',
//         headers: {
//           'Content-Type': 'application/json',
//         },
//         body: JSON.stringify(credentials),
//       });
//
//       if (response.ok) {
//         const data = await response.json();
//         const { token, refreshToken, user } = data;
//
//         // 存储Token
//         localStorage.setItem('token', token);
//         localStorage.setItem('refreshToken', refreshToken);
//
//         // 更新状态
//         setUser(user);
//         setIsAuthenticated(true);
//
//         return { success: true, user };
//       } else {
//         const error = await response.json();
//         return { success: false, error: error.message };
//       }
//     } catch (error) {
//       return { success: false, error: error.message };
//     }
//   };
//
//   // 刷新Token
//   const refreshAccessToken = async (refreshToken) => {
//     try {
//       const response = await fetch('/api/auth/refresh', {
//         method: 'POST',
//         headers: {
//           'Content-Type': 'application/json',
//         },
//         body: JSON.stringify({ refreshToken }),
//       });
//
//       if (response.ok) {
//         const data = await response.json();
//         const { token, user } = data;
//
//         localStorage.setItem('token', token);
//         setUser(user);
//         setIsAuthenticated(true);
//         return true;
//       } else {
//         return false;
//       }
//     } catch (error) {
//       console.error('Token refresh failed:', error);
//       return false;
//     }
//   };
//
//   // 登出
//   const logout = () => {
//     localStorage.removeItem('token');
//     localStorage.removeItem('refreshToken');
//     setUser(null);
//     setIsAuthenticated(false);
//     router.push('/login');
//   };
//
//   const value = {
//     user,
//     loading,
//     isAuthenticated,
//     login,
//     logout,
//     refreshAccessToken,
//   };
//
//   return (
//     <AuthContext.Provider value={value}>
//       {children}
//     </AuthContext.Provider>
//   );
// };
