'use client'

import React, { createContext, ReactNode, useContext, useState } from 'react'

interface AppContextType {
  selectedModel: string
  setSelectedModel: (model: string) => void
  selectedDevice: string
  setSelectedDevice: (device: string) => void
}

const AppContext = createContext<AppContextType | undefined>(undefined)

export function AppProvider({ children }: { children: ReactNode }) {
  const [selectedModel, setSelectedModel] = useState<string>('model1')
  const [selectedDevice, setSelectedDevice] = useState<string>('cpu')

  return (
    <AppContext.Provider
      value={{
        selectedModel,
        setSelectedModel,
        selectedDevice,
        setSelectedDevice,
      }}
    >
      {children}
    </AppContext.Provider>
  )
}

export function useAppContext() {
  const context = useContext(AppContext)
  if (context === undefined) {
    throw new Error('useAppContext must be used within an AppProvider')
  }
  return context
}
