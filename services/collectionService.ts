import { httpClient } from '@/utils/HttpClient'

export interface DataItem {
  id: number
  itemId: string
  itemName: string
  itemType: string
  itemSource: string
  expressionType: string
  extParams: string
  tags: string
  itemStatus: string
  filePath: string
  createTime: number
  updateTime: number
}

interface UploadCollectionParams {
  files: File[]
}

class CollectionService {
  // 上传文件
  async uploadFiles(
    params: UploadCollectionParams,
    onProgress?: (progress: number) => void
  ): Promise<void> {
    const formData = new FormData()
    formData.append('file', params.files[0])

    const res = await httpClient.post('/data-item/upload', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
      timeout: 0,
      onUploadProgress: progressEvent => {
        if (progressEvent.total) {
          const progress = Math.round((progressEvent.loaded * 100) / progressEvent.total)
          if (onProgress) {
            onProgress(progress)
          }
        }
      },
    })
  }

  async deleteDataItems(ids: string[]) {
    await httpClient.post(`/data-item/delete`, {
      itemIds: ids,
    })
  }

  async updateTag(itemId: string, tags: string) {
    await httpClient.post(`/data-item/update`, {
      itemId,
      tags,
    })
  }

  async exportDataItems(ids: string[]) {
    await httpClient.downloadPost(
      '/data-item/export',
      {
        itemIds: ids,
      },
      `data-items-${Date.now()}.zip`
    )
  }
}

export const collectionService = new CollectionService()
