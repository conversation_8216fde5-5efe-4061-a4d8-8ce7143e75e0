import { httpClient } from '@/utils/HttpClient'

interface AnnotationSubmitResult {
  expressionType: string
  extParams?: {
    [key: string]: string
  }
}

export interface AnnotationSubmitParams {
  itemId: string
  results: [AnnotationSubmitResult]
}

class AnnotationService {
  async submit({ itemId, results }: AnnotationSubmitParams) {
    await httpClient.post(`/annotation/submit`, {
      itemId,
      results,
    })
  }
}

export const annotationService = new AnnotationService()
