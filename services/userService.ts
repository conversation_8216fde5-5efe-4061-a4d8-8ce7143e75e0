import { httpClient } from '@/utils/HttpClient'
import { UserInfo } from '@/contexts/AuthContext'

interface LoginParams {
  userId: string
  password: string
  validateCode: string
}

export interface LoginResponse {
  success: boolean
  message?: string
  userInfo: UserInfo | null
}

class UserService {
  // 登录
  async login(params: LoginParams): Promise<LoginResponse> {
    try {
      const res = await httpClient.post('/authorization/auth/login', params)
      return { success: true, userInfo: res }
    } catch (error: any) {
      return { success: false, message: '登录失败，' + (error?.message || error), userInfo: null }
    }
  }

  // 退出登录
  async logout() {
    try {
      await httpClient.get('/authorization/auth/quit')
    } catch (error) {
      console.error('Error logging out:', error)
    }
  }

  // 获取用户信息
  async fetchUserInfo() {
    try {
      return await httpClient.get('/authorization/auth/sso')
    } catch (error) {
      return null
    }
  }
}

export const userService = new UserService()
