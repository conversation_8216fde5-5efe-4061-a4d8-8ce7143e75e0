import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios'
import { toast } from '@/components/ui/use-toast'

interface HttpClientConfig {
  baseURL?: string
  timeout?: number
  headers?: Record<string, string>
}

interface ApiResponse<T = any> {
  code: string
  data: T
  message: string
}

class HttpClient {
  private instance: AxiosInstance
  private readonly baseURL: string

  constructor(config: HttpClientConfig) {
    this.baseURL = config.baseURL || process.env.NEXT_PUBLIC_API_BASE_URL || '/api'
    this.instance = axios.create({
      baseURL: this.baseURL,
      timeout: config.timeout || 10000,
      headers: {
        'Content-Type': 'application/json',
        ...config.headers,
      },
    })
    this.setupInterceptors()
  }

  private setupInterceptors() {
    // 请求拦截器
    this.instance.interceptors.request.use(config => {
      config.withCredentials = true
      return config
    })
    // 响应拦截器
    this.instance.interceptors.response.use(
      (response: AxiosResponse<ApiResponse>) => {
        const { data } = response
        // 处理业务逻辑状态码
        if (data.code === '0') {
          return response
        } else if (data.code === 'E1000') {
          // 用户未登录
          this.handleUnauthorized()
        } else {
          return Promise.reject(data.message)
        }
        return Promise.reject('')
      },
      error => {
        console.log('[HTTP] Response error: ', error)

        // 处理 HTTP 状态码错误
        if (error.response) {
          const { status, data } = error.response
          switch (status) {
            case 401:
              this.handleUnauthorized()
              break
            default:
              toast({
                title: '请求失败',
                description: data?.message || '网络错误',
              })
          }
        } else if (error.request) {
          toast({
            title: '请求失败',
            variant: 'destructive',
            description: '网络连接错误',
          })
        } else {
          toast({
            title: '请求失败',
            variant: 'destructive',
            description: '请求配置错误',
          })
        }
      }
    )
  }

  private handleUnauthorized() {
    // TODO 触发 Electron 未登录处理流程
  }

  // GET 请求
  async get<T = any>(url: string, config?: AxiosRequestConfig): Promise<T> {
    const response = await this.instance.get<ApiResponse<T>>(url, config)
    return response.data.data
  }

  // POST 请求
  async post<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> {
    const response = await this.instance.post<ApiResponse<T>>(url, data, config)
    return response.data.data
  }

  // PUT 请求
  async put<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> {
    const response = await this.instance.put<ApiResponse<T>>(url, data, config)
    return response.data.data
  }

  // DELETE 请求
  async delete<T = any>(url: string, config?: AxiosRequestConfig): Promise<T> {
    const response = await this.instance.delete<ApiResponse<T>>(url, config)
    return response.data.data
  }

  // 上传文件
  async upload<T = any>(
    url: string,
    file: File,
    onProgress?: (progress: number) => void
  ): Promise<T> {
    const formData = new FormData()
    formData.append('file', file)

    const response = await this.instance.post<ApiResponse<T>>(url, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
      onUploadProgress: progressEvent => {
        if (onProgress && progressEvent.total) {
          const progress = Math.round((progressEvent.loaded * 100) / progressEvent.total)
          onProgress(progress)
        }
      },
    })

    return response.data.data
  }

  // 下载文件
  async download(url: string, filename?: string): Promise<void> {
    const response = await this.instance.get(url, {
      responseType: 'blob',
    })

    const blob = new Blob([response.data])
    const downloadUrl = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = downloadUrl
    link.download = filename || 'download'
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    window.URL.revokeObjectURL(downloadUrl)
  }

  // 下载文件（POST 请求）
  async downloadPost(url: string, data?: any, filename?: string): Promise<void> {
    const response = await this.instance.post(url, data, {
      responseType: 'blob',
    })

    const blob = new Blob([response.data])
    const downloadUrl = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = downloadUrl
    link.download = filename || 'download'
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    window.URL.revokeObjectURL(downloadUrl)
  }

  // 取消请求
  createCancelToken() {
    return axios.CancelToken.source()
  }
}

// 创建默认实例
export const httpClient = new HttpClient({
  baseURL: '/me-detection',
})
