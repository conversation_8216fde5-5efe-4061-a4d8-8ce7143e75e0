// // 3. API请求拦截器
// // utils/apiClient.js
// class ApiClient {
//   constructor() {
//     this.baseURL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3000';
//   }
//
//   // 获取Token
//   getToken() {
//     return localStorage.getItem('token');
//   }
//
//   // 设置Token
//   setToken(token) {
//     localStorage.setItem('token', token);
//   }
//
//   // 清除Token
//   clearToken() {
//     localStorage.removeItem('token');
//     localStorage.removeItem('refreshToken');
//   }
//
//   // 通用请求方法
//   async request(endpoint, options = {}) {
//     const url = `${this.baseURL}${endpoint}`;
//     const token = this.getToken();
//
//     const config = {
//       headers: {
//         'Content-Type': 'application/json',
//         ...(token && { Authorization: `Bearer ${token}` }),
//         ...options.headers,
//       },
//       ...options,
//     };
//
//     try {
//       const response = await fetch(url, config);
//
//       // 处理401未授权错误
//       if (response.status === 401) {
//         await this.handleUnauthorized();
//         return { success: false, error: 'Unauthorized' };
//       }
//
//       if (!response.ok) {
//         const error = await response.json();
//         throw new Error(error.message || 'Request failed');
//       }
//
//       const data = await response.json();
//       return { success: true, data };
//     } catch (error) {
//       console.error('API request failed:', error);
//       return { success: false, error: error.message };
//     }
//   }
//
//   // 处理401未授权
//   async handleUnauthorized() {
//     const refreshToken = localStorage.getItem('refreshToken');
//
//     if (refreshToken) {
//       // 尝试刷新Token
//       const refreshResult = await this.refreshToken(refreshToken);
//
//       if (refreshResult.success) {
//         return;
//       }
//     }
//
//     // 刷新失败，清除Token并跳转登录页
//     this.clearToken();
//
//     // 使用事件通知其他组件用户需要重新登录
//     window.dispatchEvent(new Event('auth:logout'));
//
//     // 跳转到登录页
//     if (typeof window !== 'undefined') {
//       window.location.href = '/login';
//     }
//   }
//
//   // 刷新Token
//   async refreshToken(refreshToken) {
//     try {
//       const response = await fetch(`${this.baseURL}/api/auth/refresh`, {
//         method: 'POST',
//         headers: {
//           'Content-Type': 'application/json',
//         },
//         body: JSON.stringify({ refreshToken }),
//       });
//
//       if (response.ok) {
//         const data = await response.json();
//         this.setToken(data.token);
//         return { success: true, data };
//       }
//
//       return { success: false };
//     } catch (error) {
//       return { success: false, error: error.message };
//     }
//   }
//
//   // GET请求
//   async get(endpoint, options = {}) {
//     return this.request(endpoint, { method: 'GET', ...options });
//   }
//
//   // POST请求
//   async post(endpoint, data, options = {}) {
//     return this.request(endpoint, {
//       method: 'POST',
//       body: JSON.stringify(data),
//       ...options,
//     });
//   }
//
//   // PUT请求
//   async put(endpoint, data, options = {}) {
//     return this.request(endpoint, {
//       method: 'PUT',
//       body: JSON.stringify(data),
//       ...options,
//     });
//   }
//
//   // DELETE请求
//   async delete(endpoint, options = {}) {
//     return this.request(endpoint, { method: 'DELETE', ...options });
//   }
// }
//
// export const apiClient = new ApiClient();
