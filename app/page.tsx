'use client'

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Separator } from '@/components/ui/separator'
import { Button } from '@/components/ui/button'
import {
  BarChart,
  Camera,
  ExternalLink,
  History,
  Image as ImageIcon,
  Settings,
  Users,
  Video,
  X,
} from 'lucide-react'
import { toast } from '@/components/ui/use-toast'
import { format } from 'date-fns'
import { DataTable } from '@/components/data-table'
import { ColumnDef } from '@tanstack/react-table'
import { cn } from '@/lib/utils'
import { Filter } from '@/components/data-table/table-filter-section'
import { useAuthContext } from '@/contexts/AuthContext'

type DetectionMode = 'image' | 'video' | 'stream'

// 检测记录接口
interface DetectionRecord {
  id: string
  name: string
  type: string
  date: string
  operator: string // 添加操作人字段
}

// 生成检测历史记录数据
const generateDetectionRecords = (count: number): DetectionRecord[] => {
  const detectionTypes = ['面部表情检测', '人群情绪分析', '视频情绪分析', '实时情绪监测']
  const operators = [
    '李国强',
    '张敏',
    '王建国',
    '刘红',
    '赵文清',
    '陈明辉',
    '周建军',
    '吴秀英',
    '郑宏伟',
    '孙志国',
    '杨明',
    '朱海燕',
    '胡建华',
    '高文',
    '林志强',
    '马建国',
    '徐文彬',
    '黄志强',
    '曹明',
    '彭伟',
  ] // 扩展操作人名单
  const departments = ['技术部', '综合处', '监测科', '分析室', '研发中心'] // 部门信息
  const records: DetectionRecord[] = []

  for (let i = 1; i <= count; i++) {
    const typeIndex = Math.floor(Math.random() * detectionTypes.length)
    const operatorIndex = Math.floor(Math.random() * operators.length)
    const departmentIndex = Math.floor(Math.random() * departments.length)
    const daysAgo = Math.floor(Math.random() * 90) // 最近90天内的记录
    const date = new Date()
    date.setDate(date.getDate() - daysAgo)

    records.push({
      id: `DET-${Math.floor(10000 + Math.random() * 90000)}`,
      name: `检测任务 #${i} - ${detectionTypes[typeIndex]}`,
      type: detectionTypes[typeIndex],
      date: format(date, 'yyyy-MM-dd HH:mm:ss'),
      operator: `${operators[operatorIndex]}（${departments[departmentIndex]}）`, // 添加部门信息
    })
  }

  // 按日期排序，最新的排在前面
  return records.sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime())
}

// 模拟 API 请求，在实际应用中会替换为真实的 API 调用
const mockFetchDetectionRecords = async (page: number = 1, pageSize: number = 10) => {
  // 模拟网络请求延迟
  await new Promise(resolve => setTimeout(resolve, 300))

  const allRecords = generateDetectionRecords(100)
  const start = (page - 1) * pageSize
  const end = start + pageSize
  const paginatedRecords = allRecords.slice(start, end)

  return {
    rows: paginatedRecords,
    total: allRecords.length,
    page,
    pageSize,
  }
}

// 模拟 API 路由处理函数
const mockDetectionRecordsAPI = async (request: Request) => {
  const url = new URL(request.url)
  const page = parseInt(url.searchParams.get('page') || '1')
  const pageSize = parseInt(url.searchParams.get('pageSize') || '10')

  return mockFetchDetectionRecords(page, pageSize)
}

// 向全局添加模拟 API 路由
if (typeof window !== 'undefined') {
  // 在客户端重写 fetch，拦截对模拟 API 的请求
  const originalFetch = window.fetch
  window.fetch = async (input: RequestInfo | URL, init?: RequestInit) => {
    if (typeof input === 'string' && input.includes('/api/detection/history')) {
      return {
        ok: true,
        json: async () => mockDetectionRecordsAPI(new Request(input)),
      } as Response
    }
    return originalFetch(input, init)
  }
}

// 自定义右侧滑出的抽屉组件
const RightDrawer = ({
  children,
  open,
  onOpenChange,
}: {
  children: React.ReactNode
  open: boolean
  onOpenChange: (open: boolean) => void
}) => {
  return (
    <>
      {open && (
        <div
          className="fixed inset-0 z-50 bg-black/60 backdrop-blur-sm transition-opacity duration-300"
          onClick={() => onOpenChange(false)}
        />
      )}
      <div
        className={cn(
          'fixed inset-y-0 right-0 z-50 w-[80vw] max-w-[900px] bg-background shadow-xl transform transition-all duration-300 ease-in-out rounded-l-2xl border-l border-gray-100',
          open ? 'translate-x-0' : 'translate-x-full'
        )}
      >
        {children}
      </div>
    </>
  )
}

// 历史记录抽屉组件
const HistoryDrawer = () => {
  const [open, setOpen] = useState(false)
  const router = useRouter()

  // 定义过滤器
  const filters: Filter[] = [
    {
      type: 'input',
      name: 'name',
      label: '检测名称',
      placeholder: '输入检测名称关键词',
    },
    {
      type: 'input',
      name: 'operator',
      label: '操作人',
      placeholder: '输入操作人姓名',
    },
    {
      type: 'select',
      name: 'dateRange',
      label: '检测时间',
      placeholder: '选择时间范围',
      options: [
        { value: '', label: '所有时间' },
        { value: 'today', label: '今天' },
        { value: 'week', label: '最近一周' },
        { value: 'month', label: '最近一个月' },
        { value: 'quarter', label: '最近三个月' },
      ],
    },
  ]

  // 定义列
  const columns: ColumnDef<DetectionRecord>[] = [
    {
      accessorKey: 'name',
      header: () => <div className="min-w-[180px] font-medium text-gray-800">检测名称</div>,
      cell: ({ row }) => (
        <div className="py-1.5">
          <div className="font-medium text-gray-900 truncate">{row.original.name}</div>
          <div className="text-xs text-muted-foreground mt-0.5 truncate">{row.original.id}</div>
        </div>
      ),
    },
    {
      accessorKey: 'operator',
      header: () => <div className="min-w-[120px] font-medium text-gray-800">操作人</div>,
      cell: ({ row }) => (
        <div className="py-1.5 text-gray-700 truncate">{row.original.operator}</div>
      ),
    },
    {
      accessorKey: 'date',
      header: () => <div className="min-w-[120px] font-medium text-gray-800">检测时间</div>,
      cell: ({ row }) => <div className="py-1.5 text-gray-700">{row.original.date}</div>,
    },
    {
      id: 'actions',
      header: () => <div className="min-w-[80px] text-center font-medium text-gray-800">操作</div>,
      cell: ({ row }) => {
        const handleViewDetails = () => {
          router.push(`/detection/${row.original.id}`)
        }

        return (
          <div className="py-2 flex justify-end">
            <div className="relative group">
              <Button
                variant="outline"
                size="sm"
                className="h-7 px-2 bg-blue-50 border-blue-200 text-blue-700 hover:bg-blue-100 hover:text-blue-800 flex items-center gap-1 group relative rounded-md transition-all"
                onClick={handleViewDetails}
              >
                <ExternalLink className="h-3 w-3" />
                <span>查看</span>

                {/* 改进的工具提示 */}
                <span className="absolute -top-8 right-0 scale-0 transition-all rounded bg-gray-800 p-1.5 text-xs text-white group-hover:scale-100 w-auto whitespace-nowrap">
                  查看检测任务详情
                  <svg
                    className="absolute text-gray-800 h-2 w-full left-0 top-full"
                    x="0px"
                    y="0px"
                    viewBox="0 0 255 255"
                  >
                    <polygon className="fill-current" points="0,0 127.5,127.5 255,0" />
                  </svg>
                </span>
              </Button>
            </div>
          </div>
        )
      },
    },
  ]

  return (
    <>
      <Button
        variant="outline"
        className="flex items-center space-x-2 mr-2 border-gray-300 hover:bg-blue-50 hover:text-blue-700 hover:border-blue-200 transition-all relative group"
        onClick={() => setOpen(true)}
      >
        <History className="h-4 w-4" />
        <span>历史记录</span>
      </Button>

      <RightDrawer open={open} onOpenChange={setOpen}>
        <div className="absolute left-0 top-0 h-1.5 w-full bg-gradient-to-r from-blue-500 to-blue-600" />
        <div className="relative h-full flex flex-col">
          <div className="px-6 pt-4 pb-4 relative border-b border-gray-200 bg-gray-50/80 backdrop-blur-sm flex items-center justify-between">
            <div>
              <h2 className="text-lg font-semibold leading-none tracking-tight text-gray-900 flex items-center">
                <History className="mr-2 h-4 w-4 text-blue-600" />
                检测历史记录
              </h2>
              <p className="text-xs text-gray-600 mt-1.5">查看近期完成的所有检测任务</p>
            </div>

            <button
              className="rounded-full p-2 bg-gray-200/80 text-gray-700 hover:bg-gray-300 hover:text-gray-900 transition-colors flex items-center justify-center"
              onClick={() => setOpen(false)}
              aria-label="关闭"
            >
              <X className="h-4 w-4" />
            </button>
          </div>

          <div className="flex-1 overflow-auto bg-white p-4">
            <div className="border border-gray-200 rounded-lg shadow-sm overflow-hidden">
              <DataTable
                url="/api/detection/history"
                columns={columns}
                filters={filters}
                pageSize={10}
                pageSizeOptions={[10, 20, 50]}
              />
            </div>
          </div>
        </div>
      </RightDrawer>
    </>
  )
}

export default function HomePage() {
  const { isLogin } = useAuthContext()
  const router = useRouter()
  const [detectionName, setDetectionName] = useState('')
  const [detectionDesc, setDetectionDesc] = useState('')

  if (!isLogin) {
    return null
  }

  const handleModeSelect = (mode: DetectionMode) => {
    if (!detectionName.trim()) {
      toast({
        title: '请输入检测名称',
        description: '在选择检测方式之前，请为本次检测命名',
        variant: 'destructive',
      })
      return
    }

    // 保存检测基本信息到localStorage
    const detectionInfo = {
      name: detectionName,
      description: detectionDesc,
      createdAt: new Date().toISOString(),
    }
    localStorage.setItem('pending_detection_info', JSON.stringify(detectionInfo))

    // 跳转到对应的检测页面
    switch (mode) {
      case 'image':
        router.push('/detection/image')
        break
      case 'video':
        router.push('/detection/video')
        break
      case 'stream':
        router.push('/detection/stream')
        break
    }
  }

  return (
    <div className="h-full bg-gray-50">
      {/* 顶部导航栏 */}
      <div className="bg-white shadow-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-4">
            <div className="flex items-center">
              <h1 className="text-2xl font-bold text-gray-900">情绪识别系统</h1>
            </div>
            <div className="flex items-center space-x-2">
              <HistoryDrawer />
              <Button
                variant="outline"
                className="flex items-center space-x-2"
                onClick={() => router.push('/admin/training/tasks')}
              >
                <Settings className="h-4 w-4" />
                <span>后台管理</span>
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* 主要内容区域 */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <Card className="hover:shadow-lg transition-shadow">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-gray-900">总检测次数</CardTitle>
              <BarChart className="h-4 w-4 text-gray-500" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-gray-900">1,234</div>
              <p className="text-xs text-gray-500">+20.1% 从上月</p>
            </CardContent>
          </Card>
          <Card className="hover:shadow-lg transition-shadow">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-gray-900">活跃用户</CardTitle>
              <Users className="h-4 w-4 text-gray-500" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-gray-900">573</div>
              <p className="text-xs text-gray-500">+180.1% 从上月</p>
            </CardContent>
          </Card>
          <Card className="hover:shadow-lg transition-shadow">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-gray-900">场景数量</CardTitle>
              <BarChart className="h-4 w-4 text-gray-500" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-gray-900">12</div>
              <p className="text-xs text-gray-500">+2 从上月</p>
            </CardContent>
          </Card>
          <Card className="hover:shadow-lg transition-shadow">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-gray-900">已标注数据</CardTitle>
              <History className="h-4 w-4 text-gray-500" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-gray-900">573,234</div>
              <p className="text-xs text-gray-500">+201 从昨天</p>
            </CardContent>
          </Card>
        </div>

        <Card className="hover:shadow-lg transition-shadow">
          <CardHeader className="pb-3">
            <CardTitle className="text-lg font-medium text-gray-900">发起新检测</CardTitle>
            <CardDescription className="text-gray-500">
              按照以下步骤开始一次新的检测
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            {/* 步骤指引 */}
            <div className="flex items-center justify-between px-2">
              <div className="flex items-center">
                <div
                  className={`w-8 h-8 rounded-full flex items-center justify-center transition-colors ${
                    !detectionName.trim()
                      ? 'bg-blue-50 text-blue-600'
                      : 'bg-green-50 text-green-600'
                  }`}
                >
                  1
                </div>
                <span className="ml-2 font-medium text-gray-900">填写检测信息</span>
              </div>
              <div className="flex-1 mx-4 h-0.5 bg-gray-100" />
              <div className="flex items-center">
                <div
                  className={`w-8 h-8 rounded-full flex items-center justify-center transition-colors ${
                    !detectionName.trim() ? 'bg-gray-50 text-gray-500' : 'bg-blue-50 text-blue-600'
                  }`}
                >
                  2
                </div>
                <span className="ml-2 font-medium text-gray-900">选择检测方式</span>
              </div>
            </div>

            {/* 步骤1：检测信息 */}
            <div className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="detection-name" className="text-gray-900">
                  检测名称
                </Label>
                <Input
                  id="detection-name"
                  placeholder="为本次检测命名，例如：会议表情分析"
                  value={detectionName}
                  onChange={e => setDetectionName(e.target.value)}
                  className="focus:ring-2 focus:ring-blue-50"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="detection-desc" className="text-gray-900">
                  详细说明
                </Label>
                <Textarea
                  id="detection-desc"
                  placeholder="添加关于本次检测的详细说明（可选）"
                  value={detectionDesc}
                  onChange={e => setDetectionDesc(e.target.value)}
                  rows={3}
                  className="focus:ring-2 focus:ring-blue-50"
                />
              </div>
            </div>

            <Separator className="bg-gray-100" />

            {/* 步骤2：检测方式 */}
            <div className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <Card
                  className={`cursor-pointer transition-all duration-300 ${
                    !detectionName.trim()
                      ? 'opacity-50 cursor-not-allowed'
                      : 'hover:shadow-lg hover:scale-105 hover:bg-blue-50'
                  }`}
                  onClick={() => handleModeSelect('image')}
                >
                  <CardContent className="flex flex-col items-center justify-center p-6">
                    <div className="w-12 h-12 rounded-full bg-blue-50 flex items-center justify-center mb-4">
                      <ImageIcon className="h-6 w-6 text-blue-600" />
                    </div>
                    <p className="text-lg font-medium text-gray-900">图片检测</p>
                    <p className="text-sm text-gray-500 text-center mt-2">
                      上传图片进行表情识别分析
                    </p>
                  </CardContent>
                </Card>

                <Card
                  className={`cursor-pointer transition-all duration-300 ${
                    !detectionName.trim()
                      ? 'opacity-50 cursor-not-allowed'
                      : 'hover:shadow-lg hover:scale-105 hover:bg-blue-50'
                  }`}
                  onClick={() => handleModeSelect('video')}
                >
                  <CardContent className="flex flex-col items-center justify-center p-6">
                    <div className="w-12 h-12 rounded-full bg-blue-50 flex items-center justify-center mb-4">
                      <Video className="h-6 w-6 text-blue-600" />
                    </div>
                    <p className="text-lg font-medium text-gray-900">视频检测</p>
                    <p className="text-sm text-gray-500 text-center mt-2">
                      上传视频进行表情识别分析
                    </p>
                  </CardContent>
                </Card>

                <Card
                  className={`cursor-pointer transition-all duration-300 ${
                    !detectionName.trim()
                      ? 'opacity-50 cursor-not-allowed'
                      : 'hover:shadow-lg hover:scale-105 hover:bg-blue-50'
                  }`}
                  onClick={() => handleModeSelect('stream')}
                >
                  <CardContent className="flex flex-col items-center justify-center p-6">
                    <div className="w-12 h-12 rounded-full bg-blue-50 flex items-center justify-center mb-4">
                      <Camera className="h-6 w-6 text-blue-600" />
                    </div>
                    <p className="text-lg font-medium text-gray-900">实时检测</p>
                    <p className="text-sm text-gray-500 text-center mt-2">
                      使用摄像头进行实时表情识别
                    </p>
                  </CardContent>
                </Card>
              </div>
              {!detectionName.trim() && (
                <p className="text-sm text-gray-500 text-center">
                  请先完成步骤1：填写检测名称，然后继续选择检测方式
                </p>
              )}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
