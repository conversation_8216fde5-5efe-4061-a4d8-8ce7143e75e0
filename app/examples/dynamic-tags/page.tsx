import { DynamicTagsExample } from '@/components/examples/dynamic-tags-example'

export const metadata = {
  title: '动态标签组件示例',
  description: '展示动态标签组件的使用方法和各种配置选项',
}

export default function DynamicTagsPage() {
  return (
    <div className="container py-10">
      <div className="max-w-3xl mx-auto">
        <h1 className="text-3xl font-bold mb-6">动态标签组件</h1>
        <p className="text-lg text-muted-foreground mb-8">
          这个组件允许用户动态添加和删除标签，类似于NaiveUI中的DynamicTags组件。
          可以通过回车键或点击加号按钮添加标签，点击标签上的X图标删除标签。
        </p>

        <DynamicTagsExample />
      </div>
    </div>
  )
}
