'use client'

import { useState } from 'react'
import {
  <PERSON><PERSON>,
  <PERSON>alog<PERSON><PERSON>nt,
  <PERSON><PERSON><PERSON>eader,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  DialogFooter,
} from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { useToast } from '@/components/ui/use-toast'

interface UploadModelDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  onSuccess: () => void
}

export function UploadModelDialog({ open, onOpenChange, onSuccess }: UploadModelDialogProps) {
  const [loading, setLoading] = useState(false)
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    version: '',
    file: null as File | null,
  })
  const { toast } = useToast()

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      setFormData(prev => ({ ...prev, file: e.target.files![0] }))
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!formData.file) {
      toast({
        title: '错误',
        description: '请选择模型文件',
        variant: 'destructive',
      })
      return
    }

    try {
      setLoading(true)
      const fileData = new FormData()
      fileData.append('file', formData.file)
      fileData.append('name', formData.name)
      fileData.append('description', formData.description)
      fileData.append('version', formData.version)

      const response = await fetch('/api/training/models/upload', {
        method: 'POST',
        body: fileData,
      })

      if (!response.ok) {
        throw new Error('上传失败')
      }

      toast({
        title: '成功',
        description: '模型上传成功',
      })
      onSuccess()
      onOpenChange(false)
      setFormData({
        name: '',
        description: '',
        version: '',
        file: null,
      })
    } catch (error) {
      console.error('Upload error:', error)
      toast({
        title: '错误',
        description: '模型上传失败',
        variant: 'destructive',
      })
    } finally {
      setLoading(false)
    }
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>上传新模型</DialogTitle>
        </DialogHeader>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="grid w-full gap-1.5">
            <Label htmlFor="name">模型名称</Label>
            <Input
              id="name"
              required
              value={formData.name}
              onChange={e => setFormData(prev => ({ ...prev, name: e.target.value }))}
            />
          </div>
          <div className="grid w-full gap-1.5">
            <Label htmlFor="version">版本</Label>
            <Input
              id="version"
              required
              value={formData.version}
              onChange={e => setFormData(prev => ({ ...prev, version: e.target.value }))}
            />
          </div>
          <div className="grid w-full gap-1.5">
            <Label htmlFor="description">描述</Label>
            <Textarea
              id="description"
              value={formData.description}
              onChange={e => setFormData(prev => ({ ...prev, description: e.target.value }))}
            />
          </div>
          <div className="grid w-full gap-1.5">
            <Label htmlFor="file">模型文件</Label>
            <Input
              id="file"
              type="file"
              accept=".h5,.keras,.pt,.pth,.onnx,.mov"
              onChange={handleFileChange}
              required
            />
          </div>
          <DialogFooter>
            <Button type="button" variant="secondary" onClick={() => onOpenChange(false)}>
              取消
            </Button>
            <Button type="submit" disabled={loading}>
              {loading ? '上传中...' : '上传'}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  )
}
