'use client'

import { ColumnDef } from '@tanstack/react-table'
import { format } from 'date-fns'
import { ModelNameCell } from './model-name-cell'
import { ModelLabelsCell } from './model-labels-cell'
import { VersionCountCell } from './version-count-cell'
import { CreatorCell } from './creator-cell'
import { ModelActionsCell } from './model-actions-cell'

interface Model {
  id: number
  name: string
  description: string | null
  version: string
  accuracy: number | null
  isActive: boolean
  createdAt: string
  updatedAt: string | null
  path: string
  createdById: number
  updatedById: number | null
  labels?: string[]
  createdBy?: string
}

interface TableColumnsProps {
  onUpdateLabels: (modelId: number, labels: string[]) => void
  onEditModel: (model: Model) => void
  onDeleteModel: (model: Model) => void
}

export const createTableColumns = ({ onUpdateLabels, onEditModel, onDeleteModel }: TableColumnsProps): ColumnDef<Model>[] => [
  {
    accessorKey: 'name',
    header: '名称',
    cell: ({ row }) => <ModelNameCell row={row} />,
  },
  {
    accessorKey: 'labels',
    header: '标签',
    cell: ({ row }) => (
      <ModelLabelsCell 
        row={row} 
        onUpdateLabels={onUpdateLabels}
      />
    ),
  },
  {
    accessorKey: 'versions',
    header: '版本数量',
    cell: ({ row }) => <VersionCountCell row={row} />,
  },
  {
    accessorKey: 'createdBy',
    header: '创建人',
    cell: ({ row }) => <CreatorCell row={row} />,
  },
  {
    accessorKey: 'createdAt',
    header: '创建时间',
    cell: ({ row }) => {
      return (
        <div className="w-32">
          {format(new Date(row.getValue('createdAt')), 'yyyy-MM-dd HH:mm')}
        </div>
      )
    },
  },
  {
    id: 'actions',
    header: '操作',
    cell: ({ row }) => (
      <ModelActionsCell 
        row={row} 
        onEdit={onEditModel}
        onDelete={onDeleteModel}
      />
    ),
  },
]