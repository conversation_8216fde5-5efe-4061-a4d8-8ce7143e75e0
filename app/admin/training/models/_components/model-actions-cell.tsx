'use client'

import { Button } from '@/components/ui/button'

interface Model {
  id: number
  name: string
  description: string | null
  version: string
  accuracy: number | null
  isActive: boolean
  createdAt: string
  updatedAt: string | null
  path: string
  createdById: number
  updatedById: number | null
  labels?: string[]
  createdBy?: string
}

interface ModelActionsCellProps {
  row: any
  onEdit: (model: Model) => void
  onDelete: (model: Model) => void
}

export function ModelActionsCell({ row, onEdit, onDelete }: ModelActionsCellProps) {
  return (
    <div className="flex items-center gap-2">
      <Button 
        variant="outline" 
        size="sm" 
        className="h-8 bg-white text-gray-700 hover:bg-gray-50 shadow-sm border-gray-200"
        onClick={(e) => {
          e.stopPropagation()
          onEdit(row.original)
        }}
      >
        编辑
      </Button>
      <Button 
        variant="ghost" 
        size="sm" 
        className="h-8 text-red-600 hover:text-red-700 hover:bg-red-50"
        onClick={(e) => {
          e.stopPropagation()
          onDelete(row.original)
        }}
      >
        删除
      </Button>
    </div>
  )
}