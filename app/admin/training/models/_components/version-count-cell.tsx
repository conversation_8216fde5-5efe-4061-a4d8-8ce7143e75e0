'use client'

const getModelVersionCount = (modelId: number): number => {
  return Math.floor(Math.random() * 15) + 1
}

interface VersionCountCellProps {
  row: any
}

export function VersionCountCell({ row }: VersionCountCellProps) {
  const modelId = row.original.id
  const count = getModelVersionCount(modelId)
  
  return (
    <div className="text-center">
      <span className="inline-flex items-center justify-center px-2.5 py-1 rounded-full text-xs font-medium bg-blue-50 text-blue-700">
        {count}
      </span>
    </div>
  )
}