'use client'

import { But<PERSON> } from '@/components/ui/button'
import { Upload } from 'lucide-react'

interface TableActionsProps {
  onUpload: () => void
}

export function TableActions({ onUpload }: TableActionsProps) {
  return (
    <div className="flex gap-2">
      <Button 
        onClick={onUpload}
        className="bg-green-600 hover:bg-green-700 text-white h-9 px-4 flex items-center gap-2"
      >
        <Upload className="h-4 w-4" />
        上传模型
      </Button>
    </div>
  )
}