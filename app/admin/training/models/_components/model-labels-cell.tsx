'use client'

import { Badge } from '@/components/ui/badge'
import { X } from 'lucide-react'

interface ModelLabelsCellProps {
  row: any
  onUpdateLabels: (modelId: number, labels: string[]) => void
}

export function ModelLabelsCell({ row, onUpdateLabels }: ModelLabelsCellProps) {
  const labels: string[] = row.original.labels || []
  const modelId = row.original.id
  
  if (labels.length === 0) return <div className="text-gray-500">-</div>

  const handleDeleteTag = (tagToDelete: string) => {
    const updatedLabels = labels.filter(tag => tag !== tagToDelete)
    onUpdateLabels(modelId, updatedLabels)
  }

  return (
    <div className="max-w-[280px]">
      <div className="flex flex-wrap gap-1.5">
        {labels.map((label: string) => (
          <Badge 
            key={label} 
            variant="outline" 
            className="text-xs py-1 border-gray-200 font-normal flex items-center gap-1.5 pr-1.5 pl-2 group"
          >
            <span className="truncate max-w-[100px]">{label}</span>
            <X 
              className="h-3 w-3 opacity-0 group-hover:opacity-100 cursor-pointer hover:text-red-500 transition-opacity" 
              onClick={(e) => {
                e.stopPropagation()
                handleDeleteTag(label)
              }}
            />
          </Badge>
        ))}
      </div>
    </div>
  )
}