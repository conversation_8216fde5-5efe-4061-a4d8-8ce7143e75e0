'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Search, X, Upload } from 'lucide-react'
import { format } from 'date-fns'
import { DateRange } from 'react-day-picker'

const mockModelTags = [
  '人脸识别', '手势识别', '姿态估计', '物体检测', '分类', '情绪分析', 
  '高精度', '低延迟', '边缘部署', '目标跟踪', '3D重建', '语义分割',
  'AR增强', '表情识别', '场景理解', '活体检测', '室内导航', '注意力机制'
]

interface TableFiltersProps {
  nameFilter: string
  setNameFilter: (value: string) => void
  tagFilter: string
  setTagFilter: (value: string) => void
  creatorFilter: string
  setCreatorFilter: (value: string) => void
  startDate: DateRange | undefined
  setStartDate: (value: DateRange | undefined) => void
  endDate: DateRange | undefined
  setEndDate: (value: DateRange | undefined) => void
  onFilter: () => void
  onClear: () => void
  onUpload: () => void
}

export function TableFilters({
  nameFilter,
  setNameFilter,
  tagFilter,
  setTagFilter,
  creatorFilter,
  setCreatorFilter,
  startDate,
  setStartDate,
  endDate,
  setEndDate,
  onFilter,
  onClear,
  onUpload
}: TableFiltersProps) {
  return (
    <div className="space-y-7 mb-10">
      <div className="grid grid-cols-1 gap-7 lg:grid-cols-12">
        <div className="lg:col-span-3 space-y-3">
          <Label className="text-sm font-medium">模型名称</Label>
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <Input
              placeholder="搜索模型名称"
              value={nameFilter}
              onChange={e => setNameFilter(e.target.value)}
              className="pl-10 h-10 border-gray-200 focus:border-blue-500 rounded-md"
            />
            {nameFilter && (
              <Button 
                variant="ghost" 
                size="icon" 
                className="absolute right-2 top-0 h-full hover:bg-transparent"
                onClick={() => setNameFilter('')}
              >
                <X className="h-4 w-4 text-gray-400 hover:text-gray-600" />
              </Button>
            )}
          </div>
        </div>
        
        <div className="lg:col-span-3 space-y-3">
          <Label className="text-sm font-medium">标签筛选</Label>
          <div className="relative">
            <Select value={tagFilter} onValueChange={setTagFilter}>
              <SelectTrigger className="h-10 border-gray-200 focus:border-blue-500 rounded-md">
                <SelectValue placeholder="选择标签" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">全部标签</SelectItem>
                {mockModelTags.map(tag => (
                  <SelectItem key={tag} value={tag}>{tag}</SelectItem>
                ))}
              </SelectContent>
            </Select>
            {tagFilter !== 'all' && (
              <Button 
                variant="ghost" 
                size="icon" 
                className="absolute right-8 top-0 h-full hover:bg-transparent"
                onClick={() => setTagFilter('all')}
              >
                <X className="h-4 w-4 text-gray-400 hover:text-gray-600" />
              </Button>
            )}
          </div>
        </div>
        
        <div className="lg:col-span-3 space-y-3">
          <Label className="text-sm font-medium">创建人</Label>
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <Input
              placeholder="搜索创建人"
              value={creatorFilter === 'all' ? '' : creatorFilter}
              onChange={e => setCreatorFilter(e.target.value || 'all')}
              className="pl-10 h-10 border-gray-200 focus:border-blue-500 rounded-md"
            />
            {creatorFilter !== 'all' && (
              <Button 
                variant="ghost" 
                size="icon" 
                className="absolute right-2 top-0 h-full hover:bg-transparent"
                onClick={() => setCreatorFilter('all')}
              >
                <X className="h-4 w-4 text-gray-400 hover:text-gray-600" />
              </Button>
            )}
          </div>
        </div>
        
        <div className="lg:col-span-3 space-y-3">
          <Label className="text-sm font-medium">创建时间</Label>
          <div className="grid grid-cols-2 gap-2">
            <div className="relative">
              <Input
                type="date"
                placeholder="开始日期"
                value={startDate?.from ? format(startDate.from, 'yyyy-MM-dd') : ''}
                onChange={e => {
                  const date = e.target.value ? new Date(e.target.value) : undefined
                  setStartDate({
                    from: date,
                    to: startDate?.to
                  })
                }}
                className="h-10 border-gray-200 focus:border-blue-500 rounded-md w-full"
              />
              {startDate?.from && (
                <Button 
                  variant="ghost" 
                  size="icon" 
                  className="absolute right-2 top-0 h-full hover:bg-transparent"
                  onClick={() => setStartDate({
                    from: undefined,
                    to: startDate?.to
                  })}
                >
                  <X className="h-4 w-4 text-gray-400 hover:text-gray-600" />
                </Button>
              )}
            </div>
            <div className="relative">
              <Input
                type="date"
                placeholder="结束日期"
                value={endDate?.to ? format(endDate.to, 'yyyy-MM-dd') : ''}
                onChange={e => {
                  const date = e.target.value ? new Date(e.target.value) : undefined
                  setEndDate({
                    from: endDate?.from,
                    to: date
                  })
                }}
                className="h-10 border-gray-200 focus:border-blue-500 rounded-md w-full"
              />
              {endDate?.to && (
                <Button 
                  variant="ghost" 
                  size="icon" 
                  className="absolute right-2 top-0 h-full hover:bg-transparent"
                  onClick={() => setEndDate({
                    from: endDate?.from,
                    to: undefined
                  })}
                >
                  <X className="h-4 w-4 text-gray-400 hover:text-gray-600" />
                </Button>
              )}
            </div>
          </div>
        </div>
      </div>
      
      <div className="flex justify-between items-center gap-4 mt-4">
        <Button 
          variant="default"
          className="bg-green-600 hover:bg-green-700 text-white h-9 px-4 flex items-center gap-2"
          onClick={onUpload}
        >
          <Upload className="h-4 w-4" />
          上传模型
        </Button>
        
        <div className="flex items-center gap-4">
          <Button 
            variant="outline" 
            size="default"
            className="text-gray-600 border-gray-300 hover:bg-gray-50 h-9 px-4"
            onClick={onClear}
          >
            重置
          </Button>
          <Button 
            variant="default"
            size="default"
            className="bg-blue-600 hover:bg-blue-700 text-white shadow-sm h-9 px-5"
            onClick={onFilter}
          >
            查询
          </Button>
        </div>
      </div>
    </div>
  )
}