'use client'

import { useState, useMemo } from 'react'
import { Button } from '@/components/ui/button'
import { Download, Upload, X, ChevronLeft, ChevronRight } from 'lucide-react'
import { format } from 'date-fns'
import { Badge } from '@/components/ui/badge'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'

interface Model {
  id: number
  name: string
  description: string | null
  version: string
  accuracy: number | null
  isActive: boolean
  createdAt: string
  updatedAt: string | null
  path: string
  createdById: number
  updatedById: number | null
  labels?: string[]
  createdBy?: string
}

interface ModelDetailDrawerProps {
  isOpen: boolean
  onClose: () => void
  model: Model
}

export function ModelDetailDrawer({ isOpen, onClose, model }: ModelDetailDrawerProps) {
  const [activeTab, setActiveTab] = useState<'info' | 'history'>('info')
  const [currentPage, setCurrentPage] = useState(1)
  const itemsPerPage = 5
  
  // 生成随机版本历史数据
  const generateVersionHistory = (count: number) => {
    return Array.from({ length: count }).map((_, index) => ({
      id: model.id * 100 + index + 1,
      version: `v${index + 1}`,
      size: `${(Math.random() * 2 + 0.5).toFixed(2)} GB`,
      updatedBy: model.createdBy,
      updatedAt: new Date(Date.now() - (index * 24 * 60 * 60 * 1000)),
    }))
  }
  
  const versions = useMemo(() => generateVersionHistory(12), [model.id])
  
  // 分页处理
  const totalPages = Math.ceil(versions.length / itemsPerPage)
  const currentVersions = versions.slice(
    (currentPage - 1) * itemsPerPage,
    currentPage * itemsPerPage
  )
  
  // 下一页
  const nextPage = () => {
    if (currentPage < totalPages) {
      setCurrentPage(currentPage + 1)
    }
  }
  
  // 上一页
  const prevPage = () => {
    if (currentPage > 1) {
      setCurrentPage(currentPage - 1)
    }
  }
  
  if (!isOpen) return null
  
  return (
    <div className="fixed inset-0 z-50 overflow-hidden">
      <div className="absolute inset-0 bg-black/40 backdrop-blur-sm transition-opacity" onClick={onClose} />
      
      <div className="fixed inset-y-0 right-0 w-[520px] bg-white shadow-xl transform transition-transform duration-300 overflow-hidden flex flex-col h-full">
        {/* Header */}
        <div className="px-6 py-4 border-b flex justify-between items-center bg-gray-50">
          <h2 className="text-lg font-medium">模型详情</h2>
          <Button variant="ghost" size="icon" onClick={onClose} className="hover:bg-gray-100">
            <X className="h-5 w-5" />
          </Button>
        </div>
        
        {/* Tabs */}
        <div className="px-6 border-b">
          <div className="flex space-x-6">
            <button
              className={`py-3 relative ${
                activeTab === 'info'
                  ? 'text-blue-600 font-medium after:absolute after:bottom-0 after:left-0 after:right-0 after:h-0.5 after:bg-blue-600'
                  : 'text-gray-600 hover:text-gray-900'
              }`}
              onClick={() => setActiveTab('info')}
            >
              基本信息
            </button>
            <button
              className={`py-3 relative ${
                activeTab === 'history'
                  ? 'text-blue-600 font-medium after:absolute after:bottom-0 after:left-0 after:right-0 after:h-0.5 after:bg-blue-600'
                  : 'text-gray-600 hover:text-gray-900'
              }`}
              onClick={() => setActiveTab('history')}
            >
              版本历史
            </button>
          </div>
        </div>
        
        {/* Content */}
        <div className="flex-1 overflow-y-auto">
          {activeTab === 'info' ? (
            <div className="p-6 space-y-6">
              {/* Model Info */}
              <div className="space-y-4">
                <div>
                  <h3 className="text-sm font-medium text-gray-500 mb-1">模型名称</h3>
                  <p className="text-gray-900 font-semibold">{model.name}</p>
                </div>
                
                <div>
                  <h3 className="text-sm font-medium text-gray-500 mb-1">描述</h3>
                  <p className="text-gray-800">{model.description || "暂无描述"}</p>
                </div>
                
                <div>
                  <h3 className="text-sm font-medium text-gray-500 mb-1">标签</h3>
                  <div className="flex flex-wrap gap-1.5">
                    {model.labels && model.labels.length > 0 ? (
                      model.labels.map(label => (
                        <Badge key={label} variant="outline" className="text-xs py-1 border-gray-200">
                          {label}
                        </Badge>
                      ))
                    ) : (
                      <span className="text-gray-500">暂无标签</span>
                    )}
                  </div>
                </div>
                
                <div>
                  <h3 className="text-sm font-medium text-gray-500 mb-1">创建人</h3>
                  <p className="text-gray-800">{model.createdBy}</p>
                </div>
                
                <div>
                  <h3 className="text-sm font-medium text-gray-500 mb-1">创建时间</h3>
                  <p className="text-gray-800">{format(new Date(model.createdAt), 'yyyy-MM-dd HH:mm:ss')}</p>
                </div>
              </div>
              
              {/* Actions */}
              <div className="flex gap-3 pt-4 border-t">
                <Button 
                  variant="outline" 
                  size="sm" 
                  className="flex-1 gap-2 hover:bg-gray-50"
                >
                  <Download className="h-4 w-4" />
                  下载模型
                </Button>
                <Button
                  size="sm"
                  className="flex-1 gap-2 bg-blue-600 hover:bg-blue-700"
                >
                  <Upload className="h-4 w-4" />
                  上传新版本
                </Button>
              </div>
            </div>
          ) : (
            <div className="p-6">
              {/* Version History */}
              <h3 className="text-sm font-medium text-gray-500 mb-4">版本历史记录</h3>
              
              <div className="border rounded-md overflow-hidden">
                <Table>
                  <TableHeader className="bg-gray-50">
                    <TableRow>
                      <TableHead className="py-3 text-xs font-medium uppercase tracking-wider text-gray-500">版本</TableHead>
                      <TableHead className="py-3 text-xs font-medium uppercase tracking-wider text-gray-500">大小</TableHead>
                      <TableHead className="py-3 text-xs font-medium uppercase tracking-wider text-gray-500">更新人</TableHead>
                      <TableHead className="py-3 text-xs font-medium uppercase tracking-wider text-gray-500">更新时间</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {currentVersions.map((version) => (
                      <TableRow key={version.id} className="hover:bg-gray-50">
                        <TableCell className="font-medium text-blue-600">{version.version}</TableCell>
                        <TableCell>{version.size}</TableCell>
                        <TableCell>{version.updatedBy}</TableCell>
                        <TableCell>{format(version.updatedAt, 'yyyy-MM-dd')}</TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
              
              {/* Pagination */}
              {totalPages > 1 && (
                <div className="flex justify-between items-center mt-4">
                  <div className="text-sm text-gray-500">
                    显示 {(currentPage - 1) * itemsPerPage + 1}-{Math.min(currentPage * itemsPerPage, versions.length)} 共 {versions.length} 条
                  </div>
                  <div className="flex space-x-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={prevPage}
                      disabled={currentPage === 1}
                      className="h-8 w-8 p-0 flex items-center justify-center"
                    >
                      <span className="sr-only">上一页</span>
                      <ChevronLeft className="h-4 w-4" />
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={nextPage}
                      disabled={currentPage === totalPages}
                      className="h-8 w-8 p-0 flex items-center justify-center"
                    >
                      <span className="sr-only">下一页</span>
                      <ChevronRight className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              )}
            </div>
          )}
        </div>
      </div>
    </div>
  )
}