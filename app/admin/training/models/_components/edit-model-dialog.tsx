'use client'

import { useState, useEffect } from 'react'
import { <PERSON><PERSON>, <PERSON>alog<PERSON>ontent, Di<PERSON><PERSON>eader, Dialog<PERSON><PERSON><PERSON>, DialogFooter, DialogDescription } from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'

interface Model {
  id: number
  name: string
  description: string | null
  version: string
  accuracy: number | null
  isActive: boolean
  createdAt: string
  updatedAt: string | null
  path: string
  createdById: number
  updatedById: number | null
  labels?: string[]
  createdBy?: string
}

interface EditModelDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  model: Model | null
  onSave: (updatedModel: Model) => void
}

export function EditModelDialog({ open, onOpenChange, model, onSave }: EditModelDialogProps) {
  const [name, setName] = useState("")
  const [description, setDescription] = useState("")
  
  useEffect(() => {
    if (model) {
      setName(model.name)
      setDescription(model.description || "")
    }
  }, [model])
  
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    if (!model) return
    
    const updatedModel = {
      ...model,
      name,
      description
    }
    
    onSave(updatedModel)
    onOpenChange(false)
  }
  
  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>编辑模型</DialogTitle>
          <DialogDescription>
            修改模型的基本信息。点击保存应用修改。
          </DialogDescription>
        </DialogHeader>
        <form onSubmit={handleSubmit}>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="name" className="text-right">
                名称
              </Label>
              <Input
                id="name"
                value={name}
                onChange={(e) => setName(e.target.value)}
                className="col-span-3"
                required
              />
            </div>
            <div className="grid grid-cols-4 items-start gap-4">
              <Label htmlFor="description" className="text-right pt-2">
                描述
              </Label>
              <Textarea
                id="description"
                value={description}
                onChange={(e) => setDescription(e.target.value)}
                className="col-span-3"
                rows={4}
              />
            </div>
          </div>
          <DialogFooter>
            <Button type="button" variant="secondary" onClick={() => onOpenChange(false)}>
              取消
            </Button>
            <Button type="submit">保存修改</Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  )
}