'use client'

import { useState } from 'react'
import { ModelDetailDrawer } from './model-detail-drawer'

interface Model {
  id: number
  name: string
  description: string | null
  version: string
  accuracy: number | null
  isActive: boolean
  createdAt: string
  updatedAt: string | null
  path: string
  createdById: number
  updatedById: number | null
  labels?: string[]
  createdBy?: string
}

interface ModelNameCellProps {
  row: any
}

export function ModelNameCell({ row }: ModelNameCellProps) {
  const [drawerOpen, setDrawerOpen] = useState(false)
  
  const handleClick = () => {
    setDrawerOpen(true)
  }
  
  return (
    <div>
      <button 
        onClick={handleClick}
        className="text-blue-600 hover:text-blue-800 font-medium"
      >
        {row.getValue() || row.original.name}
      </button>
      <ModelDetailDrawer 
        isOpen={drawerOpen} 
        onClose={() => setDrawerOpen(false)} 
        model={row.original} 
      />
    </div>
  )
}