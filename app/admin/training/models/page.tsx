'use client'

import { useEffect, useState } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { SearchX, RotateCcw, Loader2, ChevronLeft, ChevronRight, ChevronsLeft, ChevronsRight } from 'lucide-react'
import { format } from 'date-fns'
import { UploadModelDialog } from './upload-model-dialog'
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { DateRange } from 'react-day-picker'
import { 
  TableFilters, 
  EditModelDialog, 
  DeleteConfirmDialog,
  createTableColumns,
  ModelNameCell,
  ModelLabelsCell,
  VersionCountCell,
  CreatorCell,
  ModelActionsCell
} from './_components'

interface Model {
  id: number
  name: string
  description: string | null
  version: string
  accuracy: number | null
  isActive: boolean
  createdAt: string
  updatedAt: string | null
  path: string
  createdById: number
  updatedById: number | null
  labels?: string[]
  createdBy?: string
}

const mockModelTags = [
  '人脸识别', '手势识别', '姿态估计', '物体检测', '分类', '情绪分析', 
  '高精度', '低延迟', '边缘部署', '目标跟踪', '3D重建', '语义分割',
  'AR增强', '表情识别', '场景理解', '活体检测', '室内导航', '注意力机制'
]

const generateRandomTags = (): string[] => {
  const tags: string[] = []
  const tagCount = Math.floor(Math.random() * 5) + 1
  
  for (let i = 0; i < tagCount; i++) {
    const randomTag = mockModelTags[Math.floor(Math.random() * mockModelTags.length)]
    if (!tags.includes(randomTag)) {
      tags.push(randomTag)
    }
  }
  
  return tags
}

export default function ModelRepositoryPage() {
  const [models, setModels] = useState<Model[]>([])
  const [loading, setLoading] = useState(true)
  const [showUploadDialog, setShowUploadDialog] = useState(false)
  
  // 筛选状态
  const [nameFilter, setNameFilter] = useState('')
  const [tagFilter, setTagFilter] = useState('all')
  const [creatorFilter, setCreatorFilter] = useState('all')
  const [startDate, setStartDate] = useState<DateRange | undefined>(undefined)
  const [endDate, setEndDate] = useState<DateRange | undefined>(undefined)
  
  // 分页状态
  const [currentPage, setCurrentPage] = useState(1)
  const [totalPages, setTotalPages] = useState(1)
  const [totalCount, setTotalCount] = useState(0)
  const [itemsPerPage, setItemsPerPage] = useState(10)
  
  // 编辑和删除模型的状态
  const [editingModel, setEditingModel] = useState<Model | null>(null)
  const [showEditDialog, setShowEditDialog] = useState(false)
  const [deletingModel, setDeletingModel] = useState<Model | null>(null)
  const [showDeleteDialog, setShowDeleteDialog] = useState(false)
  
  // 处理编辑模型
  const handleEditModel = (model: Model) => {
    setEditingModel(model)
    setShowEditDialog(true)
  }
  
  // 处理保存编辑
  const handleSaveEdit = (updatedModel: Model) => {
    setModels(prevModels => 
      prevModels.map(model => 
        model.id === updatedModel.id ? updatedModel : model
      )
    )
  }
  
  // 处理删除模型
  const handleDeleteModel = (model: Model) => {
    setDeletingModel(model)
    setShowDeleteDialog(true)
  }
  
  // 确认删除模型
  const handleConfirmDelete = () => {
    if (!deletingModel) return
    
    setModels(prevModels => 
      prevModels.filter(model => model.id !== deletingModel.id)
    )
    setShowDeleteDialog(false)
  }
  
  // 更新模型标签
  const handleUpdateLabels = (modelId: number, updatedLabels: string[]) => {
    setModels(prevModels => 
      prevModels.map(model => 
        model.id === modelId 
          ? { ...model, labels: updatedLabels } 
          : model
      )
    )
  }

  const clearFilters = () => {
    setNameFilter('')
    setTagFilter('all')
    setCreatorFilter('all')
    setStartDate(undefined)
    setEndDate(undefined)
    setCurrentPage(1)
    fetchMockModels()
  }

  const handleUploadSuccess = () => {
    fetchMockModels()
  }

  // Handle mock data fetching for demo purposes
  const fetchMockModels = async () => {
    try {
      setLoading(true)
      
      // Simulate API call delay
      await new Promise(resolve => setTimeout(resolve, 500))
      
      // 模型名称模拟数据
      const modelNames = [
        "高清人脸识别分析系统",
        "工业缺陷智能检测模型",
        "医疗影像病灶识别算法",
        "自动驾驶障碍物检测网络",
        "多语种文本情感分析器",
        "实时人体姿态追踪引擎",
        "城市交通流量预测模型",
        "智能安防异常行为分析",
        "农作物病虫害识别系统",
        "水下目标自动检测跟踪",
        "空间目标三维重建网络",
        "智能客服语义理解模型",
        "老年人跌倒监测预警系统",
        "智能家居行为识别分析",
        "视频内容智能审核系统",
        "工业设备故障预测模型",
        "金融风险评估预警系统",
        "商品图像自动分类器",
        "智慧城市人流密度监测",
        "医疗诊断辅助决策系统"
      ]
      
      // 为演示添加更多模型数据
      const extendedModelNames = [...modelNames]
      for (let i = 0; i < 3; i++) {
        modelNames.forEach((name, index) => {
          extendedModelNames.push(`${name} V${i+2}`)
        })
      }
      
      // Generate mock data
      const allMockData = Array.from({ length: extendedModelNames.length }).map((_, i) => ({
        id: i + 1,
        name: extendedModelNames[i],
        description: i % 3 === 0 ? `这是${extendedModelNames[i]}的描述信息，用于展示模型的用途和特点。` : null,
        version: `v${Math.floor(i / 5) + 1}.${i % 5}.${Math.floor(Math.random() * 10)}`,
        accuracy: Math.random(),
        isActive: Math.random() > 0.3,
        createdAt: new Date(Date.now() - (i * 24 * 60 * 60 * 1000)).toISOString(),
        updatedAt: i % 2 === 0 ? new Date(Date.now() - (i * 12 * 60 * 60 * 1000)).toISOString() : null,
        path: `/models/model-${i + 1}`,
        createdById: Math.floor(Math.random() * 10) + 1,
        updatedById: i % 2 === 0 ? Math.floor(Math.random() * 10) + 1 : null,
        labels: generateRandomTags(),
        createdBy: `用户${Math.floor(Math.random() * 100) + 1}`,
      }))
      
      // Apply filters to mock data
      let filteredData = [...allMockData]
      
      // Filter by name
      if (nameFilter) {
        filteredData = filteredData.filter(model => 
          model.name.toLowerCase().includes(nameFilter.toLowerCase())
        )
      }
      
      // Filter by label
      if (tagFilter !== 'all') {
        filteredData = filteredData.filter(model => 
          model.labels?.includes(tagFilter)
        )
      }
      
      // Filter by creator
      if (creatorFilter !== 'all') {
        filteredData = filteredData.filter(model => 
          model.createdBy?.toLowerCase().includes(creatorFilter.toLowerCase())
        )
      }
      
      // Filter by date range
      if (startDate?.from) {
        const fromDate = new Date(startDate.from)
        fromDate.setHours(0, 0, 0, 0)
        filteredData = filteredData.filter(model => 
          new Date(model.createdAt) >= fromDate
        )
      }
      
      if (endDate?.to) {
        const toDate = new Date(endDate.to)
        toDate.setHours(23, 59, 59, 999)
        filteredData = filteredData.filter(model => 
          new Date(model.createdAt) <= toDate
        )
      }
      
      // 应用分页
      const totalItems = filteredData.length
      const totalPageCount = Math.ceil(totalItems / itemsPerPage)
      setTotalCount(totalItems)
      setTotalPages(totalPageCount > 0 ? totalPageCount : 1)
      
      // 确保当前页不超出范围
      const validCurrentPage = Math.min(currentPage, totalPageCount)
      if (validCurrentPage !== currentPage && totalPageCount > 0) {
        setCurrentPage(validCurrentPage)
      } else if (totalPageCount === 0) {
        setCurrentPage(1)
      }
      
      // 获取当前页数据
      const startIndex = (validCurrentPage - 1) * itemsPerPage
      const endIndex = startIndex + itemsPerPage
      const pageData = filteredData.slice(startIndex, endIndex)
      
      setModels(pageData)
      setLoading(false)
    } catch (error) {
      console.error('Error generating mock models:', error)
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchMockModels()
  }, [currentPage, itemsPerPage])

  // 处理页码变更
  const handlePageChange = (page: number) => {
    setCurrentPage(page)
  }

  // 添加处理页面大小变更的函数
  const handlePageSizeChange = (newSize: string) => {
    const size = parseInt(newSize, 10)
    setCurrentPage(1)
    setItemsPerPage(size)
    fetchMockModels()
  }

  const handleFilter = () => {
    setCurrentPage(1)
    fetchMockModels()
  }

  const renderModelsTable = () => {
    if (totalCount === 0 && (nameFilter || tagFilter !== 'all' || creatorFilter !== 'all' || startDate || endDate)) {
      return (
        <div className="flex flex-col items-center justify-center py-16 px-4 bg-gray-50 rounded-lg">
          <div className="h-20 w-20 text-gray-300 mb-4">
            <SearchX size={80} className="opacity-50" />
          </div>
          <h3 className="text-lg font-medium text-gray-900 mb-1">没有匹配的模型</h3>
          <p className="text-gray-500 text-center max-w-md mb-6">
            没有找到符合当前筛选条件的模型。请尝试调整您的筛选条件或清除筛选器。
          </p>
          <Button
            variant="outline"
            onClick={() => {
              setNameFilter('')
              setTagFilter('all')
              setCreatorFilter('all')
              setStartDate(undefined)
              setEndDate(undefined)
              setCurrentPage(1)
              fetchMockModels()
            }}
            className="flex items-center gap-2"
          >
            <RotateCcw size={16} />
            <span>重置所有筛选器</span>
          </Button>
        </div>
      )
    }
    
    if (loading) {
      return (
        <div className="flex justify-center items-center py-16">
          <Loader2 className="h-8 w-8 animate-spin text-gray-400" />
        </div>
      )
    }

    return (
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow className="bg-gray-50 hover:bg-gray-50">
              <TableHead className="px-4 py-3.5 text-xs font-medium uppercase tracking-wider text-gray-500">名称</TableHead>
              <TableHead className="px-4 py-3.5 text-xs font-medium uppercase tracking-wider text-gray-500">标签</TableHead>
              <TableHead className="px-4 py-3.5 text-xs font-medium uppercase tracking-wider text-gray-500">版本数量</TableHead>
              <TableHead className="px-4 py-3.5 text-xs font-medium uppercase tracking-wider text-gray-500">创建人</TableHead>
              <TableHead className="px-4 py-3.5 text-xs font-medium uppercase tracking-wider text-gray-500">创建时间</TableHead>
              <TableHead className="px-4 py-3.5 text-xs font-medium uppercase tracking-wider text-gray-500">操作</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {models.map((model) => (
              <TableRow key={model.id} className="hover:bg-gray-50">
                <TableCell className="px-4 py-4">
                  <ModelNameCell row={{ getValue: () => model.name, original: model }} />
                </TableCell>
                <TableCell className="px-4 py-4">
                  <ModelLabelsCell 
                    row={{ original: model }} 
                    onUpdateLabels={handleUpdateLabels} 
                  />
                </TableCell>
                <TableCell className="px-4 py-4">
                  <VersionCountCell row={{ original: model }} />
                </TableCell>
                <TableCell className="px-4 py-4">
                  <CreatorCell row={{ original: model }} />
                </TableCell>
                <TableCell className="px-4 py-4">
                  <div className="text-gray-600">
                    {format(new Date(model.createdAt), 'yyyy-MM-dd HH:mm')}
                  </div>
                </TableCell>
                <TableCell className="px-4 py-4">
                  <ModelActionsCell 
                    row={{ original: model }} 
                    onEdit={handleEditModel}
                    onDelete={handleDeleteModel}
                  />
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
        
        {/* 添加分页控件 */}
        {totalPages > 0 && (
          <div className="flex justify-between items-center px-4 py-3 bg-white border-t border-gray-200">
            <div className="text-sm text-gray-500">
              显示 {totalCount > 0 ? `${(currentPage - 1) * itemsPerPage + 1}-${Math.min(currentPage * itemsPerPage, totalCount)}` : '0'} 共 {totalCount} 条
            </div>
            <div className="flex items-center gap-6 lg:gap-8">
              {/* 每页记录数选择器 */}
              <div className="flex items-center gap-2">
                <span className="text-sm">每页</span>
                <Select 
                  value={itemsPerPage.toString()} 
                  onValueChange={handlePageSizeChange}
                >
                  <SelectTrigger className="h-8 w-[70px]">
                    <SelectValue placeholder={itemsPerPage} />
                  </SelectTrigger>
                  <SelectContent side="top">
                    {[10, 20, 30, 50].map(size => (
                      <SelectItem key={size} value={size.toString()}>
                        {size}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <span className="text-sm">条</span>
              </div>

              {/* 分页按钮组 */}
              <div className="flex items-center gap-1">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handlePageChange(1)}
                  disabled={currentPage === 1}
                  className="h-8 w-8 p-0 border-gray-300"
                >
                  <ChevronsLeft className="h-4 w-4" />
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handlePageChange(currentPage - 1)}
                  disabled={currentPage === 1}
                  className="h-8 w-8 p-0 border-gray-300"
                >
                  <ChevronLeft className="h-4 w-4" />
                </Button>

                {/* 页码按钮组 */}
                <div className="flex items-center gap-1">
                  {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                    let pageNum
                    if (totalPages <= 5) {
                      pageNum = i + 1
                    } else if (currentPage < 3) {
                      pageNum = i + 1
                    } else if (currentPage > totalPages - 3) {
                      pageNum = totalPages - 4 + i
                    } else {
                      pageNum = currentPage - 2 + i
                    }

                    return (
                      <Button
                        key={i}
                        variant={pageNum === currentPage ? 'default' : 'outline'}
                        size="sm"
                        onClick={() => handlePageChange(pageNum)}
                        className={`h-8 min-w-[2rem] border-gray-300 ${
                          pageNum === currentPage
                            ? 'bg-blue-600 text-white hover:bg-blue-700'
                            : 'hover:bg-gray-100'
                        }`}
                      >
                        {pageNum}
                      </Button>
                    )
                  })}
                </div>

                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handlePageChange(currentPage + 1)}
                  disabled={currentPage >= totalPages}
                  className="h-8 w-8 p-0 border-gray-300"
                >
                  <ChevronRight className="h-4 w-4" />
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handlePageChange(totalPages)}
                  disabled={currentPage >= totalPages}
                  className="h-8 w-8 p-0 border-gray-300"
                >
                  <ChevronsRight className="h-4 w-4" />
                </Button>
              </div>

              {/* 总页数显示 */}
              <div className="flex items-center text-sm">
                <span>共 {totalPages} 页</span>
              </div>
            </div>
          </div>
        )}
      </div>
    )
  }

  return (
    <div className="px-8 py-10">
      {/* Filter section */}
      <TableFilters
        nameFilter={nameFilter}
        setNameFilter={setNameFilter}
        tagFilter={tagFilter}
        setTagFilter={setTagFilter}
        creatorFilter={creatorFilter}
        setCreatorFilter={setCreatorFilter}
        startDate={startDate}
        setStartDate={setStartDate}
        endDate={endDate}
        setEndDate={setEndDate}
        onFilter={handleFilter}
        onClear={clearFilters}
        onUpload={() => setShowUploadDialog(true)}
      />
      
      {/* Models table */}
      <div className="mt-2">
        {renderModelsTable()}
      </div>

      <UploadModelDialog
        open={showUploadDialog}
        onOpenChange={setShowUploadDialog}
        onSuccess={handleUploadSuccess}
      />
      
      {/* Edit Model Dialog */}
      <EditModelDialog
        open={showEditDialog}
        onOpenChange={setShowEditDialog}
        model={editingModel}
        onSave={handleSaveEdit}
      />
      
      {/* Delete Confirm Dialog */}
      {deletingModel && (
        <DeleteConfirmDialog
          open={showDeleteDialog}
          onOpenChange={setShowDeleteDialog}
          modelName={deletingModel.name}
          onConfirm={handleConfirmDelete}
        />
      )}
    </div>
  )
}