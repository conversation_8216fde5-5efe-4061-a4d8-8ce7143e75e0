'use client'

import { ColumnDef } from '@tanstack/react-table'
import { format } from 'date-fns'
import { But<PERSON> } from '@/components/ui/button'
import { Eye } from 'lucide-react'
import { TaskModel } from '@/app/api/training/tasks/route'
import { TaskNameCell } from './task-name-cell'
import { TaskTypeCell } from './task-type-cell'
import { StatusCell } from './status-cell'
import { ProgressCell } from './progress-cell'
import { RecordCountCell } from './record-count-cell'
import { DeleteTaskDialog } from './delete-task-dialog'

// 扩展 TaskModel 接口，添加训练记录相关字段
interface ExtendedTaskModel extends TaskModel {
  recordCount: number // 训练记录数量
  lastTrainedAt?: string // 最后一次训练时间
}

interface TableColumnsProps {
  onTaskDetail: (taskId: string | number) => void
  onRefresh: () => void
}

export const createTableColumns = ({ onTaskDetail, onRefresh }: TableColumnsProps): ColumnDef<ExtendedTaskModel>[] => [
  {
    accessorKey: 'name',
    header: () => <div className="min-w-[120px]">任务名称</div>,
    cell: ({ row }) => <TaskNameCell row={row} />,
  },
  {
    accessorKey: 'taskType',
    header: () => <div className="min-w-[80px]">任务类型</div>,
    cell: ({ row }) => <TaskTypeCell row={row} />,
  },
  {
    accessorKey: 'datasetName',
    header: () => <div className="min-w-[120px]">数据集</div>,
  },
  {
    accessorKey: 'modelName',
    header: () => <div className="min-w-[100px]">模型</div>,
  },
  {
    accessorKey: 'status',
    header: () => <div>状态</div>,
    cell: ({ row }) => <StatusCell row={row} />,
  },
  {
    accessorKey: 'progress',
    header: () => <div className="min-w-[120px]">最新训练结果</div>,
    cell: ({ row }) => <ProgressCell row={row} />,
  },
  {
    accessorKey: 'recordCount',
    header: () => <div className="min-w-[60px]">训练记录</div>,
    cell: ({ row }) => <RecordCountCell row={row} />,
  },
  {
    accessorKey: 'createdAt',
    header: () => <div className="min-w-[140px]">创建时间</div>,
    cell: ({ row }) => format(new Date(row.original.createdAt), 'yyyy-MM-dd HH:mm'),
  },
  {
    id: 'actions',
    header: () => <div className="min-w-[180px]">操作</div>,
    cell: ({ row }) => (
      <div className="flex space-x-2">
        <Button
          variant="outline"
          size="sm"
          onClick={() => onTaskDetail(row.original.id)}
          className="h-8 whitespace-nowrap"
        >
          <Eye className="h-4 w-4 mr-0" />
          查看
        </Button>
        <DeleteTaskDialog
          taskId={row.original.id}
          taskName={row.original.name}
          onDelete={onRefresh}
        />
      </div>
    ),
  },
]