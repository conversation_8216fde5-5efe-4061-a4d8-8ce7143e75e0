'use client'

import { useState } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import {
  <PERSON>alog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog'
import { Trash2 } from 'lucide-react'

interface DeleteTaskDialogProps {
  taskId: string | number
  taskName: string
  onDelete: () => void
}

export function DeleteTaskDialog({ taskId, taskName, onDelete }: DeleteTaskDialogProps) {
  const [open, setOpen] = useState(false)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const handleDelete = async () => {
    setLoading(true)
    setError(null)
    try {
      const response = await fetch(`/api/training/tasks/${taskId}`, {
        method: 'DELETE',
      })

      if (!response.ok) {
        let errorMessage = '删除任务失败'
        // 只有当响应有内容时才尝试解析JSON
        const contentType = response.headers.get('content-type')
        if (contentType && contentType.includes('application/json') && response.status !== 204) {
          const text = await response.text()
          if (text) {
            try {
              const errorData = JSON.parse(text)
              errorMessage = errorData.error || errorMessage
            } catch (parseError) {
              console.error('Failed to parse error response:', parseError)
            }
          }
        }
        throw new Error(errorMessage)
      }

      setOpen(false)
      // 在成功删除后调用回调函数来刷新数据
      onDelete()
    } catch (error) {
      console.error('删除任务错误:', error)
      setError(error instanceof Error ? error.message : '删除任务时发生错误')
    } finally {
      setLoading(false)
    }
  }

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button
          variant="outline"
          size="sm"
          className="h-8 whitespace-nowrap text-red-500 hover:text-red-700 hover:bg-red-50"
        >
          <Trash2 className="h-4 w-4 mr-0" />
          删除
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>确认删除任务</DialogTitle>
          <DialogDescription className="pt-2">
            您确定要删除任务 <span className="font-medium">{taskName}</span> 吗？此操作无法撤销。
          </DialogDescription>
        </DialogHeader>
        {error && (
          <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-2 rounded-md text-sm">
            {error}
          </div>
        )}
        <DialogFooter className="mt-4">
          <Button type="button" variant="outline" onClick={() => setOpen(false)}>
            取消
          </Button>
          <Button type="button" variant="destructive" onClick={handleDelete} disabled={loading}>
            {loading ? '删除中...' : '确认删除'}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}