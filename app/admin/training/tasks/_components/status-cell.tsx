'use client'

import { cn } from '@/lib/utils'

const getStatusColor = (status: string): string => {
  switch (status) {
    case 'pending':
      return 'text-yellow-600 bg-yellow-50 border-yellow-200'
    case 'training':
      return 'text-blue-600 bg-blue-50 border-blue-200'
    case 'completed':
      return 'text-green-600 bg-green-50 border-green-200'
    case 'error':
      return 'text-red-600 bg-red-50 border-red-200'
    default:
      return 'text-gray-600 bg-gray-50 border-gray-200'
  }
}

const getStatusText = (status: string): string => {
  switch (status) {
    case 'pending':
      return '等待中'
    case 'training':
      return '训练中'
    case 'completed':
      return '已完成'
    case 'error':
      return '错误'
    default:
      return status
  }
}

interface StatusCellProps {
  row: any
}

export function StatusCell({ row }: StatusCellProps) {
  const status = row.original.status
  const error = row.original.error

  return (
    <div className="flex flex-col gap-1 items-start">
      <span
        className={cn(
          'inline-flex items-center justify-center px-2.5 py-0.5 rounded-full text-xs font-medium border whitespace-nowrap',
          getStatusColor(status)
        )}
      >
        {getStatusText(status)}
      </span>
      {error && <span className="text-xs text-red-500">{error}</span>}
    </div>
  )
}