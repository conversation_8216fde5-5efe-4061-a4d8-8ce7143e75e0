'use client'

// 模型基本信息接口
interface ModelDetail {
  id: string
  name: string
  description: string
  version: string
  accuracy: number
  status: 'active' | 'inactive'
  createdAt: string
  updatedAt: string
  framework: string
  size: number
  parameters: number
  tags: string[]
}

// 训练记录接口
interface TrainingRecord {
  id: string
  taskId: string
  startTime: string
  endTime: string | null
  status: 'running' | 'completed' | 'failed' | 'stopped' | 'paused'
  progress: number
  accuracy: number | null
  loss: number | null
  dataset: string
  epochs: number
  batchSize: number
  learningRate: number
}

// 模拟数据 - 实际项目中应该从API获取
const mockModelDetail: ModelDetail = {
  id: '1',
  name: '情绪识别模型 V1',
  description: '用于识别文本中的情绪，包括积极、消极和中性情绪',
  version: '1.0.0',
  accuracy: 0.92,
  status: 'active',
  createdAt: '2023-10-15T08:30:00Z',
  updatedAt: '2023-11-20T14:45:00Z',
  framework: 'PyTorch',
  size: 256,
  parameters: 125000000,
  tags: ['NLP', '情绪分析', '中文'],
}

// 模拟训练记录数据
const mockTrainingRecords: TrainingRecord[] = [
  {
    id: 't1',
    taskId: '1',
    startTime: '2023-10-10T09:00:00Z',
    endTime: '2023-10-10T14:30:00Z',
    status: 'completed',
    progress: 100,
    accuracy: 0.92,
    loss: 0.08,
    dataset: '中文情绪语料库',
    epochs: 10,
    batchSize: 32,
    learningRate: 0.001,
  },
  {
    id: 't2',
    taskId: '1',
    startTime: '2023-09-25T13:15:00Z',
    endTime: '2023-09-25T18:45:00Z',
    status: 'completed',
    progress: 100,
    accuracy: 0.89,
    loss: 0.11,
    dataset: '中文情绪语料库',
    epochs: 8,
    batchSize: 32,
    learningRate: 0.002,
  },
  {
    id: 't3',
    taskId: '1',
    startTime: '2023-11-05T10:30:00Z',
    endTime: null,
    status: 'running',
    progress: 65,
    accuracy: null,
    loss: null,
    dataset: '增强中文情绪语料库',
    epochs: 15,
    batchSize: 64,
    learningRate: 0.0005,
  },
]

// 模拟不同任务的假数据映射
export const mockTaskDetailMap: Record<string, { model: ModelDetail; records: TrainingRecord[] }> = {
  '1': {
    model: {
      ...mockModelDetail,
      name: '基础表情识别模型 v1.0',
      description: '识别基本的七种表情：高兴、悲伤、愤怒、恐惧、惊讶、厌恶和中性',
      tags: ['表情识别', '基础模型', '七情绪'],
    },
    records: mockTrainingRecords,
  },
  '2': {
    model: {
      ...mockModelDetail,
      id: '2',
      name: '增强表情识别模型 v2.1',
      version: '2.1.0',
      description: '能够识别更丰富的情绪表达，包括微妙的表情变化和混合情绪',
      accuracy: 0.94,
      framework: 'TensorFlow',
      size: 320,
      parameters: 180000000,
      tags: ['表情识别', '增强模型', '细微表情', '混合情绪'],
    },
    records: [
      {
        id: 't1',
        taskId: '2',
        startTime: '2023-11-15T10:20:00Z',
        endTime: '2023-11-15T18:45:00Z',
        status: 'completed',
        progress: 100,
        accuracy: 0.94,
        loss: 0.06,
        dataset: '增强表情数据集',
        epochs: 15,
        batchSize: 64,
        learningRate: 0.0008,
      },
      {
        id: 't2',
        taskId: '2',
        startTime: '2023-10-28T09:10:00Z',
        endTime: '2023-10-28T15:30:00Z',
        status: 'completed',
        progress: 100,
        accuracy: 0.91,
        loss: 0.09,
        dataset: '增强表情数据集',
        epochs: 12,
        batchSize: 32,
        learningRate: 0.001,
      },
    ],
  },
  '3': {
    model: {
      ...mockModelDetail,
      id: '3',
      name: '多人表情模型 v1.5',
      version: '1.5.0',
      description: '同时识别多个人脸的表情，适用于群体场景分析',
      accuracy: 0.88,
      framework: 'PyTorch',
      size: 420,
      parameters: 220000000,
      tags: ['多人识别', '群体分析', '表情识别'],
    },
    records: [
      {
        id: 't1',
        taskId: '3',
        startTime: '2023-12-05T11:00:00Z',
        endTime: null,
        status: 'running',
        progress: 45,
        accuracy: null,
        loss: null,
        dataset: '多人场景数据集',
        epochs: 20,
        batchSize: 16,
        learningRate: 0.0005,
      },
    ],
  },
}