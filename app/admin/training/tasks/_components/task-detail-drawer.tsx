'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Separator } from '@/components/ui/separator'
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { useToast } from '@/components/ui/use-toast'
import { format } from 'date-fns'
import { X, PlusCircle } from 'lucide-react'
import { TrainingRecordsTable } from './training-records-table'

// 模型基本信息接口
interface ModelDetail {
  id: string
  name: string
  description: string
  version: string
  accuracy: number
  status: 'active' | 'inactive'
  createdAt: string
  updatedAt: string
  framework: string
  size: number
  parameters: number
  tags: string[]
}

// 训练记录接口
interface TrainingRecord {
  id: string
  taskId: string
  startTime: string
  endTime: string | null
  status: 'running' | 'completed' | 'failed' | 'stopped' | 'paused'
  progress: number
  accuracy: number | null
  loss: number | null
  dataset: string
  epochs: number
  batchSize: number
  learningRate: number
}

// 训练配置接口
interface TrainingConfig {
  epochs: number
  batchSize: number
  learningRate: number
}

interface TaskDetailDrawerProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  taskId: string | number
  mockTaskDetailMap: Record<string, { model: ModelDetail; records: TrainingRecord[] }>
}

export function TaskDetailDrawer({ 
  open, 
  onOpenChange, 
  taskId,
  mockTaskDetailMap
}: TaskDetailDrawerProps) {
  const { toast } = useToast()
  const [loading, setLoading] = useState(false)
  const [showDialog, setShowDialog] = useState(false)

  // 从模拟数据映射中获取当前任务的数据，如果没有则使用默认数据
  const taskData = mockTaskDetailMap[String(taskId)] || {
    model: {
      id: '1',
      name: '情绪识别模型 V1',
      description: '用于识别文本中的情绪，包括积极、消极和中性情绪',
      version: '1.0.0',
      accuracy: 0.92,
      status: 'active' as const,
      createdAt: '2023-10-15T08:30:00Z',
      updatedAt: '2023-11-20T14:45:00Z',
      framework: 'PyTorch',
      size: 256,
      parameters: 125000000,
      tags: ['NLP', '情绪分析', '中文'],
    },
    records: [],
  }

  const [trainingRecords, setTrainingRecords] = useState<TrainingRecord[]>(taskData.records)
  const [trainingConfig, setTrainingConfig] = useState<TrainingConfig>({
    epochs: 10,
    batchSize: 32,
    learningRate: 0.001,
  })

  // 实际项目中应该根据taskId来获取任务的详细信息
  // 这里仅使用模拟数据
  const modelDetail = taskData.model

  // 打开训练配置对话框
  const openTrainingDialog = () => {
    setShowDialog(true)
  }

  // 处理训练配置变更
  const handleConfigChange = (field: keyof TrainingConfig, value: any) => {
    setTrainingConfig(prev => ({
      ...prev,
      [field]: value,
    }))
  }

  // 暂停训练任务
  const handlePauseTask = (taskId: string) => {
    setTrainingRecords(prev =>
      prev.map(record => (record.id === taskId ? { ...record, status: 'paused' as const } : record))
    )

    toast({
      title: '任务已暂停',
      description: `训练任务 ${taskId} 已暂停`,
    })
  }

  // 开始训练
  const handleStartTraining = async () => {
    setLoading(true)
    try {
      // 实际项目中这里应该调用API
      await new Promise(resolve => setTimeout(resolve, 1000))

      // 创建新的训练记录
      const newRecord: TrainingRecord = {
        id: `t${trainingRecords.length + 1}`,
        taskId: taskId.toString(),
        startTime: new Date().toISOString(),
        endTime: null,
        status: 'running',
        progress: 0,
        accuracy: null,
        loss: null,
        dataset: '默认数据集',
        epochs: trainingConfig.epochs,
        batchSize: trainingConfig.batchSize,
        learningRate: trainingConfig.learningRate,
      }

      // 添加到记录列表
      setTrainingRecords(prev => [newRecord, ...prev])

      toast({
        title: '训练已开始',
        description: '新的训练任务已加入队列',
      })

      setShowDialog(false)
    } catch (error) {
      toast({
        title: '创建训练任务失败',
        description: '无法创建新的训练任务',
        variant: 'destructive',
      })
    } finally {
      setLoading(false)
    }
  }

  if (!open) return null

  return (
    <div className="fixed inset-0 z-50 overflow-hidden">
      {/* 背景遮罩 */}
      <div
        className="absolute inset-0 bg-black/40 backdrop-blur-sm transition-opacity"
        onClick={() => onOpenChange(false)}
      />

      {/* 右侧抽屉 */}
      <div className="fixed inset-y-0 right-0 w-[95%] sm:w-[800px] md:w-[900px] lg:w-[1000px] xl:w-[1100px] 2xl:w-[1200px] bg-white shadow-xl transform transition-transform duration-300 overflow-hidden flex flex-col h-full">
        {/* 头部 */}
        <div className="px-6 py-4 border-b flex justify-between items-center sticky top-0 bg-white z-10">
          <div>
            <h2 className="text-lg font-medium">{modelDetail.name}</h2>
            <div className="flex items-center gap-2 mt-1">
              <span className="text-sm text-muted-foreground">任务ID: {taskId}</span>
              <Badge
                className={
                  modelDetail.status === 'active'
                    ? 'bg-green-100 text-green-800'
                    : 'bg-gray-100 text-gray-800'
                }
              >
                {modelDetail.status === 'active' ? '已激活' : '未激活'}
              </Badge>
            </div>
          </div>
          <Button
            variant="ghost"
            size="icon"
            onClick={() => onOpenChange(false)}
            className="rounded-full hover:bg-gray-100"
          >
            <X className="h-5 w-5" />
          </Button>
        </div>

        {/* 内容区域 */}
        <div className="flex-1 overflow-y-auto p-6 space-y-6">
          <Tabs defaultValue="training" className="space-y-4">
            <TabsList>
              <TabsTrigger value="basic">基本信息</TabsTrigger>
              <TabsTrigger value="training">训练记录</TabsTrigger>
            </TabsList>

            <TabsContent value="basic" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      {modelDetail.name}
                      <Badge
                        className={
                          modelDetail.status === 'active'
                            ? 'bg-green-100 text-green-800'
                            : 'bg-gray-100 text-gray-800'
                        }
                      >
                        {modelDetail.status === 'active' ? '已激活' : '未激活'}
                      </Badge>
                    </div>
                    <Badge variant="outline">{modelDetail.version}</Badge>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <div className="space-y-4">
                      <div>
                        <h3 className="text-sm font-medium text-muted-foreground">框架</h3>
                        <p>{modelDetail.framework}</p>
                      </div>
                      <div>
                        <h3 className="text-sm font-medium text-muted-foreground">准确率</h3>
                        <p>{(modelDetail.accuracy * 100).toFixed(2)}%</p>
                      </div>
                      <div>
                        <h3 className="text-sm font-medium text-muted-foreground">模型大小</h3>
                        <p>{modelDetail.size} MB</p>
                      </div>
                    </div>
                    <div className="space-y-4">
                      <div>
                        <h3 className="text-sm font-medium text-muted-foreground">参数数量</h3>
                        <p>{(modelDetail.parameters / 1000000).toFixed(1)}M</p>
                      </div>
                      <div>
                        <h3 className="text-sm font-medium text-muted-foreground">创建时间</h3>
                        <p>{format(new Date(modelDetail.createdAt), 'yyyy-MM-dd HH:mm:ss')}</p>
                      </div>
                      <div>
                        <h3 className="text-sm font-medium text-muted-foreground">最后更新</h3>
                        <p>{format(new Date(modelDetail.updatedAt), 'yyyy-MM-dd HH:mm:ss')}</p>
                      </div>
                    </div>
                    <div className="space-y-4">
                      <div>
                        <h3 className="text-sm font-medium text-muted-foreground">标签</h3>
                        <div className="flex flex-wrap gap-1 mt-1">
                          {modelDetail.tags.map(tag => (
                            <Badge key={tag} variant="secondary">
                              {tag}
                            </Badge>
                          ))}
                        </div>
                      </div>
                      <div>
                        <h3 className="text-sm font-medium text-muted-foreground">状态</h3>
                        <Badge
                          className={
                            modelDetail.status === 'active'
                              ? 'bg-green-100 text-green-800 mt-1'
                              : 'bg-gray-100 text-gray-800 mt-1'
                          }
                        >
                          {modelDetail.status === 'active' ? '已激活' : '未激活'}
                        </Badge>
                      </div>
                    </div>
                  </div>

                  <Separator className="my-6" />

                  <div className="space-y-4">
                    <h3 className="text-md font-medium">模型性能指标</h3>
                    <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-4">
                      <Card>
                        <CardHeader className="pb-2">
                          <CardTitle className="text-sm font-medium text-muted-foreground">
                            准确率
                          </CardTitle>
                        </CardHeader>
                        <CardContent>
                          <div className="text-2xl font-bold">
                            {(modelDetail.accuracy * 100).toFixed(2)}%
                          </div>
                        </CardContent>
                      </Card>
                      <Card>
                        <CardHeader className="pb-2">
                          <CardTitle className="text-sm font-medium text-muted-foreground">
                            推理速度
                          </CardTitle>
                        </CardHeader>
                        <CardContent>
                          <div className="text-2xl font-bold">15ms</div>
                        </CardContent>
                      </Card>
                      <Card>
                        <CardHeader className="pb-2">
                          <CardTitle className="text-sm font-medium text-muted-foreground">
                            训练次数
                          </CardTitle>
                        </CardHeader>
                        <CardContent>
                          <div className="text-2xl font-bold">{trainingRecords.length}</div>
                        </CardContent>
                      </Card>
                      <Card>
                        <CardHeader className="pb-2">
                          <CardTitle className="text-sm font-medium text-muted-foreground">
                            参数量
                          </CardTitle>
                        </CardHeader>
                        <CardContent>
                          <div className="text-2xl font-bold">
                            {(modelDetail.parameters / 1000000).toFixed(1)}M
                          </div>
                        </CardContent>
                      </Card>
                      <Card>
                        <CardHeader className="pb-2">
                          <CardTitle className="text-sm font-medium text-muted-foreground">
                            模型大小
                          </CardTitle>
                        </CardHeader>
                        <CardContent>
                          <div className="text-2xl font-bold">{modelDetail.size} MB</div>
                        </CardContent>
                      </Card>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="training">
              <Card className="shadow-sm">
                <CardHeader className="flex flex-row items-center justify-between">
                  <Button
                    onClick={openTrainingDialog}
                    disabled={loading}
                    className="bg-blue-600 hover:bg-blue-700"
                  >
                    <PlusCircle className="h-4 w-4 mr-2" />
                    开始训练
                  </Button>
                </CardHeader>
                <CardContent>
                  <TrainingRecordsTable records={trainingRecords} onPauseTask={handlePauseTask} />
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>

          {/* 训练配置对话框 */}
          <Dialog open={showDialog} onOpenChange={setShowDialog}>
            <DialogContent className="sm:max-w-[425px]">
              <DialogHeader>
                <DialogTitle>训练配置</DialogTitle>
                <DialogDescription>设置训练参数，确认后将开始新的训练任务</DialogDescription>
              </DialogHeader>
              <div className="grid gap-4 py-4">
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="epochs" className="text-right">
                    训练轮次
                  </Label>
                  <Input
                    id="epochs"
                    type="number"
                    value={trainingConfig.epochs}
                    onChange={e => handleConfigChange('epochs', parseInt(e.target.value))}
                    className="col-span-3"
                    min={1}
                    max={100}
                  />
                </div>
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="batchSize" className="text-right">
                    批次大小
                  </Label>
                  <Input
                    id="batchSize"
                    type="number"
                    value={trainingConfig.batchSize}
                    onChange={e => handleConfigChange('batchSize', parseInt(e.target.value))}
                    className="col-span-3"
                    min={8}
                    max={256}
                    step={8}
                  />
                </div>
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="learningRate" className="text-right">
                    学习率
                  </Label>
                  <Input
                    id="learningRate"
                    type="number"
                    value={trainingConfig.learningRate}
                    onChange={e => handleConfigChange('learningRate', parseFloat(e.target.value))}
                    className="col-span-3"
                    min={0.0001}
                    max={0.1}
                    step={0.0001}
                  />
                </div>
              </div>
              <DialogFooter>
                <Button variant="outline" onClick={() => setShowDialog(false)}>
                  取消
                </Button>
                <Button onClick={handleStartTraining} disabled={loading}>
                  {loading ? '处理中...' : '开始训练'}
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        </div>

        {/* 底部 */}
        <div className="p-4 border-t">
          <Button variant="outline" onClick={() => onOpenChange(false)} className="w-full">
            关闭
          </Button>
        </div>
      </div>
    </div>
  )
}