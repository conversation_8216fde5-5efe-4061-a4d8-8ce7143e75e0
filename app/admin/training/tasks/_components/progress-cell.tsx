'use client'

import { Progress } from '@/components/ui/progress'
import { format } from 'date-fns'

interface ProgressCellProps {
  row: any
}

export function ProgressCell({ row }: ProgressCellProps) {
  const task = row.original
  const hasRecords = task.recordCount && task.recordCount > 0

  if (!hasRecords) {
    return <div className="text-sm text-muted-foreground">尚未开始训练</div>
  }

  return (
    <div className="flex flex-col gap-1 min-w-[200px]">
      {task.status === 'training' && (
        <>
          <Progress value={task.progress} className="h-2" />
          <div className="text-xs text-muted-foreground text-right">{task.progress}%</div>
        </>
      )}

      <div className="flex justify-between text-xs">
        <span>准确率: {(task.accuracy * 100).toFixed(1)}%</span>
      </div>
      <div className="flex justify-between text-xs">
        <span>损失: {task.loss.toFixed(3)}</span>
      </div>
      <div className="text-xs text-muted-foreground">
        最后训练:{' '}
        {task.lastTrainedAt ? format(new Date(task.lastTrainedAt), 'yyyy-MM-dd HH:mm') : '无'}
      </div>
    </div>
  )
}