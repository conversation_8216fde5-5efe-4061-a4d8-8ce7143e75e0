'use client'

interface TaskTypeCellProps {
  row: any
}

export function TaskTypeCell({ row }: TaskTypeCellProps) {
  const taskType = row.original.taskType
  const executionMethod = row.original.executionMethod

  let typeText = '一次性任务'
  let executionText = ''

  if (taskType === 'recurring') {
    typeText = '例行任务'

    if (executionMethod) {
      switch (executionMethod) {
        case 'daily':
          executionText = '每日执行'
          break
        case 'weekly':
          executionText = '每周执行'
          break
        case 'monthly':
          executionText = '每月执行'
          break
        case 'custom':
          const interval = row.original.executionInterval || 1
          executionText = `每${interval}天执行`
          break
      }
    }
  }

  return (
    <div className="flex flex-col">
      <span className="font-medium">{typeText}</span>
      {executionText && <span className="text-xs text-muted-foreground">{executionText}</span>}
    </div>
  )
}