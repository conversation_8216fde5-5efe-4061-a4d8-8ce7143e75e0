'use client'

import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { format } from 'date-fns'
import { Pause } from 'lucide-react'

interface TrainingRecord {
  id: string
  taskId: string
  startTime: string
  endTime: string | null
  status: 'running' | 'completed' | 'failed' | 'stopped' | 'paused'
  progress: number
  accuracy: number | null
  loss: number | null
  dataset: string
  epochs: number
  batchSize: number
  learningRate: number
}

interface TrainingRecordsTableProps {
  records: TrainingRecord[]
  onPauseTask: (id: string) => void
}

export function TrainingRecordsTable({ records, onPauseTask }: TrainingRecordsTableProps) {
  // 状态样式映射
  const getStatusBadge = (status: string) => {
    const statusMap = {
      running: { label: '运行中', class: 'bg-blue-50 text-blue-700 border-blue-200' },
      completed: { label: '已完成', class: 'bg-green-50 text-green-700 border-green-200' },
      failed: { label: '失败', class: 'bg-red-50 text-red-700 border-red-200' },
      stopped: { label: '已停止', class: 'bg-slate-50 text-slate-700 border-slate-200' },
      paused: { label: '已暂停', class: 'bg-amber-50 text-amber-700 border-amber-200' },
    }
    const { label, class: className } = statusMap[status as keyof typeof statusMap]
    return <Badge className={`${className} font-medium text-xs px-2 py-1`}>{label}</Badge>
  }

  // 格式化学习率显示
  const formatLearningRate = (rate: number) => {
    return rate.toExponential(3)
  }

  // 格式化时间范围
  const formatTimeRange = (startTime: string, endTime: string | null) => {
    const startFormatted = format(new Date(startTime), 'yyyy-MM-dd HH:mm')
    if (!endTime) {
      return (
        <div className="flex flex-col gap-1">
          <div className="flex items-center gap-1">
            <span className="font-medium text-slate-900">开始:</span>
            <span className="text-slate-700">{startFormatted}</span>
          </div>
          <div className="text-xs text-slate-500 italic">正在进行中...</div>
        </div>
      )
    }
    const endFormatted = format(new Date(endTime), 'yyyy-MM-dd HH:mm')
    const durationMs = new Date(endTime).getTime() - new Date(startTime).getTime()
    const hours = Math.floor(durationMs / (1000 * 60 * 60))
    const minutes = Math.floor((durationMs % (1000 * 60 * 60)) / (1000 * 60))
    const durationText =
      hours > 0 ? `${hours}小时${minutes > 0 ? ` ${minutes}分钟` : ''}` : `${minutes}分钟`

    return (
      <div className="flex flex-col gap-1">
        <div className="flex items-center gap-1">
          <span className="font-medium text-slate-900">开始:</span>
          <span className="text-slate-700">{startFormatted}</span>
        </div>
        <div className="flex items-center gap-1">
          <span className="font-medium text-slate-900">结束:</span>
          <span className="text-slate-700">{endFormatted}</span>
        </div>
        <div className="text-xs text-slate-500">总时长: {durationText}</div>
      </div>
    )
  }

  // 判断是否可以暂停任务
  const canPauseTask = (status: string) => {
    return status === 'running'
  }

  // 根据任务ID获取模型版本
  const getModelVersion = (taskId: string) => {
    // 实际项目中应该从API获取，这里使用模拟数据
    const versionMap: Record<string, string> = {
      '1': 'v1.0.3',
      '2': 'v2.1.5',
      '3': 'v1.5.2',
    }
    return versionMap[taskId] || `v1.0.0`
  }

  return (
    <div className="space-y-4">
      <div className="rounded-md border overflow-hidden shadow-sm">
        <div className="overflow-x-auto">
          <Table className="w-full">
            <TableHeader>
              <TableRow className="bg-slate-50 border-b border-slate-200">
                <TableHead className="py-3 w-[5%] font-medium text-slate-700">ID</TableHead>
                <TableHead className="py-3 w-[24%] font-medium text-slate-700">训练时间</TableHead>
                <TableHead className="py-3 w-[10%] font-medium text-slate-700">状态</TableHead>
                <TableHead className="py-3 w-[15%] font-medium text-slate-700 text-center">
                  轮次/批次
                </TableHead>
                <TableHead className="py-3 w-[15%] font-medium text-slate-700 text-center">
                  学习率
                </TableHead>
                <TableHead className="py-3 w-[10%] font-medium text-slate-700 text-center">
                  进度
                </TableHead>
                <TableHead className="py-3 w-[11%] font-medium text-slate-700 text-center">
                  模型版本
                </TableHead>
                <TableHead className="py-3 w-[10%] font-medium text-slate-700 text-center">
                  操作
                </TableHead>
              </TableRow>
            </TableHeader>
            <TableBody className="bg-white">
              {records.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={8} className="h-24 text-center text-slate-500">
                    暂无训练记录
                  </TableCell>
                </TableRow>
              ) : (
                records.map(record => (
                  <TableRow
                    key={record.id}
                    className="hover:bg-slate-50/80 border-b border-slate-100"
                  >
                    <TableCell className="py-3 font-medium text-sm text-slate-700">
                      {record.id}
                    </TableCell>
                    <TableCell className="py-3 text-sm">
                      {formatTimeRange(record.startTime, record.endTime)}
                    </TableCell>
                    <TableCell className="py-3">{getStatusBadge(record.status)}</TableCell>
                    <TableCell className="py-3 text-center">
                      <div className="text-sm text-slate-700 flex flex-col">
                        <span className="text-slate-900">{record.epochs} 轮</span>
                        <span className="text-xs text-slate-500">{record.batchSize} 批次</span>
                      </div>
                    </TableCell>
                    <TableCell className="py-3 font-mono text-xs text-center text-slate-700">
                      {formatLearningRate(record.learningRate)}
                    </TableCell>
                    <TableCell className="py-3 text-center">
                      <span
                        className={`inline-block text-xs font-semibold px-2 py-1 rounded-full ${
                          record.progress === 100
                            ? 'bg-green-50 text-green-700'
                            : 'bg-blue-50 text-blue-700'
                        }`}
                      >
                        {record.progress}%
                      </span>
                    </TableCell>
                    <TableCell className="py-3 text-center">
                      {record.status === 'completed' ? (
                        <Badge
                          variant="outline"
                          className="text-xs font-medium px-2 py-1 bg-slate-50 border-slate-200"
                        >
                          {getModelVersion(record.taskId)}
                        </Badge>
                      ) : (
                        <span className="text-xs text-slate-400">-</span>
                      )}
                    </TableCell>
                    <TableCell className="py-3 text-center">
                      {canPauseTask(record.status) ? (
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => onPauseTask(record.id)}
                          className="px-2 py-0 h-7 border-slate-200 hover:bg-slate-50 hover:text-slate-800"
                        >
                          <Pause className="h-3 w-3 mr-1" />
                          暂停
                        </Button>
                      ) : (
                        <span className="text-xs text-slate-400">-</span>
                      )}
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </div>
      </div>
    </div>
  )
}