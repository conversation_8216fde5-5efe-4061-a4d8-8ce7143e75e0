'use client'

import type { Filter } from '@/components/data-table/table-filter-section'

export const tableFilters: Filter[] = [
  {
    type: 'input',
    name: 'name',
    label: '任务名称',
    placeholder: '请输入任务名称',
  },
  {
    type: 'select',
    name: 'taskType',
    label: '任务类型',
    placeholder: '选择任务类型',
    options: [
      { value: 'one-time', label: '一次性任务' },
      { value: 'recurring', label: '例行任务' },
    ],
  },
  {
    type: 'select',
    name: 'status',
    label: '状态',
    placeholder: '选择状态',
    options: [
      { value: 'pending', label: '等待中' },
      { value: 'training', label: '训练中' },
      { value: 'completed', label: '已完成' },
      { value: 'error', label: '错误' },
    ],
  },
  {
    type: 'select',
    name: 'model',
    label: '模型',
    placeholder: '选择模型',
    fetchUrl: '/api/training/models/options',
  },
  {
    type: 'select',
    name: 'dataset',
    label: '数据集',
    placeholder: '选择数据集',
    options: [
      { value: '1', label: '表情数据集2024' },
      { value: '2', label: '多人场景数据集' },
      { value: '3', label: '视频流数据集' },
    ],
  },
]