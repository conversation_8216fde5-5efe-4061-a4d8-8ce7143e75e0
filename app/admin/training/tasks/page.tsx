'use client'

import { TaskModel } from '@/app/api/training/tasks/route'
import { SortableDataTable } from '@/components/data-table/sortable-data-table'
import { Button } from '@/components/ui/button'
import { Plus } from 'lucide-react'
import Link from 'next/link'
import { useRouter } from 'next/navigation'
import { useState } from 'react'
import { 
  createTableColumns, 
  tableFilters, 
  TaskDetailDrawer,
  mockTaskDetailMap
} from './_components'

// 扩展 TaskModel 接口，添加训练记录相关字段
interface ExtendedTaskModel extends TaskModel {
  recordCount: number // 训练记录数量
  lastTrainedAt?: string // 最后一次训练时间
}

export default function TaskListPage() {
  const router = useRouter()
  const [refreshTrigger, setRefreshTrigger] = useState(0)
  const [drawerOpen, setDrawerOpen] = useState(false)
  const [selectedTaskId, setSelectedTaskId] = useState<string | number>('')

  // 刷新数据表格
  const refreshTable = () => {
    setRefreshTrigger(Date.now())
  }

  // 打开详情抽屉
  const openTaskDetail = (taskId: string | number) => {
    setSelectedTaskId(taskId)
    setDrawerOpen(true)
  }

  // 高亮新创建的任务行
  const highlightNewTask = (task: ExtendedTaskModel, newTaskId?: string | number) => {
    if (!newTaskId) return false
    return task.id.toString() === newTaskId.toString()
  }

  const columns = createTableColumns({
    onTaskDetail: openTaskDetail,
    onRefresh: refreshTable
  })

  return (
    <>
      <SortableDataTable
        url="/api/training/tasks"
        columns={columns}
        filters={tableFilters}
        key={`tasks-table-${refreshTrigger}`}
        initialSort={{
          sortBy: 'createdAt',
          sortOrder: 'desc',
        }}
        highlightRow={highlightNewTask}
        actions={
          <Link href="/admin/training/tasks/new">
            <Button size="sm" className="h-8 bg-blue-600 hover:bg-blue-700">
              <Plus className="h-4 w-4 mr-2" />
              创建任务
            </Button>
          </Link>
        }
      />

      {/* 任务详情抽屉 */}
      <TaskDetailDrawer 
        open={drawerOpen} 
        onOpenChange={setDrawerOpen} 
        taskId={selectedTaskId}
        mockTaskDetailMap={mockTaskDetailMap}
      />
    </>
  )
}