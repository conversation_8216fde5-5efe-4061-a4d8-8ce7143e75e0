'use client'

import { useState } from 'react'
import { useRouter, useSearchParams } from 'next/navigation'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Separator } from '@/components/ui/separator'
import { Pause, PlusCircle } from 'lucide-react'
import { Progress } from '@/components/ui/progress'
import { format } from 'date-fns'
import { useToast } from '@/components/ui/use-toast'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'

// 模型基本信息接口
interface ModelDetail {
  id: string
  name: string
  description: string
  version: string
  accuracy: number
  status: 'active' | 'inactive'
  createdAt: string
  updatedAt: string
  framework: string
  size: number
  parameters: number
  tags: string[]
}

// 训练记录接口
interface TrainingRecord {
  id: string
  taskId: string
  startTime: string
  endTime: string | null
  status: 'running' | 'completed' | 'failed' | 'stopped' | 'paused'
  progress: number
  accuracy: number | null
  loss: number | null
  dataset: string
  epochs: number
  batchSize: number
  learningRate: number
}

// 训练配置接口
interface TrainingConfig {
  epochs: number
  batchSize: number
  learningRate: number
}

// 模拟数据 - 实际项目中应该从API获取
const mockModelDetail: ModelDetail = {
  id: '1',
  name: '情绪识别模型 V1',
  description: '用于识别文本中的情绪，包括积极、消极和中性情绪',
  version: '1.0.0',
  accuracy: 0.92,
  status: 'active',
  createdAt: '2023-10-15T08:30:00Z',
  updatedAt: '2023-11-20T14:45:00Z',
  framework: 'PyTorch',
  size: 256,
  parameters: 125000000,
  tags: ['NLP', '情绪分析', '中文'],
}

// 模拟训练记录数据
const mockTrainingRecords: TrainingRecord[] = [
  {
    id: 't1',
    taskId: '1',
    startTime: '2023-10-10T09:00:00Z',
    endTime: '2023-10-10T14:30:00Z',
    status: 'completed',
    progress: 100,
    accuracy: 0.92,
    loss: 0.08,
    dataset: '中文情绪语料库',
    epochs: 10,
    batchSize: 32,
    learningRate: 0.001,
  },
  {
    id: 't2',
    taskId: '1',
    startTime: '2023-09-25T13:15:00Z',
    endTime: '2023-09-25T18:45:00Z',
    status: 'completed',
    progress: 100,
    accuracy: 0.89,
    loss: 0.11,
    dataset: '中文情绪语料库',
    epochs: 8,
    batchSize: 32,
    learningRate: 0.002,
  },
  {
    id: 't3',
    taskId: '1',
    startTime: '2023-11-05T10:30:00Z',
    endTime: null,
    status: 'running',
    progress: 65,
    accuracy: null,
    loss: null,
    dataset: '增强中文情绪语料库',
    epochs: 15,
    batchSize: 64,
    learningRate: 0.0005,
  },
  {
    id: 't4',
    taskId: '1',
    startTime: '2023-08-15T08:20:00Z',
    endTime: '2023-08-15T16:45:00Z',
    status: 'completed',
    progress: 100,
    accuracy: 0.87,
    loss: 0.13,
    dataset: '中文情绪语料库',
    epochs: 12,
    batchSize: 16,
    learningRate: 0.0015,
  },
  {
    id: 't5',
    taskId: '1',
    startTime: '2023-07-20T11:30:00Z',
    endTime: '2023-07-20T18:10:00Z',
    status: 'failed',
    progress: 45,
    accuracy: null,
    loss: null,
    dataset: '多语言情绪数据集',
    epochs: 20,
    batchSize: 64,
    learningRate: 0.01,
  },
  {
    id: 't6',
    taskId: '1',
    startTime: '2023-06-05T09:15:00Z',
    endTime: '2023-06-05T15:40:00Z',
    status: 'completed',
    progress: 100,
    accuracy: 0.91,
    loss: 0.09,
    dataset: '社交媒体情绪数据',
    epochs: 15,
    batchSize: 48,
    learningRate: 0.0008,
  },
]

// 可用数据集列表
const availableDatasets = [
  { id: '1', name: '中文情绪语料库', size: 10000 },
  { id: '2', name: '增强中文情绪语料库', size: 15000 },
  { id: '3', name: '多语言情绪数据集', size: 20000 },
  { id: '4', name: '社交媒体情绪数据', size: 25000 },
]

// 简化版的训练记录表格组件
function TrainingRecordsTable({
  records,
  onPauseTask,
}: {
  records: TrainingRecord[]
  onPauseTask: (id: string) => void
}) {
  // 状态样式映射
  const getStatusBadge = (status: string) => {
    const statusMap = {
      running: { label: '运行中', class: 'bg-blue-100 text-blue-800' },
      completed: { label: '已完成', class: 'bg-green-100 text-green-800' },
      failed: { label: '失败', class: 'bg-red-100 text-red-800' },
      stopped: { label: '已停止', class: 'bg-gray-100 text-gray-800' },
      paused: { label: '已暂停', class: 'bg-yellow-100 text-yellow-800' },
    }
    const { label, class: className } = statusMap[status as keyof typeof statusMap]
    return <Badge className={className}>{label}</Badge>
  }

  // 格式化学习率显示
  const formatLearningRate = (rate: number) => {
    return rate.toExponential(4)
  }

  // 判断是否可以暂停任务
  const canPauseTask = (status: string) => {
    return status === 'running'
  }

  return (
    <div className="space-y-4">
      <div className="rounded-md border overflow-hidden">
        <div className="overflow-x-auto">
          <Table className="w-full">
            <TableHeader>
              <TableRow className="bg-slate-50">
                <TableHead className="w-[8%] font-medium">记录ID</TableHead>
                <TableHead className="w-[14%] font-medium">开始时间</TableHead>
                <TableHead className="w-[14%] font-medium">结束时间</TableHead>
                <TableHead className="w-[8%] font-medium">状态</TableHead>
                <TableHead className="w-[12%] font-medium">进度</TableHead>
                <TableHead className="w-[8%] font-medium text-center">训练轮次</TableHead>
                <TableHead className="w-[8%] font-medium text-center">批次大小</TableHead>
                <TableHead className="w-[10%] font-medium text-center">学习率</TableHead>
                <TableHead className="w-[8%] font-medium text-center">准确率</TableHead>
                <TableHead className="w-[10%] font-medium text-center">操作</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {records.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={10} className="h-24 text-center">
                    没有找到数据
                  </TableCell>
                </TableRow>
              ) : (
                records.map(record => (
                  <TableRow key={record.id} className="hover:bg-slate-50/50">
                    <TableCell className="font-medium">{record.id}</TableCell>
                    <TableCell className="text-sm">
                      {format(new Date(record.startTime), 'yyyy-MM-dd HH:mm:ss')}
                    </TableCell>
                    <TableCell className="text-sm">
                      {record.endTime
                        ? format(new Date(record.endTime), 'yyyy-MM-dd HH:mm:ss')
                        : '-'}
                    </TableCell>
                    <TableCell>{getStatusBadge(record.status)}</TableCell>
                    <TableCell>
                      <div className="w-full max-w-xs">
                        <Progress value={record.progress} className="h-2" />
                        <span className="text-xs text-muted-foreground mt-1">
                          {record.progress}%
                        </span>
                      </div>
                    </TableCell>
                    <TableCell className="text-center">{record.epochs}</TableCell>
                    <TableCell className="text-center">{record.batchSize}</TableCell>
                    <TableCell className="font-mono text-sm text-center">
                      {formatLearningRate(record.learningRate)}
                    </TableCell>
                    <TableCell className="text-center">
                      {record.accuracy !== null ? `${(record.accuracy * 100).toFixed(2)}%` : '-'}
                    </TableCell>
                    <TableCell className="text-center">
                      {canPauseTask(record.status) ? (
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => onPauseTask(record.id)}
                          className="px-3 py-1 h-8"
                        >
                          <Pause className="h-4 w-4 mr-1" />
                          暂停
                        </Button>
                      ) : (
                        <span className="text-xs text-muted-foreground">-</span>
                      )}
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </div>
      </div>
    </div>
  )
}

export default function ModelDetailPage() {
  const searchParams = useSearchParams()
  const router = useRouter()
  const { toast } = useToast()
  const id = searchParams?.get('id') || '1' // 默认ID为1，实际应从URL获取
  const [loading, setLoading] = useState(false)
  const [showDialog, setShowDialog] = useState(false)
  const [trainingRecords, setTrainingRecords] = useState<TrainingRecord[]>(mockTrainingRecords)
  const [trainingConfig, setTrainingConfig] = useState<TrainingConfig>({
    epochs: 10,
    batchSize: 32,
    learningRate: 0.001,
  })

  // 在实际应用中，应该使用id从API获取模型详情和训练记录
  const modelDetail = mockModelDetail

  // 打开训练配置对话框
  const openTrainingDialog = () => {
    setShowDialog(true)
  }

  // 处理训练配置变更
  const handleConfigChange = (field: keyof TrainingConfig, value: any) => {
    setTrainingConfig(prev => ({
      ...prev,
      [field]: value,
    }))
  }

  // 暂停训练任务
  const handlePauseTask = (taskId: string) => {
    // 实际项目中应该调用API
    setTrainingRecords(prev =>
      prev.map(record => (record.id === taskId ? { ...record, status: 'paused' as const } : record))
    )

    toast({
      title: '任务已暂停',
      description: `训练任务 ${taskId} 已暂停`,
    })
  }

  // 开始训练
  const handleStartTraining = async () => {
    setLoading(true)
    try {
      // 实际项目中这里应该调用API
      await new Promise(resolve => setTimeout(resolve, 1000))

      // 创建新的训练记录
      const newRecord: TrainingRecord = {
        id: `t${trainingRecords.length + 1}`,
        taskId: id,
        startTime: new Date().toISOString(),
        endTime: null,
        status: 'running',
        progress: 0,
        accuracy: null,
        loss: null,
        dataset: '默认数据集', // 使用默认数据集
        epochs: trainingConfig.epochs,
        batchSize: trainingConfig.batchSize,
        learningRate: trainingConfig.learningRate,
      }

      // 添加到记录列表
      setTrainingRecords(prev => [newRecord, ...prev])

      toast({
        title: '训练已开始',
        description: '新的训练任务已加入队列',
      })

      setShowDialog(false)
    } catch (error) {
      toast({
        title: '创建训练任务失败',
        description: '无法创建新的训练任务',
        variant: 'destructive',
      })
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="p-8 space-y-6">
      <Tabs defaultValue="basic" className="space-y-4">
        <TabsList>
          <TabsTrigger value="basic">基本信息</TabsTrigger>
          <TabsTrigger value="training">训练记录</TabsTrigger>
        </TabsList>

        <TabsContent value="basic" className="space-y-4">
          <Card >
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  {modelDetail.name}
                  <Badge
                    className={
                      modelDetail.status === 'active'
                        ? 'bg-green-100 text-green-800'
                        : 'bg-gray-100 text-gray-800'
                    }
                  >
                    {modelDetail.status === 'active' ? '已激活' : '未激活'}
                  </Badge>
                </div>
                <Badge variant="outline">{modelDetail.version}</Badge>
              </CardTitle>
              <CardDescription>{modelDetail.description}</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <div>
                    <h3 className="text-sm font-medium text-muted-foreground">框架</h3>
                    <p>{modelDetail.framework}</p>
                  </div>
                  <div>
                    <h3 className="text-sm font-medium text-muted-foreground">准确率</h3>
                    <p>{(modelDetail.accuracy * 100).toFixed(2)}%</p>
                  </div>
                  <div>
                    <h3 className="text-sm font-medium text-muted-foreground">模型大小</h3>
                    <p>{modelDetail.size} MB</p>
                  </div>
                  <div>
                    <h3 className="text-sm font-medium text-muted-foreground">参数数量</h3>
                    <p>{(modelDetail.parameters / 1000000).toFixed(1)}M</p>
                  </div>
                </div>
                <div className="space-y-4">
                  <div>
                    <h3 className="text-sm font-medium text-muted-foreground">创建时间</h3>
                    <p>{format(new Date(modelDetail.createdAt), 'yyyy-MM-dd HH:mm:ss')}</p>
                  </div>
                  <div>
                    <h3 className="text-sm font-medium text-muted-foreground">最后更新</h3>
                    <p>{format(new Date(modelDetail.updatedAt), 'yyyy-MM-dd HH:mm:ss')}</p>
                  </div>
                  <div>
                    <h3 className="text-sm font-medium text-muted-foreground">标签</h3>
                    <div className="flex flex-wrap gap-1 mt-1">
                      {modelDetail.tags.map(tag => (
                        <Badge key={tag} variant="secondary">
                          {tag}
                        </Badge>
                      ))}
                    </div>
                  </div>
                </div>
              </div>

              <Separator className="my-6" />

              <div className="space-y-4">
                <h3 className="text-md font-medium">模型性能指标</h3>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <Card>
                    <CardHeader className="pb-2">
                      <CardTitle className="text-sm font-medium text-muted-foreground">
                        准确率
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="text-2xl font-bold">
                        {(modelDetail.accuracy * 100).toFixed(2)}%
                      </div>
                    </CardContent>
                  </Card>
                  <Card>
                    <CardHeader className="pb-2">
                      <CardTitle className="text-sm font-medium text-muted-foreground">
                        推理速度
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="text-2xl font-bold">15ms</div>
                    </CardContent>
                  </Card>
                  <Card>
                    <CardHeader className="pb-2">
                      <CardTitle className="text-sm font-medium text-muted-foreground">
                        训练次数
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="text-2xl font-bold">{trainingRecords.length}</div>
                    </CardContent>
                  </Card>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="training">
          <Card className="shadow-sm">
            <CardHeader className="flex flex-row items-center justify-between">
              <div>
                <CardTitle>训练记录</CardTitle>
                <CardDescription>模型的所有训练历史记录</CardDescription>
              </div>
              <Button
                onClick={openTrainingDialog}
                disabled={loading}
                className="bg-blue-600 hover:bg-blue-700"
              >
                <PlusCircle className="h-4 w-4 mr-2" />
                开始训练
              </Button>
            </CardHeader>
            <CardContent>
              <TrainingRecordsTable records={trainingRecords} onPauseTask={handlePauseTask} />
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* 训练配置对话框 */}
      <Dialog open={showDialog} onOpenChange={setShowDialog}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>训练配置</DialogTitle>
            <DialogDescription>设置训练参数，确认后将开始新的训练任务</DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="epochs" className="text-right">
                训练轮次
              </Label>
              <Input
                id="epochs"
                type="number"
                value={trainingConfig.epochs}
                onChange={e => handleConfigChange('epochs', parseInt(e.target.value))}
                className="col-span-3"
                min={1}
                max={100}
              />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="batchSize" className="text-right">
                批次大小
              </Label>
              <Input
                id="batchSize"
                type="number"
                value={trainingConfig.batchSize}
                onChange={e => handleConfigChange('batchSize', parseInt(e.target.value))}
                className="col-span-3"
                min={8}
                max={256}
                step={8}
              />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="learningRate" className="text-right">
                学习率
              </Label>
              <Input
                id="learningRate"
                type="number"
                value={trainingConfig.learningRate}
                onChange={e => handleConfigChange('learningRate', parseFloat(e.target.value))}
                className="col-span-3"
                min={0.0001}
                max={0.1}
                step={0.0001}
              />
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowDialog(false)}>
              取消
            </Button>
            <Button onClick={handleStartTraining} disabled={loading}>
              {loading ? '处理中...' : '开始训练'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}
