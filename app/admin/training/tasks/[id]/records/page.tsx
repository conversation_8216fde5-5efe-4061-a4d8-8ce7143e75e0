'use client'

import { use<PERSON><PERSON><PERSON>, useRouter } from 'next/navigation'
import { Button } from '@/components/ui/button'
import { ArrowLeft, Play, Download, RotateCw } from 'lucide-react'
import Link from 'next/link'
import { format } from 'date-fns'
import { ColumnDef } from '@tanstack/react-table'
import { DataTable } from '@/components/data-table'
import { cn } from '@/lib/utils'
import { Progress } from '@/components/ui/progress'
import { useEffect, useState } from 'react'
import { useToast } from '@/components/ui/use-toast'

// 训练记录接口
interface TrainingRecord {
  id: number
  taskId: number
  startTime: string
  endTime?: string
  status: string
  epoch: number
  currentEpoch: number
  progress: number
  accuracy: number
  loss: number
  learningRate: number
  batchSize: number
  optimizer: string
  error?: string
}

// 任务基本信息接口
interface TaskBasicInfo {
  id: number
  name: string
  modelName: string
  datasetName: string
}

const getStatusColor = (status: string): string => {
  switch (status) {
    case 'pending':
      return 'text-yellow-600 bg-yellow-50 border-yellow-200'
    case 'training':
      return 'text-blue-600 bg-blue-50 border-blue-200'
    case 'completed':
      return 'text-green-600 bg-green-50 border-green-200'
    case 'error':
      return 'text-red-600 bg-red-50 border-red-200'
    default:
      return 'text-gray-600 bg-gray-50 border-gray-200'
  }
}

const getStatusText = (status: string): string => {
  switch (status) {
    case 'pending':
      return '等待中'
    case 'training':
      return '训练中'
    case 'completed':
      return '已完成'
    case 'error':
      return '错误'
    default:
      return status
  }
}

const StatusCell = ({ row }: { row: any }) => {
  const status = row.original.status
  const error = row.original.error

  return (
    <div className="flex flex-col gap-1 items-start">
      <span
        className={cn(
          'inline-flex items-center justify-center px-2.5 py-0.5 rounded-full text-xs font-medium border whitespace-nowrap',
          getStatusColor(status)
        )}
      >
        {getStatusText(status)}
      </span>
      {error && <span className="text-xs text-red-500">{error}</span>}
    </div>
  )
}

const ProgressCell = ({ row }: { row: any }) => {
  const record = row.original
  return (
    <div className="flex flex-col gap-1 min-w-[200px]">
      <Progress value={(record.currentEpoch / record.epoch) * 100} className="h-2" />
      <div className="flex justify-between text-xs text-muted-foreground">
        <span>
          Epoch {record.currentEpoch}/{record.epoch}
        </span>
        <span>{record.progress}%</span>
      </div>
      {record.status !== 'pending' && (
        <div className="flex justify-between text-xs">
          <span>准确率: {(record.accuracy * 100).toFixed(1)}%</span>
          <span>损失: {record.loss.toFixed(3)}</span>
        </div>
      )}
    </div>
  )
}

export default function TrainingRecordsPage() {
  // 使用类型断言确保 params 不为 null
  const params = useParams() as { id: string }
  const taskId = params.id
  const router = useRouter()
  const { toast } = useToast()
  const [taskInfo, setTaskInfo] = useState<TaskBasicInfo | null>(null)
  const [loading, setLoading] = useState(true)

  // 获取任务基本信息
  useEffect(() => {
    const fetchTaskInfo = async () => {
      try {
        const response = await fetch(`/api/training/tasks/${taskId}/basic`)
        if (!response.ok) {
          throw new Error('获取任务信息失败')
        }
        const data = await response.json()
        setTaskInfo(data)
      } catch (error) {
        console.error('Failed to fetch task info:', error)
        toast({
          title: '获取任务信息失败',
          description: error instanceof Error ? error.message : '获取任务信息时发生错误',
          variant: 'destructive',
        })
      } finally {
        setLoading(false)
      }
    }

    if (taskId) {
      fetchTaskInfo()
    }
  }, [taskId, toast])

  const columns: ColumnDef<TrainingRecord>[] = [
    {
      accessorKey: 'id',
      header: () => <div className="min-w-[80px]">记录ID</div>,
      cell: ({ row }) => <div className="font-medium">{row.original.id}</div>,
    },
    {
      accessorKey: 'startTime',
      header: () => <div className="min-w-[140px]">开始时间</div>,
      cell: ({ row }) => format(new Date(row.original.startTime), 'yyyy-MM-dd HH:mm:ss'),
    },
    {
      accessorKey: 'endTime',
      header: () => <div className="min-w-[140px]">结束时间</div>,
      cell: ({ row }) =>
        row.original.endTime ? format(new Date(row.original.endTime), 'yyyy-MM-dd HH:mm:ss') : '-',
    },
    {
      accessorKey: 'status',
      header: () => <div>状态</div>,
      cell: ({ row }) => <StatusCell row={row} />,
    },
    {
      accessorKey: 'progress',
      header: () => <div className="min-w-[120px]">进度</div>,
      cell: ({ row }) => <ProgressCell row={row} />,
    },
    {
      accessorKey: 'learningRate',
      header: () => <div className="min-w-[100px]">学习率</div>,
      cell: ({ row }) => row.original.learningRate,
    },
    {
      accessorKey: 'batchSize',
      header: () => <div className="min-w-[100px]">批次大小</div>,
      cell: ({ row }) => row.original.batchSize,
    },
    {
      accessorKey: 'optimizer',
      header: () => <div className="min-w-[100px]">优化器</div>,
      cell: ({ row }) => row.original.optimizer,
    },
    {
      id: 'actions',
      header: () => <div className="min-w-[120px]">操作</div>,
      cell: ({ row }) => (
        <div className="flex space-x-2">
          {row.original.status === 'completed' && (
            <Button
              variant="outline"
              size="sm"
              onClick={() =>
                window.open(`/api/training/records/${row.original.id}/download`, '_blank')
              }
              className="h-8 whitespace-nowrap"
            >
              <Download className="h-4 w-4 mr-2" />
              下载模型
            </Button>
          )}
          {row.original.status === 'error' && (
            <Button
              variant="outline"
              size="sm"
              onClick={() => {
                // 重新训练逻辑
                fetch(`/api/training/records/${row.original.id}/retry`, {
                  method: 'POST',
                })
                  .then(response => {
                    if (!response.ok) throw new Error('重新训练失败')
                    toast({
                      title: '重新训练已开始',
                      description: '训练记录已重新加入训练队列',
                    })
                    // 刷新数据
                    setTimeout(() => {
                      window.location.reload()
                    }, 1000)
                  })
                  .catch(error => {
                    toast({
                      title: '重新训练失败',
                      description: error.message,
                      variant: 'destructive',
                    })
                  })
              }}
              className="h-8 whitespace-nowrap"
            >
              <RotateCw className="h-4 w-4 mr-2" />
              重新训练
            </Button>
          )}
        </div>
      ),
    },
  ]

  if (!taskId) {
    return (
      <div className="p-8">
        <div className="text-center text-red-500">任务ID无效</div>
      </div>
    )
  }

  return (
    <div className="p-8">
      <div className="flex items-center mb-6">
        <Link href={`/admin/training/tasks/${taskId}`} className="mr-4">
          <Button variant="outline" size="icon">
            <ArrowLeft className="h-4 w-4" />
          </Button>
        </Link>
        <h1 className="text-3xl font-bold">
          {loading ? '加载中...' : `${taskInfo?.name || '未知任务'} - 训练记录`}
        </h1>
        {taskInfo && (
          <Button
            variant="outline"
            size="sm"
            onClick={() => {
              // 创建新训练记录
              fetch(`/api/training/tasks/${taskId}/records`, {
                method: 'POST',
              })
                .then(response => {
                  if (!response.ok) throw new Error('创建训练记录失败')
                  toast({
                    title: '新训练已开始',
                    description: '新的训练记录已创建并加入训练队列',
                  })
                  // 刷新数据
                  setTimeout(() => {
                    window.location.reload()
                  }, 1000)
                })
                .catch(error => {
                  toast({
                    title: '创建训练记录失败',
                    description: error.message,
                    variant: 'destructive',
                  })
                })
            }}
            className="ml-auto h-8 bg-blue-600 hover:bg-blue-700 text-white"
          >
            <Play className="h-4 w-4 mr-2" />
            开始新训练
          </Button>
        )}
      </div>

      {taskInfo && (
        <div className="mb-6 text-sm text-muted-foreground">
          <p>
            模型: {taskInfo.modelName} | 数据集: {taskInfo.datasetName}
          </p>
        </div>
      )}

      <DataTable url={`/api/training/tasks/${taskId}/records`} columns={columns} />
    </div>
  )
}
