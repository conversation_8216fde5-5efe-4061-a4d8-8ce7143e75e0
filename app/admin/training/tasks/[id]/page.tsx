'use client'

import { use<PERSON><PERSON><PERSON>, useRouter } from 'next/navigation'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { ArrowLeft, Play, Pause, StopCircle, RotateCw, History } from 'lucide-react'
import Link from 'next/link'
import { useToast } from '@/components/ui/use-toast'
import { useEffect, useState } from 'react'
import { format } from 'date-fns'

interface Task {
  id: string
  name: string
  status: string
  progress: number
  createdAt: string
  description?: string
  modelType?: string
  datasetSize?: number
  epochs?: number
  learningRate?: number
  error?: string
  recordCount?: number
  lastTrainedAt?: string
  accuracy?: number
  loss?: number
}

// 模拟的任务数据
const mockTasks: Record<string, Task> = {
  '1': {
    id: '1',
    name: '表情识别模型V1',
    status: '进行中',
    progress: 75,
    createdAt: '2023-05-20',
    description: '基于深度学习的表情识别模型，支持7种基本表情识别',
    modelType: 'CNN + Transformer',
    datasetSize: 10000,
    epochs: 100,
    learningRate: 0.001,
  },
  '2': {
    id: '2',
    name: '多人表情识别模型',
    status: '等待中',
    progress: 0,
    createdAt: '2023-05-21',
    description: '支持多人同时表情识别的增强版模型',
    modelType: 'YOLO + ResNet',
    datasetSize: 15000,
    epochs: 150,
    learningRate: 0.0005,
  },
  '3': {
    id: '3',
    name: '高级表情识别模型V2',
    status: '完成',
    progress: 100,
    createdAt: '2023-05-19',
    description: '改进版表情识别模型，提高了识别准确率',
    modelType: 'EfficientNet',
    datasetSize: 20000,
    epochs: 200,
    learningRate: 0.0003,
  },
}

export default function TaskDetailPage() {
  const params = useParams() as { id: string }
  const taskId = params.id
  const router = useRouter()
  const { toast } = useToast()
  const [task, setTask] = useState<Task | null>(null)
  const [loading, setLoading] = useState(false)

  // 获取任务详情
  const fetchTaskDetail = async () => {
    try {
      const response = await fetch(`/api/training/tasks/${taskId}`)
      if (!response.ok) {
        throw new Error('获取任务详情失败')
      }
      const data = await response.json()
      setTask(data)
    } catch (error) {
      console.error('Failed to fetch task:', error)
      toast({
        title: '获取任务失败',
        description: error instanceof Error ? error.message : '获取任务详情时发生错误',
        variant: 'destructive',
      })
    }
  }

  useEffect(() => {
    if (taskId) {
      fetchTaskDetail()
    }
  }, [taskId])

  // 重新训练任务
  const handleRetry = async () => {
    if (!task) return

    setLoading(true)
    try {
      const response = await fetch(`/api/training/tasks/${task.id}/retry`, {
        method: 'POST',
      })

      if (!response.ok) {
        throw new Error('重新训练失败')
      }

      toast({
        title: '重新训练已开始',
        description: '任务已重新加入训练队列',
      })

      // 刷新任务状态
      fetchTaskDetail()
    } catch (error) {
      console.error('Failed to retry task:', error)
      toast({
        title: '重新训练失败',
        description: error instanceof Error ? error.message : '重新训练时发生错误',
        variant: 'destructive',
      })
    } finally {
      setLoading(false)
    }
  }

  // 开始新训练
  const handleStartNewTraining = async () => {
    if (!task) return

    setLoading(true)
    try {
      const response = await fetch(`/api/training/tasks/${task.id}/records`, {
        method: 'POST',
      })

      if (!response.ok) {
        throw new Error('创建训练记录失败')
      }

      toast({
        title: '新训练已开始',
        description: '新的训练记录已创建并加入训练队列',
      })

      // 刷新任务状态
      fetchTaskDetail()
    } catch (error) {
      console.error('Failed to start new training:', error)
      toast({
        title: '创建训练记录失败',
        description: error instanceof Error ? error.message : '创建训练记录时发生错误',
        variant: 'destructive',
      })
    } finally {
      setLoading(false)
    }
  }

  if (!task) {
    return (
      <div className="p-8">
        <div className="text-center text-gray-500">加载中...</div>
      </div>
    )
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'training':
        return 'text-blue-600 bg-blue-50'
      case 'pending':
        return 'text-yellow-600 bg-yellow-50'
      case 'completed':
        return 'text-green-600 bg-green-50'
      case 'error':
        return 'text-red-600 bg-red-50'
      default:
        return 'text-gray-600 bg-gray-50'
    }
  }

  return (
    <div className="p-8">
      <div className="flex items-center mb-6">
        <Link href="/admin/training/tasks" className="mr-4">
          <Button variant="outline" size="icon">
            <ArrowLeft className="h-4 w-4" />
          </Button>
        </Link>
        <h1 className="text-3xl font-bold">任务详情</h1>
        <div className="ml-auto flex gap-2">
          <Button
            variant="outline"
            onClick={() => router.push(`/admin/training/tasks/${task.id}/records`)}
          >
            <History className="h-4 w-4 mr-2" />
            查看训练记录
          </Button>
          <Button
            variant="default"
            className="bg-blue-600 hover:bg-blue-700"
            onClick={handleStartNewTraining}
            disabled={loading || task.status === 'training'}
          >
            <Play className="h-4 w-4 mr-2" />
            开始新训练
          </Button>
        </div>
      </div>

      <div className="grid gap-6">
        <Card>
          <CardHeader>
            <CardTitle>基本信息</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid gap-4">
              <div className="flex justify-between items-center">
                <div>
                  <h2 className="text-xl font-semibold">{task.name}</h2>
                  <p className="text-gray-500 mt-1">创建时间：{task.createdAt}</p>
                  {task.recordCount !== undefined && (
                    <p className="text-gray-500 mt-1">
                      训练记录：{task.recordCount} 次
                      {task.lastTrainedAt && (
                        <span className="ml-2">
                          (最近：{format(new Date(task.lastTrainedAt), 'yyyy-MM-dd HH:mm:ss')})
                        </span>
                      )}
                    </p>
                  )}
                </div>
                <div className="flex gap-2">
                  {task.status === 'error' && (
                    <Button variant="outline" onClick={handleRetry} disabled={loading}>
                      <RotateCw className="h-4 w-4 mr-2" />
                      {loading ? '重新训练中...' : '重新训练'}
                    </Button>
                  )}
                </div>
              </div>
              <div>
                <span
                  className={`inline-block px-3 py-1 rounded-full text-sm ${getStatusColor(task.status)}`}
                >
                  {task.status === 'training'
                    ? '训练中'
                    : task.status === 'pending'
                      ? '等待中'
                      : task.status === 'completed'
                        ? '已完成'
                        : task.status === 'error'
                          ? '错误'
                          : task.status}
                </span>
                {task.error && <p className="mt-2 text-sm text-red-500">{task.error}</p>}
              </div>
              <div className="mt-2">
                <p className="text-gray-600">{task.description}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>训练参数</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <p className="text-sm text-gray-500">模型类型</p>
                <p className="font-medium">{task.modelType}</p>
              </div>
              <div>
                <p className="text-sm text-gray-500">数据集大小</p>
                <p className="font-medium">{task.datasetSize?.toLocaleString()} 条</p>
              </div>
              <div>
                <p className="text-sm text-gray-500">训练轮次</p>
                <p className="font-medium">{task.epochs} epochs</p>
              </div>
              <div>
                <p className="text-sm text-gray-500">学习率</p>
                <p className="font-medium">{task.learningRate}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>最新训练结果</CardTitle>
          </CardHeader>
          <CardContent>
            {task.recordCount && task.recordCount > 0 ? (
              <div className="space-y-4">
                {task.status === 'training' && (
                  <>
                    <div className="w-full bg-gray-200 rounded-full h-4">
                      <div
                        className="bg-indigo-600 h-4 rounded-full transition-all duration-300"
                        style={{ width: `${task.progress}%` }}
                      />
                    </div>
                    <p className="text-right text-sm text-gray-500">{task.progress}%</p>
                  </>
                )}

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <p className="text-sm text-gray-500">准确率</p>
                    <p className="font-medium">
                      {task.accuracy !== undefined
                        ? `${(task.accuracy * 100).toFixed(1)}%`
                        : '未知'}
                    </p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-500">损失</p>
                    <p className="font-medium">
                      {task.loss !== undefined ? task.loss.toFixed(3) : '未知'}
                    </p>
                  </div>
                </div>
              </div>
            ) : (
              <p className="text-gray-500">尚未开始训练</p>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
