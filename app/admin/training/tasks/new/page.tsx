'use client'

import { <PERSON><PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group'
import { useToast } from '@/components/ui/use-toast'
import { useRouter } from 'next/navigation'
import { useState } from 'react'
import { Combobox } from '@/components/ui/combobox'

interface FormData {
  name: string
  modelId: string
  datasetId: string
  epoch: string
  taskType: string
  learningRate: string
  executionMethod?: string
  executionInterval?: string
}

export default function NewTaskPage() {
  const router = useRouter()
  const { toast } = useToast()
  const [loading, setLoading] = useState(false)
  const [formData, setFormData] = useState<FormData>({
    name: '',
    modelId: '',
    datasetId: '',
    epoch: '100',
    taskType: 'one-time',
    learningRate: '0.001',
    executionMethod: 'daily',
    executionInterval: '1',
  })

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)

    try {
      const response = await fetch('/api/training/tasks', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          name: formData.name,
          modelId: parseInt(formData.modelId),
          datasetId: parseInt(formData.datasetId),
          epoch: parseInt(formData.epoch),
          taskType: formData.taskType,
          learningRate: parseFloat(formData.learningRate),
          ...(formData.taskType === 'recurring' && {
            executionMethod: formData.executionMethod,
            executionInterval: parseInt(formData.executionInterval || '1'),
          }),
        }),
      })

      const data = await response.json()

      if (!response.ok) {
        throw new Error(data.error || '创建任务失败')
      }

      toast({
        title: '创建成功',
        description: '任务已创建，正在跳转到任务列表...',
      })

      const newTaskId = data.id || data.taskId
      const redirectUrl = `/admin/training/tasks?page=1&sortBy=createdAt&sortOrder=desc${newTaskId ? `&newTaskId=${newTaskId}` : ''}`
      router.push(redirectUrl)
    } catch (error) {
      console.error('Failed to create task:', error)
      toast({
        title: '创建失败',
        description: error instanceof Error ? error.message : '创建任务时发生错误',
        variant: 'destructive',
      })
    } finally {
      setLoading(false)
    }
  }

  return (
    <form onSubmit={handleSubmit}>
      <Card>
        <CardHeader>
          <CardTitle>创建训练任务</CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="space-y-2">
            <Label htmlFor="name">任务名称</Label>
            <Input
              id="name"
              placeholder="请输入任务名称"
              value={formData.name}
              onChange={e => setFormData(prev => ({ ...prev, name: e.target.value }))}
              required
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="taskType">任务类型</Label>
            <RadioGroup
              id="taskType"
              value={formData.taskType}
              onValueChange={value => setFormData(prev => ({ ...prev, taskType: value }))}
              className="flex space-x-4"
            >
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="one-time" id="one-time" />
                <Label htmlFor="one-time" className="cursor-pointer">一次性任务</Label>
              </div>
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="recurring" id="recurring" />
                <Label htmlFor="recurring" className="cursor-pointer">例行任务</Label>
              </div>
            </RadioGroup>
          </div>

          {formData.taskType === 'recurring' && (
            <div className="space-y-2 rounded-md border border-gray-200 p-4 bg-gray-50">
              <div className="text-sm font-medium mb-3">执行方式</div>
              
              <div className="space-y-4">
                <div className="flex items-center space-x-2">
                  <RadioGroup
                    id="executionMethod"
                    value={formData.executionMethod}
                    onValueChange={value => setFormData(prev => ({ ...prev, executionMethod: value }))}
                    className="flex flex-col space-y-2"
                  >
                    <div className="flex items-center space-x-2">
                      <RadioGroupItem value="daily" id="daily" />
                      <Label htmlFor="daily" className="cursor-pointer">每日执行</Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <RadioGroupItem value="weekly" id="weekly" />
                      <Label htmlFor="weekly" className="cursor-pointer">每周执行</Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <RadioGroupItem value="monthly" id="monthly" />
                      <Label htmlFor="monthly" className="cursor-pointer">每月执行</Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <RadioGroupItem value="custom" id="custom" />
                      <Label htmlFor="custom" className="cursor-pointer">自定义间隔</Label>
                    </div>
                  </RadioGroup>
                </div>
                
                {formData.executionMethod === 'custom' && (
                  <div className="flex items-center space-x-2">
                    <Label htmlFor="executionInterval" className="whitespace-nowrap">间隔天数</Label>
                    <Input
                      id="executionInterval"
                      type="number"
                      min="1"
                      max="365"
                      className="w-24"
                      value={formData.executionInterval}
                      onChange={e => setFormData(prev => ({ ...prev, executionInterval: e.target.value }))}
                      required={formData.executionMethod === 'custom'}
                    />
                    <span className="text-sm text-gray-500">天</span>
                  </div>
                )}
              </div>
            </div>
          )}

          <div className="space-y-2">
            <Label htmlFor="model">选择模型</Label>
            <Combobox
              fetchUrl="/api/training/models/options"
              value={formData.modelId}
              onValueChange={value => setFormData(prev => ({ ...prev, modelId: value }))}
              placeholder="请选择模型"
              searchPlaceholder="搜索模型..."
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="dataset">选择数据集</Label>
            <Combobox
              fetchUrl={`/api/training/datasets/options`}
              value={formData.datasetId}
              onValueChange={value => setFormData(prev => ({ ...prev, datasetId: value }))}
              placeholder="请选择数据集"
              searchPlaceholder="搜索数据集..."
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="epoch">训练轮数</Label>
            <Input
              id="epoch"
              type="number"
              min="1"
              max="1000"
              placeholder="请输入训练轮数"
              value={formData.epoch}
              onChange={e => setFormData(prev => ({ ...prev, epoch: e.target.value }))}
              required
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="learningRate">学习率</Label>
            <Input
              id="learningRate"
              type="number"
              min="0.0001"
              max="1"
              step="0.0001"
              placeholder="请输入学习率"
              value={formData.learningRate}
              onChange={e => setFormData(prev => ({ ...prev, learningRate: e.target.value }))}
              required
            />
          </div>

          <div className="flex justify-end space-x-4">
            <Button
              type="button"
              variant="outline"
              onClick={() => router.back()}
              disabled={loading}
            >
              取消
            </Button>
            <Button type="submit" disabled={loading}>
              {loading ? '创建中...' : '创建任务'}
            </Button>
          </div>
        </CardContent>
      </Card>
    </form>
  )
}
