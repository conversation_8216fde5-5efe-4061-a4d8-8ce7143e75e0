'use client'

import { Button } from '@/components/ui/button'
import { Download, Plus, Trash2 } from 'lucide-react'
import { collectionService } from '@/services/collectionService'
import { useToast } from '@/components/ui/use-toast'
import { useState } from 'react'

interface TableActionsProps {
  selectedIds: number[]
  onUpload: () => void
}

export function TableActions({ selectedIds, onUpload }: TableActionsProps) {
  const { toast } = useToast()
  const [isExporting, setIsExporting] = useState(false)

  const handleExport = async () => {
    if (selectedIds.length === 0) {
      toast({
        title: '提示',
        description: '请先选择要导出的数据项',
        variant: 'destructive',
      })
      return
    }

    setIsExporting(true)
    try {
      await collectionService.exportDataItems(selectedIds)
      toast({
        title: '导出成功',
        description: `已成功导出 ${selectedIds.length} 个数据项`,
      })
    } catch (error) {
      console.error('导出失败:', error)
      toast({
        title: '导出失败',
        description: '导出过程中发生错误，请重试',
        variant: 'destructive',
      })
    } finally {
      setIsExporting(false)
    }
  }

  const handleDelete = async () => {
    // 删除逻辑
    collectionService.deleteDataItems(selectedIds)
  }

  return (
    <div className="flex items-center gap-2">
      <Button
        variant="outline"
        size="sm"
        onClick={handleExport}
        disabled={selectedIds.length === 0 || isExporting}
        className="h-8"
      >
        <Download className="h-4 w-4 mr-2" />
        {isExporting ? '导出中...' : `导出 (${selectedIds.length})`}
      </Button>
      <Button
        variant="outline"
        size="sm"
        onClick={handleDelete}
        disabled={selectedIds.length === 0}
        className="h-8"
      >
        <Trash2 className="h-4 w-4 mr-2" />
        删除 ({selectedIds.length})
      </Button>
      <Button size="sm" className="h-8 bg-blue-600 hover:bg-blue-700" onClick={onUpload}>
        <Plus className="h-4 w-4 mr-2" />
        上传数据
      </Button>
    </div>
  )
}
