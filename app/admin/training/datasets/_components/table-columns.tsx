'use client'

import { Button } from '@/components/ui/button'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { cn } from '@/lib/utils'
import { DataSet } from '@/services/datasetService'
import { ColumnDef } from '@tanstack/react-table'
import { format } from 'date-fns'
import { Download, Edit, Eye, MoreHorizontal, Trash2 } from 'lucide-react'

const DatasetNameCell = ({ row }: { row: any }) => (
  <div className="min-w-[200px]">
    <div className="font-medium">{row.getValue('name')}</div>
    {row.original.description && (
      <div className="text-sm text-muted-foreground truncate">{row.original.description}</div>
    )}
  </div>
)

const TypeCell = ({ row }: { row: any }) => {
  const type = row.getValue('type') as string
  const typeMap: Record<string, { label: string; color: string }> = {
    face: { label: '人脸表情', color: 'text-blue-600 bg-blue-50 border-blue-200' },
    scene: { label: '场景表情', color: 'text-green-600 bg-green-50 border-green-200' },
    video: { label: '视频流', color: 'text-purple-600 bg-purple-50 border-purple-200' },
  }
  const { label, color } = typeMap[type] || {
    label: type,
    color: 'text-gray-600 bg-gray-50 border-gray-200',
  }

  return (
    <div className="min-w-[100px]">
      <span
        className={cn('inline-flex px-2.5 py-0.5 rounded-full text-xs font-medium border', color)}
      >
        {label}
      </span>
    </div>
  )
}

export function getTableColumns(
  handlePreviewClick: (dataset: DataSet) => void,
  handleExportClick: (dataset: DataSet) => void
) {
  // const handlePreviewClick = (dataset: DataSet) => {
  //   setSelectedDataset(dataset)
  //   setIsPreviewDrawerOpen(true)
  // }

  // const handleExportClick = (dataset: DataSet) => {
  //   setSelectedDataset(dataset)
  //   setIsExportDialogOpen(true)
  // }

  // 列定义
  const tableColumns: ColumnDef<DataSet, any>[] = [
    {
      accessorKey: 'datasetCode',
      header: '数据集编码',
    },
    {
      accessorKey: 'name',
      header: () => <div className="min-w-[200px]">名称</div>,
      cell: ({ row }) => <DatasetNameCell row={row} />,
    },
    {
      accessorKey: 'type',
      header: () => <div className="min-w-[100px]">类型</div>,
      cell: ({ row }) => <TypeCell row={row} />,
    },
    {
      accessorKey: 'resourceCount',
      header: () => <div className="min-w-[100px]">资源数量</div>,
      cell: ({ row }) => <div className="min-w-[100px]">{row.getValue('resourceCount')}</div>,
    },
    {
      accessorKey: 'keepUpdated',
      header: () => <div className="min-w-[100px]">是否自动更新</div>,
      cell: ({ row }) => (
        <div className="min-w-[100px]">{row.getValue('keepUpdated') ? '是' : '否'}</div>
      ),
    },
    {
      accessorKey: 'createTime',
      header: () => <div className="min-w-[140px]">创建时间</div>,
      cell: ({ row }) => format(new Date(row.getValue('createTime')), 'yyyy-MM-dd HH:mm:ss'),
    },
    {
      id: 'actions',
      header: () => <div className="min-w-[200px]">操作</div>,
      cell: ({ row }) => (
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            size="sm"
            className="h-8 whitespace-nowrap"
            onClick={() => handlePreviewClick(row.original)}
          >
            <Eye className="h-4 w-4 mr-2" />
            预览
          </Button>
          <Button
            variant="outline"
            size="sm"
            className="h-8 whitespace-nowrap"
            onClick={() => handleExportClick(row.original)}
          >
            <Download className="h-4 w-4 mr-2" />
            导出
          </Button>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="sm" className="h-8">
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem>
                <Edit className="h-4 w-4 mr-2" />
                编辑
              </DropdownMenuItem>
              <DropdownMenuItem className="text-red-600">
                <Trash2 className="h-4 w-4 mr-2" />
                删除
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      ),
    },
  ]
  return tableColumns
}
