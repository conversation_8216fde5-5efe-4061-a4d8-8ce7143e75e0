import type { Filter } from '@/components/data-table/table-filter-section'

export const tableFilters: Filter[] = [
  {
    type: 'input',
    name: 'name',
    label: '数据集名称',
    placeholder: '请输入数据集名称',
  },
  {
    type: 'select',
    name: 'type',
    label: '数据集类型',
    placeholder: '选择类型',
    options: [
      { value: 'face', label: '人脸表情' },
      { value: 'scene', label: '场景表情' },
      { value: 'video', label: '视频流' },
    ],
  },
  {
    type: 'select',
    name: 'size',
    label: '数据集大小',
    placeholder: '选择大小范围',
    options: [
      { value: 'small', label: '小型 (<1GB)' },
      { value: 'medium', label: '中型 (1-5GB)' },
      { value: 'large', label: '大型 (>5GB)' },
    ],
  },
  {
    type: 'dateRange',
    name: 'dateRange',
    label: '创建时间',
    placeholder: '选择日期范围',
  },
]
