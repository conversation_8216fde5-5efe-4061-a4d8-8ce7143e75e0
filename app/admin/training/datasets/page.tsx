'use client'

import { DataTable } from '@/components/data-table'
import { CreateDatasetDialog } from '@/components/datasets/create-dataset-dialog'
import { DatasetPreviewDrawer } from '@/components/datasets/DatasetPreviewDrawer'
import { ExportDatasetDialog } from '@/components/datasets/export-dataset-dialog'
import { Button } from '@/components/ui/button'
import { Plus } from 'lucide-react'
import { useState } from 'react'
import { getTableColumns, tableFilters } from './_components'
import { DataSet } from '@/services/datasetService'

export default function DatasetPage() {
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false)
  const [isPreviewDrawerOpen, setIsPreviewDrawerOpen] = useState(false)
  const [isExportDialogOpen, setIsExportDialogOpen] = useState(false)
  const [selectedDataset, setSelectedDataset] = useState<DataSet | null>(null)

  const handleExportConfirm = async () => {
    // 模拟导出操作
    return new Promise<void>(resolve => {
      setTimeout(() => {
        console.log(`导出数据集: ${selectedDataset?.name}`)
        resolve()
      }, 1500)
    })
  }

  const handlePreviewClick = (dataset: DataSet) => {
    setSelectedDataset(dataset)
    setIsPreviewDrawerOpen(true)
  }

  const handleExportClick = (dataset: DataSet) => {
    setSelectedDataset(dataset)
    setIsExportDialogOpen(true)
  }

  const handleDrawerClose = (open: boolean) => {
    setIsPreviewDrawerOpen(open)
    if (!open) {
      // 延迟清除选中的数据集，以便抽屉关闭动画完成
      setTimeout(() => {
        setSelectedDataset(null)
      }, 300)
    }
  }

  return (
    <>
      <DataTable
        url="/dataset/list"
        columns={getTableColumns(handlePreviewClick, handleExportClick)}
        filters={tableFilters}
        actions={
          <>
            <Button
              size="sm"
              className="h-8 bg-blue-600 hover:bg-blue-700"
              onClick={() => setIsCreateDialogOpen(true)}
            >
              <Plus className="mr-2 h-4 w-4" />
              新建数据集
            </Button>
            <CreateDatasetDialog
              open={isCreateDialogOpen}
              onOpenChange={setIsCreateDialogOpen}
              onConfirm={data => {
                // TODO: Handle dataset creation
                console.log('Create dataset:', data)
              }}
            />
          </>
        }
      />

      {selectedDataset && (
        <>
          <DatasetPreviewDrawer
            open={isPreviewDrawerOpen}
            onOpenChange={handleDrawerClose}
            datasetInfo={{
              name: selectedDataset.name,
              type:
                selectedDataset.type === 'face'
                  ? '人脸表情'
                  : selectedDataset.type === 'scene'
                    ? '场景表情'
                    : selectedDataset.type === 'video'
                      ? '视频流'
                      : selectedDataset.type,
              totalImages: selectedDataset.imageCount,
              createdAt: new Date(selectedDataset.createdAt),
              labels: ['happy', 'sad', 'angry', 'surprised', 'neutral'], // 示例标签，实际应从API获取
            }}
          />

          <ExportDatasetDialog
            open={isExportDialogOpen}
            onOpenChange={setIsExportDialogOpen}
            datasetName={selectedDataset.name}
            onConfirm={handleExportConfirm}
          />
        </>
      )}
    </>
  )
}
