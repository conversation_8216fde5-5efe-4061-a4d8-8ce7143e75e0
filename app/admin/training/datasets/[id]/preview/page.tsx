'use client'

import { useEffect, useState } from 'react'
import { useParams, useRouter } from 'next/navigation'
import { DatasetPreviewDrawer } from '@/components/datasets/DatasetPreviewDrawer'

interface Dataset {
  id: number
  name: string
  description: string | null
  type: string
  size: number
  imageCount: number
  createdAt: string
  updatedAt: string
  path: string
  createdById: number
  updatedById: number | null
}

export default function DatasetPreviewPage() {
  const params = useParams<{ id: string }>()
  const router = useRouter()
  const [isLoading, setIsLoading] = useState(true)
  const [dataset, setDataset] = useState<Dataset | null>(null)
  const [isDrawerOpen, setIsDrawerOpen] = useState(true)

  useEffect(() => {
    // 在实际应用中，这里应该从API获取数据集信息
    // 这里使用模拟数据
    const datasetId = params?.id ? Number(params.id) : 0
    
    const mockDataset: Dataset = {
      id: datasetId,
      name: `数据集 ${datasetId}`,
      description: '这是一个示例数据集描述',
      type: ['face', 'scene', 'video'][Math.floor(Math.random() * 3)],
      size: Math.floor(Math.random() * 1000000000),
      imageCount: Math.floor(Math.random() * 1000) + 100,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      path: '/datasets/example',
      createdById: 1,
      updatedById: null
    }

    setDataset(mockDataset)
    setIsLoading(false)
  }, [params?.id])

  const handleDrawerClose = () => {
    setIsDrawerOpen(false)
    // 返回到数据集列表页
    setTimeout(() => {
      router.push('/admin/training/datasets')
    }, 300)
  }

  if (isLoading) {
    return <div className="p-8">加载中...</div>
  }

  if (!dataset) {
    return <div className="p-8">未找到数据集</div>
  }

  return (
    <div>
      {dataset && (
        <DatasetPreviewDrawer
          open={isDrawerOpen}
          onOpenChange={handleDrawerClose}
          datasetInfo={{
            name: dataset.name,
            type: dataset.type === 'face' ? '人脸表情' : 
                  dataset.type === 'scene' ? '场景表情' : 
                  dataset.type === 'video' ? '视频流' : dataset.type,
            totalImages: dataset.imageCount,
            createdAt: new Date(dataset.createdAt),
            labels: ['happy', 'sad', 'angry', 'surprised', 'neutral'] // 示例标签，实际应从API获取
          }}
        />
      )}
    </div>
  )
} 