'use client'

import { useState } from 'react'

import { DataTable } from '@/components/data-table'
import { DataItem } from '@/services/collectionService'
import { TableActions, tableColumns, tableFilters } from './_components'

export default function AnnotationWorkbenchPage() {
  const [selectedItems, setSelectedItems] = useState<DataItem[]>([])
  return (
    <div className="relative">
      <DataTable
        url="/data-item/list"
        columns={tableColumns}
        filters={tableFilters}
        onSelectionChange={setSelectedItems}
        actions={<TableActions selectedItems={selectedItems} />}
      />
    </div>
  )
}
