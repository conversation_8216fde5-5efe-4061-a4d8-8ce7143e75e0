'use client'

import { AnnotationDataConfirmAlert } from '@/app/admin/annotation/workbench/_components/annotation-data-confirm'
import { Button } from '@/components/ui/button'
import { toast } from '@/components/ui/use-toast'
import { cn } from '@/lib/utils'
import { DataItem } from '@/services/collectionService'
import { DataItemStatusHelper, MediaTypeHelper } from '@/types'
import { Play } from 'lucide-react'
import { useRouter } from 'next/navigation'
import { useEffect, useState } from 'react'
import SelectedStats from './selected-stats'

interface TableActionsProps {
  selectedItems: DataItem[]
}

export function TableActions({ selectedItems }: TableActionsProps) {
  const router = useRouter()

  const [showAnnotatedAlert, setShowAnnotatedAlert] = useState(false)
  const [selectionStats, setSelectionStats] = useState({
    total: 0,
    annotated: 0,
    pending: 0,
    images: 0,
    annotatedImages: 0,
    videos: 0,
    annotatedVideos: 0,
  })

  useEffect(() => {
    let total = 0
    let annotated = 0
    let pending = 0
    let images = 0
    let videos = 0
    let annotatedImages = 0
    let annotatedVideos = 0
    if (selectedItems.length > 0) {
      selectedItems.forEach(item => {
        total++
        if (DataItemStatusHelper.isAnnotated(item.itemStatus)) {
          annotated++
        } else {
          pending++
        }
        if (MediaTypeHelper.isImage(item.itemType)) {
          images++
          if (DataItemStatusHelper.isAnnotated(item.itemStatus)) {
            annotatedImages++
          }
        } else {
          videos++
          if (DataItemStatusHelper.isAnnotated(item.itemStatus)) {
            annotatedVideos++
          }
        }
      })
    }
    setSelectionStats({
      total,
      annotated,
      pending,
      images,
      videos,
      annotatedImages,
      annotatedVideos,
    })
  }, [selectedItems])

  const handleStartAnnotation = async () => {
    // 校验1：有且仅能选择一种类型的数据，不能同时选择图片和视频
    const selectedTypes = Array.from(new Set(selectedItems.map(item => item.itemType)))

    if (selectedTypes.length > 1) {
      toast({
        title: '类型错误',
        description: '只能选择一种类型的数据，不能同时选择图片和视频',
        variant: 'destructive',
      })
      return
    }

    // 校验2：如果选择的是视频类型，有且仅能选择一个视频
    if (selectedTypes[0] === 'video' && selectedItems.length > 1) {
      toast({
        title: '选择错误',
        description: '视频类型只能选择一个进行标注',
        variant: 'destructive',
      })
      return
    }

    // 校验3：检查是否包含已标注的数据
    const annotatedItems = selectedItems.filter(item =>
      DataItemStatusHelper.isAnnotated(item.itemStatus)
    )
    if (annotatedItems.length > 0) {
      // 如果包含已标注数据，显示确认弹窗
      setShowAnnotatedAlert(true)
      return
    }

    // 没有已标注数据，直接进行标注处理
    proceedWithAnnotation()
  }

  // 提取标注处理逻辑为单独的函数
  const proceedWithAnnotation = () => {
    // 通过校验后，根据选择的类型跳转到对应的标注页面
    const isImage = MediaTypeHelper.isImage(selectedItems[0].itemType)
    const isVideo = MediaTypeHelper.isVideo(selectedItems[0].itemType)

    // 将选择的数据项保存到会话存储中，以便在目标页面获取
    sessionStorage.setItem('annotationItems', JSON.stringify(selectedItems))

    if (isImage) {
      router.push('/admin/annotation/image')
    } else if (isVideo) {
      router.push('/admin/annotation/video')
    } else {
      toast({
        title: '类型错误',
        description: '只能选择一种类型的数据，不能同时选择图片和视频',
        variant: 'destructive',
      })
      return
    }
  }

  return (
    <div className="relative group">
      <div className="flex items-center gap-2">
        <Button
          className={cn(
            'h-9 px-4 flex items-center gap-2 transition-all duration-200',
            selectedItems.length === 0
              ? 'bg-gray-100 text-gray-500 hover:bg-gray-200'
              : selectionStats.annotated > 0
                ? 'bg-amber-600 text-white hover:bg-amber-700'
                : 'bg-green-600 text-white hover:bg-green-700'
          )}
          onClick={handleStartAnnotation}
          disabled={selectedItems.length === 0}
        >
          <Play className="h-4 w-4" />
          <span>开始标注</span>
          {selectedItems.length > 0 && (
            <span className="ml-1 bg-white/20 text-xs font-medium rounded-full px-1.5 py-0.5 min-w-5 text-center">
              {selectedItems.length}
            </span>
          )}
        </Button>

        {selectionStats.total > 0 && <SelectedStats selectedStats={selectionStats} />}
      </div>

      {/* 已标注数据确认弹窗 */}
      <AnnotationDataConfirmAlert
        annotatedStats={selectionStats}
        setShowAnnotatedAlert={setShowAnnotatedAlert}
        proceedWithAnnotation={proceedWithAnnotation}
        showAnnotatedAlert={showAnnotatedAlert}
      />
    </div>
  )
}
