'use client'

import ImagePreview from '@/components/ImagePreview'
import { Badge } from '@/components/ui/badge'
import { Toolt<PERSON>, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip'
import { cn } from '@/lib/utils'
import { DataItem } from '@/services/collectionService'
import { DataItemStatusHelper, EmotionHelper, MediaTypeHelper } from '@/types'
import { ColumnDef } from '@tanstack/react-table'
import { format } from 'date-fns'
import { CheckCircle2, ImageIcon, VideoIcon } from 'lucide-react'

// 列定义
export const tableColumns: ColumnDef<DataItem, any>[] = [
  {
    accessorKey: 'preview',
    header: '预览',
    cell: ({ row }) => <ImagePreview itemId={row.original.itemId} alt={row.original.itemName} />,
  },
  {
    accessorKey: 'name',
    header: '数据名称',
    cell: ({ row }) => {
      const isAnnotated = DataItemStatusHelper.isAnnotated(row.original.itemStatus)
      return (
        <div className="min-w-[100px]">
          <div
            className={cn(
              `font-medium text-gray-900 flex items-center gap-1`,
              isAnnotated && 'text-green-700'
            )}
          >
            {row.original.itemName}
            {isAnnotated && (
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <span className="inline-flex items-center justify-center h-4 w-4 rounded-full bg-green-100">
                      <CheckCircle2 className="h-3 w-3 text-green-600" />
                    </span>
                  </TooltipTrigger>
                  <TooltipContent side="top">
                    <div className="text-xs">
                      {row.original.expressionType && (
                        <p>
                          情绪标注:{' '}
                          <span className="font-medium">{row.original.expressionType}</span>
                        </p>
                      )}
                      {row.original.tags && (
                        <p>
                          标签: <span className="font-medium">{row.original.tags}</span>
                        </p>
                      )}
                    </div>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            )}
          </div>
          <div className="text-sm text-gray-500 truncate">{row.original.itemId}</div>
        </div>
      )
    },
  },
  {
    accessorKey: 'type',
    header: '类型',
    cell: ({ row }) => (
      <div className="text-gray-700">
        <Badge
          variant="outline"
          className={cn(
            'font-normal gap-3',
            MediaTypeHelper.isImage(row.original.itemType)
              ? 'bg-purple-50 text-purple-700 border-purple-200 hover:bg-purple-100'
              : 'bg-amber-50 text-amber-700 border-amber-200 hover:bg-amber-100'
          )}
        >
          {MediaTypeHelper.isImage(row.original.itemType) ? (
            <ImageIcon className="h-4 w-4" />
          ) : (
            <VideoIcon className="h-4 w-4" />
          )}
          {MediaTypeHelper.getDescByString(row.original.itemType)}
        </Badge>
      </div>
    ),
  },
  {
    accessorKey: 'status',
    header: '状态',
    cell: ({ row }) => {
      const isAnnotated = DataItemStatusHelper.isAnnotated(row.original.itemStatus)

      return (
        <div className="flex items-center gap-1.5">
          <Badge
            variant={isAnnotated ? 'default' : 'outline'}
            className={cn(
              isAnnotated
                ? 'bg-green-50 text-green-700 border-green-200 hover:bg-green-100'
                : 'bg-gray-50 text-gray-700 border-gray-200 hover:bg-gray-100'
            )}
          >
            {isAnnotated && <CheckCircle2 className="h-3 w-3 mr-1" />}
            {DataItemStatusHelper.getDescByString(row.original.itemStatus)}
          </Badge>
        </div>
      )
    },
  },
  {
    accessorKey: 'expressionType',
    header: '情绪',
    cell: ({ row }) => {
      const isAnnotated = DataItemStatusHelper.isAnnotated(row.original.itemStatus)
      return (
        <div className="text-gray-600">
          {isAnnotated && EmotionHelper.getDescByString(row.original.expressionType)}
        </div>
      )
    },
  },
  {
    accessorKey: 'tags',
    header: '标签',
  },
  {
    accessorKey: 'createdAt',
    header: '创建时间',
    cell: ({ row }) => (
      <div className="text-gray-600">{format(row.original.createTime, 'yyyy-MM-dd HH:mm:ss')}</div>
    ),
  },
]
