import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog'
import { AlertTriangle, Info } from 'lucide-react'

interface AnnotationDataConfirmAlertProps {
  annotatedStats: {
    total: number
    annotated: number
    pending: number
    images: number
    videos: number
    annotatedImages: number
    annotatedVideos: number
  }
  setShowAnnotatedAlert: (open: boolean) => void
  showAnnotatedAlert: boolean
  proceedWithAnnotation: () => void
}

export function AnnotationDataConfirmAlert({
  annotatedStats,
  setShowAnnotatedAlert,
  showAnnotatedAlert,
  proceedWithAnnotation,
}: AnnotationDataConfirmAlertProps) {
  return (
    <AlertDialog open={showAnnotatedAlert} onOpenChange={setShowAnnotatedAlert}>
      <AlertDialogContent className="max-w-md">
        <AlertDialogHeader>
          <div className="flex items-center gap-2 text-amber-600">
            <AlertTriangle className="h-5 w-5" />
            <AlertDialogTitle>已包含标注数据</AlertDialogTitle>
          </div>
          <AlertDialogDescription className="pt-4">
            <div className="bg-amber-50 border border-amber-100 rounded-md p-3 mb-4">
              <div className="text-amber-800 font-medium mb-2 flex items-center gap-1.5">
                <Info className="h-4 w-4" />
                数据统计
              </div>
              <div className="space-y-1 text-sm text-amber-700">
                <p className="flex justify-between">
                  <span>总已标注数据:</span>
                  <span className="font-medium">{annotatedStats.total} 项</span>
                </p>
                {annotatedStats.images > 0 && (
                  <p className="flex justify-between">
                    <span>已标注图片:</span>
                    <span className="font-medium">{annotatedStats.annotatedImages} 张</span>
                  </p>
                )}
                {annotatedStats.videos > 0 && (
                  <p className="flex justify-between">
                    <span>已标注视频:</span>
                    <span className="font-medium">{annotatedStats.annotatedVideos} 个</span>
                  </p>
                )}
              </div>
            </div>

            <p className="text-gray-600">
              继续操作将
              <span className="text-amber-600 font-medium">覆盖这些数据的原有标注结果</span>
              ，您确定要继续吗？
            </p>
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter className="gap-2">
          <AlertDialogCancel className="border-amber-200 text-amber-700 hover:bg-amber-50 hover:text-amber-800">
            取消
          </AlertDialogCancel>
          <AlertDialogAction
            onClick={proceedWithAnnotation}
            className="bg-amber-600 hover:bg-amber-700 text-white"
          >
            继续标注
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  )
}
