import type { Filter } from '@/components/data-table/table-filter-section'

export const tableFilters: Filter[] = [
  {
    type: 'input',
    name: 'itemName',
    label: '文件名称',
    placeholder: '请输入文件名称',
  },
  {
    type: 'select',
    name: 'itemStatus',
    label: '状态',
    placeholder: '选择状态',
    options: [
      { value: 'ANNOTATED', label: '已标注' },
      { value: 'UNANNOTATED', label: '未标注' },
    ],
  },
  {
    type: 'select',
    name: 'itemType',
    label: '类型',
    placeholder: '选择类型',
    options: [
      { value: 'IMAGE', label: '图片' },
      { value: 'VIDEO', label: '视频' },
    ],
  },
  {
    type: 'select',
    name: 'tags',
    label: '标签',
    placeholder: '选择标签',
    fetchUrl: '/material-tag/list',
  },
]
