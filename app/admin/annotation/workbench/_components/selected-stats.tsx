import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip'
import { Badge } from '@/components/ui/badge'
import { CheckCircle2, Info } from 'lucide-react'

interface SelectedStatsProps {
  selectedStats: {
    total: number
    annotated: number
    pending: number
    images: number
    videos: number
  }
}

export default function SelectedStats({ selectedStats }: SelectedStatsProps) {
  const { total, annotated, pending, images, videos } = selectedStats
  return (
    <div className="px-4 flex flex-wrap items-center gap-6">
      <div className="flex items-center">
        <div className="w-2 h-2 rounded-full bg-blue-500 mr-2"></div>
        <span className="font-medium text-gray-800">已选择 {total} 项</span>
      </div>

      <div className="flex items-center gap-3">
        {annotated > 0 && (
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Badge className="bg-green-50 text-green-700 border-green-200 hover:bg-green-100 flex items-center gap-1">
                  <CheckCircle2 className="h-3 w-3" />
                  已标注 {annotated}
                </Badge>
              </TooltipTrigger>
              <TooltipContent>
                <p>这些数据已经标注过</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        )}

        {pending > 0 && (
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Badge
                  variant="outline"
                  className="bg-gray-50 text-gray-600 border-gray-200 hover:bg-gray-100 flex items-center gap-1"
                >
                  <Info className="h-3 w-3" />
                  未标注 {pending}
                </Badge>
              </TooltipTrigger>
              <TooltipContent>
                <p>这些数据尚未标注</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        )}
      </div>

      <div className="flex items-center gap-2">
        {images > 0 && (
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Badge
                  variant="secondary"
                  className="bg-purple-50 text-purple-700 border-purple-200 px-2.5"
                >
                  图片 {images}
                </Badge>
              </TooltipTrigger>
              <TooltipContent>
                <p>选择了 {images} 张图片</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        )}

        {videos > 0 && (
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Badge
                  variant="secondary"
                  className="bg-amber-50 text-amber-700 border-amber-200 px-2.5"
                >
                  视频 {videos}
                </Badge>
              </TooltipTrigger>
              <TooltipContent>
                <p>选择了 {videos} 个视频</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        )}
      </div>
    </div>
  )
}
