'use client'

import { Card, CardContent } from '@/components/ui/card'
import { ConfirmationDialog } from '@/components/ui/image-annotator/ConfirmationDialog'
import { ImagePreview } from '@/components/ui/image-annotator/ImagePreview'
import {
  emotionShortcuts,
  EmotionType,
  ShortcutHints,
} from '@/components/ui/image-annotator/ShortcutHints'
import { toast } from '@/components/ui/use-toast'
import { annotationService } from '@/services/annotationService'
import { DataItem } from '@/services/collectionService'
import { useRouter } from 'next/navigation'
import { useEffect, useState } from 'react'
import { CurrentAnnotationInfo } from './_components'

export default function ImageAnnotationPage() {
  const router = useRouter()
  const [currentIndex, setCurrentIndex] = useState(0)
  const [selectedEmotion, setSelectedEmotion] = useState<string | null>(null)
  const [isDialogOpen, setIsDialogOpen] = useState(false)
  const [images, setImages] = useState<string[]>([])
  const [annotationItems, setAnnotationItems] = useState<DataItem[]>([])
  const [annotations, setAnnotations] = useState<(string | null)[]>([])
  const [isLoading, setIsLoading] = useState(true)

  // 页面加载时从 sessionStorage 获取需要标注的数据
  useEffect(() => {
    try {
      const storedItems = sessionStorage.getItem('annotationItems')
      if (storedItems) {
        const items = JSON.parse(storedItems) as DataItem[]
        if (items.length > 0) {
          setAnnotationItems(items)
          // 提取图片预览或URL
          const imageUrls = items.map(
            item => `/me-detection/data-item/image/${item.itemId}?imageType=original`
          )
          setImages(imageUrls)
          // 初始化标注数据
          setAnnotations(Array(imageUrls.length).fill(null))
          setIsLoading(false)
        } else {
          toast({
            title: '数据错误',
            description: '没有找到需要标注的图片数据',
            variant: 'destructive',
          })
          // 跳回工作台
          router.push('/admin/annotation/workbench')
        }
      } else {
        toast({
          title: '数据错误',
          description: '没有找到需要标注的图片数据',
          variant: 'destructive',
        })
        // 跳回工作台
        router.push('/admin/annotation/workbench')
      }
    } catch (error) {
      console.error('获取标注数据失败:', error)
      toast({
        title: '数据错误',
        description: '获取标注数据时发生错误',
        variant: 'destructive',
      })
      // 跳回工作台
      router.push('/admin/annotation/workbench')
    }
  }, [router])

  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (isDialogOpen) return

      const key = e.key.toLowerCase()
      if (key in emotionShortcuts) {
        e.preventDefault()
        const emotion = emotionShortcuts[key as EmotionType]
        setSelectedEmotion(emotion)
        setIsDialogOpen(true)
      }
    }

    window.addEventListener('keydown', handleKeyDown)
    return () => window.removeEventListener('keydown', handleKeyDown)
  }, [isDialogOpen])

  const handleConfirm = () => {
    // 更新标注数据
    const newAnnotations = [...annotations]
    newAnnotations[currentIndex] = selectedEmotion
    setAnnotations(newAnnotations)

    console.log(`图片 ${currentIndex + 1} 标注为: ${selectedEmotion}`)
    setIsDialogOpen(false)
    setSelectedEmotion(null)

    // 自动前进到下一张图片
    if (currentIndex < images.length - 1) {
      setCurrentIndex(currentIndex + 1)
    }
  }

  const handleCancel = () => {
    setIsDialogOpen(false)
    setSelectedEmotion(null)
  }

  const handleSubmit = async () => {
    // 检查是否所有图片都已标注
    if (annotations.filter(Boolean).length < images.length) {
      toast({
        title: '未完成标注',
        description: '请先完成所有图片的标注',
        variant: 'destructive',
      })
      return
    }

    for (const item of annotationItems) {
      const index = annotationItems.indexOf(item)
      const emotion = annotations[index]
      if (emotion == null) {
        continue
      }
      await annotationService.submit({
        itemId: item.itemId,
        results: [
          {
            expressionType: emotion,
          },
        ],
      })
    }

    toast({
      title: '标注完成',
      description: '所有图片已成功标注',
    })
    router.push('/admin/annotation/workbench')
  }

  const handleBackToWorkbench = () => {
    router.push('/admin/annotation/workbench')
  }

  if (isLoading) {
    return (
      <div className="flex min-h-screen items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary mx-auto mb-4"></div>
          <p>加载标注数据中...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="flex flex-col">
      {/* 主要内容区域 */}
      <main className="container flex-1 py-6">
        <div className="grid gap-6 md:grid-cols-[1fr_600px]">
          {/* 图片预览区域 */}
          <Card className="overflow-hidden">
            <CardContent className="p-0">
              <div className="relative aspect-video md:aspect-auto md:h-[calc(100vh-12rem)]">
                <ImagePreview
                  images={images}
                  currentIndex={currentIndex}
                  onImageChange={setCurrentIndex}
                />
              </div>
            </CardContent>
          </Card>

          {/* 侧边栏信息 */}
          <div className="flex flex-col gap-4">
            <CurrentAnnotationInfo
              total={images.length}
              currentIndex={currentIndex}
              currentExpressionType={annotations[currentIndex]}
              finishedCount={annotations.filter(Boolean).length}
              handleSubmit={handleSubmit}
            />

            <Card>
              <CardContent className="p-4">
                <h2 className="mb-2 font-semibold">快捷键指南</h2>
                <div className="text-xs text-muted-foreground mb-2">按下对应按键选择情绪标签</div>
                <ShortcutHints
                  className="max-h-[40vh] overflow-y-auto"
                  selectedEmotion={isDialogOpen ? selectedEmotion : annotations[currentIndex]}
                />
              </CardContent>
            </Card>
          </div>
        </div>
      </main>

      <ConfirmationDialog
        open={isDialogOpen}
        selectedEmotion={selectedEmotion}
        onConfirm={handleConfirm}
        onCancel={handleCancel}
      />
    </div>
  )
}
