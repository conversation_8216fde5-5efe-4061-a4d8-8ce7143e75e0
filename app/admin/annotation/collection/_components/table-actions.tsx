'use client'

import { Button } from '@/components/ui/button'
import { useToast } from '@/components/ui/use-toast'
import { collectionService, DataItem } from '@/services/collectionService'
import { Download, Trash2, Upload } from 'lucide-react'
import { useRef, useState } from 'react'
import { showDeleteConfirmDialog } from '@/lib/utils/confirmDialog'

interface TableActionsProps {
  selectedItems: DataItem[]
  onUpload: () => void
  refreshTable: () => void
}

export function TableActions({ selectedItems, onUpload, refreshTable }: TableActionsProps) {
  const { toast } = useToast()
  const [isExporting, setIsExporting] = useState(false)
  const [isUploading, setIsUploading] = useState(false)
  const fileInputRef = useRef<HTMLInputElement>(null)

  const handleExport = async () => {
    if (selectedItems.length === 0) {
      toast({
        title: '提示',
        description: '请先选择要导出的数据项',
        variant: 'destructive',
      })
      return
    }

    setIsExporting(true)
    try {
      await collectionService.exportDataItems(selectedItems.map(item => item.itemId))
      toast({
        title: '导出成功',
        description: `已成功导出 ${selectedItems.length} 个数据项`,
      })
    } catch (error) {
      console.error('导出失败:', error)
      toast({
        title: '导出失败',
        description: '导出过程中发生错误，请重试',
        variant: 'destructive',
      })
    } finally {
      setIsExporting(false)
    }
  }

  const handleDelete = async () => {
    if (selectedItems.length === 0) {
      toast({
        title: '提示',
        description: '请先选择要删除的数据项',
        variant: 'destructive',
      })
      return
    }

    // 显示删除确认对话框
    const confirmed = await showDeleteConfirmDialog('数据项', selectedItems.length)

    if (!confirmed) {
      return
    }

    try {
      // 执行删除操作
      await collectionService.deleteDataItems(selectedItems.map(item => item.itemId))

      toast({
        title: '删除成功',
        description: `已成功删除 ${selectedItems.length} 个数据项`,
      })

      // 调用父组件的刷新方法
      refreshTable()
    } catch (error) {
      console.error('删除失败:', error)
      toast({
        title: '删除失败',
        description: '删除过程中发生错误，请重试',
        variant: 'destructive',
      })
    }
  }

  const handleUploadClick = () => {
    fileInputRef.current?.click()
  }

  const handleFileChange = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files
    if (!files || files.length === 0) return

    setIsUploading(true)
    try {
      // 这里调用上传服务
      await collectionService.uploadFiles({ files: Array.from(files) })

      toast({
        title: '上传成功',
        description: `已成功上传 ${files.length} 个文件`,
      })

      // 调用父组件的刷新方法
      refreshTable()
    } catch (error) {
      console.error('上传失败:', error)
      toast({
        title: '上传失败',
        description: '上传过程中发生错误,' + error,
        variant: 'destructive',
      })
    } finally {
      // 清空文件输入
      if (fileInputRef.current) {
        fileInputRef.current.value = ''
      }
      setIsUploading(false)
    }
  }

  return (
    <div className="flex items-center gap-2">
      <Button
        variant="outline"
        size="sm"
        onClick={handleExport}
        disabled={selectedItems.length === 0 || isExporting}
        className="h-8"
      >
        <Download className="h-4 w-4 mr-2" />
        {isExporting ? '导出中...' : `导出 (${selectedItems.length})`}
      </Button>
      <Button
        variant="outline"
        size="sm"
        onClick={handleDelete}
        disabled={selectedItems.length === 0}
        className="h-8"
      >
        <Trash2 className="h-4 w-4 mr-2" />
        删除 ({selectedItems.length})
      </Button>
      <Button
        size="sm"
        className="h-8 bg-blue-600 hover:bg-blue-700"
        onClick={handleUploadClick}
        disabled={isUploading}
      >
        <Upload className="h-4 w-4 mr-2" />
        {isUploading ? '上传中...' : '上传数据'}
      </Button>

      {/* 隐藏的文件输入元素 */}
      <input
        ref={fileInputRef}
        type="file"
        multiple
        accept=".jpg,.jpeg,.png,.gif,.mp4,.avi,.mov,.zip,.rar"
        onChange={handleFileChange}
        className="hidden"
      />
    </div>
  )
}
