import { cn } from '@/lib/utils'
import { EmotionHelper } from '@/types/utils/constants'

export default function DataEmotionCell({ emotion }: { emotion: string }) {
  return (
    <span
      className={cn(
        'inline-flex items-center justify-center px-2.5 py-0.5 rounded-full text-xs font-medium border whitespace-nowrap',
        EmotionHelper.getColorByString(emotion)
      )}
    >
      {EmotionHelper.getDescByString(emotion)}
    </span>
  )
}
