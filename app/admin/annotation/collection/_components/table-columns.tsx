'use client'

import ImagePreview from '@/components/ImagePreview'
import { TagCombobox } from '@/components/tag-combobox'
import { toast } from '@/components/ui/use-toast'
import { useTags } from '@/hooks/use-tags'
import { collectionService, DataItem } from '@/services/collectionService'
import { materialTagService } from '@/services/materialTagService'
import { DataItemSourceHelper, MediaTypeHelper } from '@/types'
import { ColumnDef } from '@tanstack/react-table'
import { format } from 'date-fns'
import { useState } from 'react'
import DataEmotionCell from './data-emotion-cell'
import DataStatusCell from './data-status-cell'

const LabelTagsCell = ({ row }: { row: any }) => {
  const { tags, loading, addTag } = useTags()
  const [selectedTag, setSelectedTag] = useState<string>(row.original.tags || '')

  const handleTagChange = async (newTag: string) => {
    try {
      await collectionService.updateTag(row.original.itemId, newTag)
      setSelectedTag(newTag)
    } catch (e: any) {
      toast({
        variant: 'destructive',
        description: e.message(),
        title: '更新标签失败',
      })
    }
  }

  const handleCreateNewTag = async (newTagName: string) => {
    // 这里可以调用API创建新标签
    await materialTagService.addTag(newTagName)
    // 添加到本地标签列表
    const newTag = { value: newTagName, label: newTagName }
    addTag(newTag)
  }

  return (
    <TagCombobox
      value={selectedTag}
      onValueChange={handleTagChange}
      placeholder="选择标签..."
      options={tags}
      loading={loading}
      allowCreate={true}
      onCreateNew={handleCreateNewTag}
      createText="新增标签"
      emptyText="未找到匹配的标签"
      className="min-w-[200px] max-w-[300px]"
    />
  )
}

// 列定义
export const tableColumns: ColumnDef<DataItem, any>[] = [
  {
    accessorKey: 'name',
    header: () => <div className="min-w-[100px]">文件名称</div>,
    cell: ({ row }: { row: any }) => (
      <div className="min-w-[100px]">
        <div className="font-medium truncate">{row.original.itemName}</div>
        <div className="text-sm text-muted-foreground truncate">{row.original.itemId}</div>
      </div>
    ),
  },
  {
    accessorKey: 'preview',
    header: () => <div className="text-center">预览</div>,
    cell: ({ row }) => <ImagePreview itemId={row.original.itemId} alt={row.original.itemName} />,
  },
  {
    accessorKey: 'type',
    header: () => <div className="min-w-[30px]">类型</div>,
    cell: ({ row }) => MediaTypeHelper.getDescByString(row.original.itemType),
  },
  {
    accessorKey: 'source',
    header: () => <div className="min-w-[30px]">来源</div>,
    cell: ({ row }) => DataItemSourceHelper.getDescByString(row.original.itemSource),
  },
  {
    accessorKey: 'emotion',
    header: () => <div className="text-center">表情</div>,
    cell: ({ row }) => <DataEmotionCell emotion={row.original.expressionType} />,
  },
  {
    accessorKey: 'tags',
    header: '标签',
    cell: ({ row }) => <LabelTagsCell row={row} />,
  },
  {
    accessorKey: 'status',
    header: () => <div className="text-center">状态</div>,
    cell: ({ row }) => <DataStatusCell status={row.original.itemStatus} />,
  },
  {
    accessorKey: 'createdAt',
    header: () => <div className="min-w-[140px]">创建时间</div>,
    cell: ({ row }) => format(row.original.createTime, 'yyyy-MM-dd HH:mm:ss'),
  },
]
