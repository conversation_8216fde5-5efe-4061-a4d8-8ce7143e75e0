import { cn } from '@/lib/utils'
import { DataItemStatusHelper } from '@/types/utils/constants'

export default function DataStatusCell({ status }: { status: string }) {
  return (
    <span
      className={cn(
        'inline-flex items-center justify-center px-2.5 py-0.5 rounded-full text-xs font-medium border whitespace-nowrap',
        DataItemStatusHelper.getColorByString(status)
      )}
    >
      {DataItemStatusHelper.getDescByString(status)}
    </span>
  )
}
