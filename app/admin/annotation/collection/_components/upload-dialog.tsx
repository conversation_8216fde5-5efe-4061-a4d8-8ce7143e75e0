'use client'

import { useRef, useState } from 'react'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON>alogHeader, DialogTitle } from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Progress } from '@/components/ui/progress'
import { Loader2 } from 'lucide-react'
import { toast } from '@/components/ui/use-toast'
import { collectionService } from '@/services/collectionService'

type UploadFormData = {
  files: File[]
}

interface UploadDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
}

export function UploadDialog({ open, onOpenChange }: UploadDialogProps) {
  const [uploading, setUploading] = useState(false)
  const [uploadProgress, setUploadProgress] = useState(0)

  const fileInputRef = useRef<HTMLInputElement>(null)

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setUploading(true)

    try {
      const files = fileInputRef.current?.files
      if (!files || files.length === 0) {
        toast({
          title: '文件错误',
          description: '请上传文件',
          variant: 'destructive',
        })
        return
      }

      const data: UploadFormData = { files: Array.from(files) }

      // 模拟上传进度
      const progressInterval = setInterval(() => {
        setUploadProgress(prev => {
          if (prev >= 90) {
            clearInterval(progressInterval)
            return prev
          }
          return prev + 10
        })
      }, 200)

      await collectionService.uploadFiles(data)
      setUploadProgress(100)
      toast({
        title: '上传成功',
        description: '文件已成功上传',
      })
      onOpenChange(false)
    } catch (error) {
      toast({
        title: '上传失败',
        description: error instanceof Error ? error.message : '文件上传失败',
        variant: 'destructive',
      })
    } finally {
      setUploading(false)
      // 重置表单
      setUploadProgress(0)
      if (fileInputRef.current) {
        fileInputRef.current.value = ''
      }
    }
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <DialogTitle>上传文件</DialogTitle>
        </DialogHeader>
        <form onSubmit={handleSubmit} className="space-y-6">
          <div className="grid gap-4">
            <div className="flex flex-col space-y-1.5">
              <Label htmlFor="file">选择文件</Label>
              <Input
                id="file"
                ref={fileInputRef}
                type="file"
                multiple
                accept="image/*,video/*,application/zip"
              />
            </div>

            {uploading && (
              <div className="space-y-2">
                <Progress value={uploadProgress} className="w-full" />
                <p className="text-sm text-gray-500 text-center">{uploadProgress}% 已上传</p>
              </div>
            )}
          </div>

          <Button type="submit" disabled={uploading} className="w-full">
            {uploading ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                上传中...
              </>
            ) : (
              '开始上传'
            )}
          </Button>
        </form>
      </DialogContent>
    </Dialog>
  )
}
