import type { Filter } from '@/components/data-table/table-filter-section'
import {
  DataItemSource,
  DataItemSourceHelper,
  DataItemStatus,
  DataItemStatusHelper,
  MediaType,
  MediaTypeHelper,
} from '@/types'

export const tableFilters: Filter[] = [
  {
    type: 'input',
    name: 'itemName',
    label: '文件名称',
    placeholder: '支持模糊搜索',
  },
  {
    type: 'select',
    name: 'itemType',
    label: '类型',
    placeholder: '选择类型',
    options: Object.values(MediaType).map(media => ({
      value: media,
      label: MediaTypeHelper.getDescByString(media),
    })),
  },
  {
    type: 'select',
    name: 'itemSource',
    label: '来源',
    placeholder: '选择来源',
    options: Object.values(DataItemSource).map(source => ({
      value: source,
      label: DataItemSourceHelper.getDescByString(source),
    })),
  },
  {
    type: 'select',
    name: 'itemStatus',
    label: '状态',
    placeholder: '选择状态',
    options: Object.values(DataItemStatus).map(status => ({
      value: status,
      label: DataItemStatusHelper.getDescByString(status),
    })),
  },
  {
    type: 'select',
    name: 'tags',
    label: '标签',
    placeholder: '选择标签',
    fetchUrl: '/material-tag/list',
  },
]
