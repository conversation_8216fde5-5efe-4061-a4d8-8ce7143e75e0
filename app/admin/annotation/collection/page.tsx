'use client'

import { DataTable } from '@/components/data-table'
import { DataItem } from '@/services/collectionService'
import { useRef, useState } from 'react'
import { TableActions, tableColumns, tableFilters, UploadDialog } from './_components'
import { DataTableRef } from '@/components/data-table'

export default function DataCollectionPage() {
  const [selectedItems, setSelectedItems] = useState<DataItem[]>([])
  const [uploadDialogOpen, setUploadDialogOpen] = useState(false)
  const tableRef = useRef<DataTableRef<DataItem>>(null)
  const handleSelectionChange = (items: DataItem[]) => {
    setSelectedItems(items)
  }

  const handleUpload = () => {
    setUploadDialogOpen(true)
  }

  const refreshTable = () => {
    tableRef.current?.handleSearch()
  }

  const tableActions = (
    <TableActions
      selectedItems={selectedItems}
      onUpload={handleUpload}
      refreshTable={refreshTable}
    />
  )

  return (
    <>
      <DataTable<DataItem>
        ref={tableRef}
        url="/data-item/list"
        columns={tableColumns}
        filters={tableFilters}
        actions={tableActions}
        pageSizeOptions={[10, 20, 50, 100, 500, 1000]}
        onSelectionChange={handleSelectionChange}
      />

      <UploadDialog open={uploadDialogOpen} onOpenChange={setUploadDialogOpen} />
    </>
  )
}
