'use client'

import { useEffect, useState } from 'react'
import { useRouter } from 'next/navigation'
import { useAppContext } from '@/contexts/AppContext'
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Separator } from "@/components/ui/separator"
import { BarChart, Users, Video, History, Camera, Image as ImageIcon } from 'lucide-react'
import { toast } from "@/components/ui/use-toast"

type DetectionMode = 'image' | 'video' | 'stream'

export default function WorkbenchPage() {
  const { isLoggedIn } = useAppContext()
  const router = useRouter()
  const [detectionName, setDetectionName] = useState('')
  const [detectionDesc, setDetectionDesc] = useState('')

  useEffect(() => {
    if (!isLoggedIn) {
      router.push('/login')
    }
  }, [isLoggedIn, router])

  if (!isLoggedIn) {
    return null
  }

  const handleModeSelect = (mode: DetectionMode) => {
    if (!detectionName.trim()) {
      toast({
        title: "请输入检测名称",
        description: "在选择检测方式之前，请为本次检测命名",
        variant: "destructive"
      })
      return
    }

    // 保存检测基本信息到localStorage
    const detectionInfo = {
      name: detectionName,
      description: detectionDesc,
      createdAt: new Date().toISOString()
    }
    localStorage.setItem('pending_detection_info', JSON.stringify(detectionInfo))

    // 跳转到对应的检测页面
    switch (mode) {
      case 'image':
        router.push('/detection/image')
        break
      case 'video':
        router.push('/detection/video')
        break
      case 'stream':
        router.push('/detection/stream')
        break
    }
  }

  return (
    <div className="container mx-auto py-6 space-y-8">
      <div className="space-y-2">
        <h2 className="text-2xl font-bold tracking-tight">工作台</h2>
        <p className="text-muted-foreground">
          在这里开始新的情绪识别检测任务
        </p>
      </div>

      <div className="grid gap-4">
        <Card>
          <CardHeader>
            <CardTitle>基本信息</CardTitle>
            <CardDescription>
              为本次检测任务设置基本信息
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="name">检测名称</Label>
              <Input
                id="name"
                placeholder="为本次检测命名"
                value={detectionName}
                onChange={(e) => setDetectionName(e.target.value)}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="description">检测描述</Label>
              <Textarea
                id="description"
                placeholder="添加对本次检测的描述信息"
                value={detectionDesc}
                onChange={(e) => setDetectionDesc(e.target.value)}
              />
            </div>
          </CardContent>
        </Card>

        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
          <Card className="cursor-pointer hover:bg-gray-50 transition-colors" onClick={() => handleModeSelect('image')}>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <ImageIcon className="h-5 w-5" />
                <span>图片检测</span>
              </CardTitle>
              <CardDescription>
                上传图片文件进行情绪识别分析
              </CardDescription>
            </CardHeader>
          </Card>

          <Card className="cursor-pointer hover:bg-gray-50 transition-colors" onClick={() => handleModeSelect('video')}>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Video className="h-5 w-5" />
                <span>视频检测</span>
              </CardTitle>
              <CardDescription>
                上传视频文件进行情绪识别分析
              </CardDescription>
            </CardHeader>
          </Card>

          <Card className="cursor-pointer hover:bg-gray-50 transition-colors" onClick={() => handleModeSelect('stream')}>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Camera className="h-5 w-5" />
                <span>实时检测</span>
              </CardTitle>
              <CardDescription>
                使用摄像头进行实时情绪识别分析
              </CardDescription>
            </CardHeader>
          </Card>
        </div>
      </div>
    </div>
  )
}
