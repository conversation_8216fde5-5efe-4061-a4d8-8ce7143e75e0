'use client'

import { useState } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { ArrowLeft, Bell, BellRing, BellOff, Mail, MessageSquare } from 'lucide-react'
import Link from 'next/link'
import { Switch } from "@/components/ui/switch"
import { Label } from "@/components/ui/label"
import { Separator } from "@/components/ui/separator"
import { toast } from "@/components/ui/use-toast"

interface NotificationSetting {
  id: string
  name: string
  description: string
  type: 'browser' | 'email' | 'message'
  icon: any
  enabled: boolean
}

export default function NotificationsPage() {
  const [settings, setSettings] = useState<NotificationSetting[]>([
    {
      id: 'browser',
      name: '浏览器通知',
      description: '在浏览器中显示桌面通知',
      type: 'browser',
      icon: BellRing,
      enabled: true
    },
    {
      id: 'email',
      name: '邮件通知',
      description: '通过邮件发送重要通知',
      type: 'email',
      icon: Mail,
      enabled: false
    },
    {
      id: 'message',
      name: '站内消息',
      description: '在系统内显示消息提醒',
      type: 'message',
      icon: MessageSquare,
      enabled: true
    }
  ])

  const [masterSwitch, setMasterSwitch] = useState(true)

  const handleToggleMaster = (enabled: boolean) => {
    setMasterSwitch(enabled)
    if (!enabled) {
      toast({
        title: "已关闭所有通知",
        description: "您可以随时重新开启通知功能"
      })
    } else {
      toast({
        title: "已开启通知功能",
        description: "您现在可以接收到系统通知"
      })
    }
  }

  const handleToggleSetting = (settingId: string) => {
    setSettings(prev => prev.map(setting =>
      setting.id === settingId
        ? { ...setting, enabled: !setting.enabled }
        : setting
    ))
  }

  return (
    <div className="container mx-auto py-6 max-w-3xl">
      <div className="flex items-center gap-4 mb-6">
        <div className="flex-1">
          <h1 className="text-2xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
            通知设置
          </h1>
          <p className="text-sm text-gray-500 mt-1">
            管理系统通知和提醒的接收方式
          </p>
        </div>
      </div>

      <div className="space-y-6">
        {/* 总开关 */}
        <Card>
          <CardHeader className="pb-3">
            <div className="flex items-center justify-between">
              <CardTitle className="flex items-center gap-3 text-lg">
                <div className="w-10 h-10 rounded-lg bg-blue-50 flex items-center justify-center">
                  {masterSwitch ? (
                    <Bell className="h-5 w-5 text-blue-600" />
                  ) : (
                    <BellOff className="h-5 w-5 text-gray-400" />
                  )}
                </div>
                通知总开关
              </CardTitle>
              <Switch
                checked={masterSwitch}
                onCheckedChange={handleToggleMaster}
              />
            </div>
          </CardHeader>
          <CardContent>
            <p className="text-gray-600">
              控制是否接收任何形式的系统通知。关闭后将不会收到任何通知。
            </p>
          </CardContent>
        </Card>

        {/* 通知方式设置 */}
        <Card className={!masterSwitch ? 'opacity-60' : ''}>
          <CardHeader className="pb-3">
            <CardTitle className="text-lg">通知方式</CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            {settings.map((setting, index) => (
              <div key={setting.id}>
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <div className="w-10 h-10 rounded-lg bg-gray-50 flex items-center justify-center">
                      <setting.icon className={`h-5 w-5 ${
                        masterSwitch && setting.enabled ? 'text-blue-600' : 'text-gray-400'
                      }`} />
                    </div>
                    <div>
                      <Label className="text-base">{setting.name}</Label>
                      <p className="text-sm text-gray-500">{setting.description}</p>
                    </div>
                  </div>
                  <Switch
                    checked={setting.enabled}
                    onCheckedChange={() => handleToggleSetting(setting.id)}
                    disabled={!masterSwitch}
                  />
                </div>
                {index < settings.length - 1 && (
                  <Separator className="my-6" />
                )}
              </div>
            ))}
          </CardContent>
        </Card>

        {/* 通知时段设置 */}
        <Card className={!masterSwitch ? 'opacity-60' : ''}>
          <CardHeader className="pb-3">
            <CardTitle className="text-lg">通知时段</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-gray-500 text-sm">
              该功能正在开发中，未来您可以设置在特定时段接收或静音通知。
            </p>
          </CardContent>
        </Card>
      </div>
    </div>
  )
} 