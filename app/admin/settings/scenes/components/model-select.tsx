import { useState } from "react"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectGroup, SelectItem, SelectTrigger, SelectValue, SelectLabel } from "@/components/ui/select"

interface Model {
  id: string
  name: string
  description: string
  category: string
  tags: string[]
}

interface ModelSelectProps {
  label: string
  value: string
  onValueChange: (value: string) => void
  models: Model[]
}

export function ModelSelect({
  label,
  value,
  onValueChange,
  models
}: ModelSelectProps) {
  const [searchQuery, setSearchQuery] = useState('')
  
  // 按类别分组模型
  const groupedModels = models.reduce((acc, model) => {
    const category = model.category
    if (!acc[category]) {
      acc[category] = []
    }
    acc[category].push(model)
    return acc
  }, {} as Record<string, Model[]>)

  // 搜索和过滤模型
  const filteredGroups = Object.entries(groupedModels).reduce((acc, [category, models]) => {
    const filteredModels = models.filter(model => 
      model.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      model.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
      model.tags.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase()))
    )
    if (filteredModels.length > 0) {
      acc[category] = filteredModels
    }
    return acc
  }, {} as Record<string, Model[]>)

  const selectedModel = models.find(m => m.id === value)

  return (
    <div className="space-y-2">
      <Label className="text-sm text-gray-600">{label}</Label>
      <Select value={value} onValueChange={onValueChange}>
        <SelectTrigger className="w-full">
          <SelectValue>
            {selectedModel && (
              <div className="flex items-center gap-2">
                <span>{selectedModel.name}</span>
                <div className="flex gap-1">
                  {selectedModel.tags.map(tag => (
                    <span key={tag} className="px-1.5 py-0.5 bg-blue-50 text-blue-600 text-xs rounded-full">
                      {tag}
                    </span>
                  ))}
                </div>
              </div>
            )}
          </SelectValue>
        </SelectTrigger>
        <SelectContent>
          <div className="p-2">
            <Input
              placeholder="搜索模型..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="mb-2"
            />
          </div>
          {Object.entries(filteredGroups).map(([category, models]) => (
            <SelectGroup key={category}>
              <SelectLabel className="px-2 py-1.5 text-sm font-medium text-gray-500">
                {category}
              </SelectLabel>
              {models.map(model => (
                <SelectItem 
                  key={model.id} 
                  value={model.id}
                  className="py-3"
                >
                  <div className="space-y-1">
                    <div className="font-medium">{model.name}</div>
                    <div className="text-sm text-gray-500">{model.description}</div>
                    <div className="flex gap-1">
                      {model.tags.map(tag => (
                        <span key={tag} className="px-1.5 py-0.5 bg-blue-50 text-blue-600 text-xs rounded-full">
                          {tag}
                        </span>
                      ))}
                    </div>
                  </div>
                </SelectItem>
              ))}
            </SelectGroup>
          ))}
        </SelectContent>
      </Select>
    </div>
  )
} 