'use client'

import { useState } from 'react'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ooter } from "@/components/ui/dialog"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Scene } from '@/app/config/scenes'
import { MODEL_TYPES } from '@/app/config/models'

interface SceneDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  onConfirm: (scene: Scene) => void
  mode: 'add' | 'edit'
  initialData?: Scene | null
}

export function SceneDialog({
  open,
  onOpenChange,
  onConfirm,
  mode,
  initialData
}: SceneDialogProps) {
  const [formData, setFormData] = useState<Scene>(
    initialData || {
      id: '',
      name: '',
      description: '',
      models: {
        image: {
          type: '',
          version: ''
        },
        video: {
          type: '',
          version: ''
        },
        stream: {
          type: '',
          version: ''
        }
      }
    }
  )

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    onConfirm(formData)
  }

  const handleModelTypeChange = (type: 'image' | 'video' | 'stream', value: string) => {
    setFormData(prev => ({
      ...prev,
      models: {
        ...prev.models,
        [type]: {
          type: value,
          version: '' // 重置版本
        }
      }
    }))
  }

  const handleModelVersionChange = (type: 'image' | 'video' | 'stream', value: string) => {
    setFormData(prev => ({
      ...prev,
      models: {
        ...prev.models,
        [type]: {
          ...prev.models[type],
          version: value
        }
      }
    }))
  }

  const getModelVersions = (type: 'image' | 'video' | 'stream') => {
    const modelType = formData.models[type].type
    return MODEL_TYPES.find(m => m.id === modelType)?.versions || []
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[550px] max-h-[90vh] p-0">
        <DialogHeader className="px-6 pt-6 pb-2">
          <DialogTitle>{mode === 'add' ? '添加场景' : '编辑场景'}</DialogTitle>
        </DialogHeader>
        
        <form onSubmit={handleSubmit} className="flex flex-col h-full">
          <ScrollArea className="px-6 flex-1 max-h-[calc(90vh-130px)]">
            <div className="space-y-6 py-2">
              <div className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="id">场景ID</Label>
                  <Input
                    id="id"
                    value={formData.id}
                    onChange={(e) => setFormData(prev => ({ ...prev, id: e.target.value }))}
                    placeholder="请输入场景ID"
                    required
                    disabled={mode === 'edit'}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="name">场景名称</Label>
                  <Input
                    id="name"
                    value={formData.name}
                    onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                    placeholder="请输入场景名称"
                    required
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="description">场景描述</Label>
                  <Textarea
                    id="description"
                    value={formData.description}
                    onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                    placeholder="请输入场景描述"
                    required
                  />
                </div>
              </div>

              <div className="space-y-4">
                <h3 className="text-lg font-medium">模型配置</h3>
                {(['image', 'video', 'stream'] as const).map((type) => (
                  <div key={type} className="space-y-4 p-4 border rounded-lg">
                    <h4 className="font-medium capitalize">{type} 模型</h4>
                    <div className="grid grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label>模型类型</Label>
                        <Select
                          value={formData.models[type].type}
                          onValueChange={(value) => handleModelTypeChange(type, value)}
                        >
                          <SelectTrigger>
                            <SelectValue placeholder="选择模型类型" />
                          </SelectTrigger>
                          <SelectContent>
                            {MODEL_TYPES
                              .filter(m => m.id.endsWith(`_${type}`))
                              .map(model => (
                                <SelectItem key={model.id} value={model.id}>
                                  {model.name}
                                </SelectItem>
                              ))}
                          </SelectContent>
                        </Select>
                      </div>
                      <div className="space-y-2">
                        <Label>模型版本</Label>
                        <Select
                          value={formData.models[type].version}
                          onValueChange={(value) => handleModelVersionChange(type, value)}
                          disabled={!formData.models[type].type}
                        >
                          <SelectTrigger>
                            <SelectValue placeholder="选择模型版本" />
                          </SelectTrigger>
                          <SelectContent>
                            {getModelVersions(type).map(version => (
                              <SelectItem key={version.id} value={version.version}>
                                {version.name}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>
                    </div>
                    {formData.models[type].type && (
                      <div className="mt-2 text-sm text-gray-500">
                        {MODEL_TYPES.find(m => m.id === formData.models[type].type)?.description}
                      </div>
                    )}
                  </div>
                ))}
              </div>
            </div>
          </ScrollArea>
          
          <DialogFooter className="px-6 py-4 border-t">
            <Button
              type="button"
              variant="outline"
              onClick={() => onOpenChange(false)}
            >
              取消
            </Button>
            <Button type="submit">
              {mode === 'add' ? '添加' : '保存'}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  )
} 