'use client'

import { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Plus, Pencil, Trash2 } from "lucide-react"
import { toast } from "@/components/ui/use-toast"
import { SceneDialog } from "./components/scene-dialog"
import { DETECTION_SCENES } from '@/app/config/scenes'
import { Switch } from "@/components/ui/switch"
import { MODEL_TYPES } from '@/app/config/models'
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog"

interface Scene {
  id: string
  name: string
  description: string
  models: {
    image: {
      type: string
      version: string
    }
    video: {
      type: string
      version: string
    }
    stream: {
      type: string
      version: string
    }
  }
  isActive?: boolean
}

export default function ScenesPage() {
  const [scenes, setScenes] = useState<Scene[]>(
    DETECTION_SCENES.map(scene => ({ ...scene, isActive: true }))
  )
  const [dialogOpen, setDialogOpen] = useState(false)
  const [editingScene, setEditingScene] = useState<Scene | null>(null)
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false)
  const [deletingScene, setDeletingScene] = useState<Scene | null>(null)

  const handleAddScene = (sceneData: Scene) => {
    // 检查ID是否已存在
    if (scenes.some(scene => scene.id === sceneData.id)) {
      toast({
        title: "添加失败",
        description: "场景ID已存在",
        variant: "destructive"
      })
      return
    }

    // 添加新场景
    setScenes([...scenes, { ...sceneData, isActive: true }])
    setDialogOpen(false)
    toast({
      title: "添加成功",
      description: "新场景已添加"
    })
  }

  const handleEditScene = (scene: Scene) => {
    setEditingScene(scene)
    setDialogOpen(true)
  }

  const handleSaveEdit = (sceneData: Scene) => {
    setScenes(prev => prev.map(scene =>
      scene.id === sceneData.id ? { ...sceneData, isActive: scene.isActive } : scene
    ))
    setDialogOpen(false)
    setEditingScene(null)
    toast({
      title: "保存成功",
      description: "场景信息已更新"
    })
  }

  const handleDeleteClick = (scene: Scene) => {
    setDeletingScene(scene)
    setDeleteDialogOpen(true)
  }

  const handleConfirmDelete = () => {
    if (deletingScene) {
      setScenes(prev => prev.filter(scene => scene.id !== deletingScene.id))
      setDeleteDialogOpen(false)
      setDeletingScene(null)
      toast({
        title: "删除成功",
        description: "场景已删除"
      })
    }
  }

  const handleToggleActive = (sceneId: string) => {
    setScenes(prev => prev.map(scene =>
      scene.id === sceneId ? { ...scene, isActive: !scene.isActive } : scene
    ))
  }

  const getModelInfo = (type: string, version: string) => {
    const modelType = MODEL_TYPES.find(m => m.id === type)
    if (!modelType) return null
    return modelType.versions.find(v => v.version === version)
  }

  return (
    <div className="container mx-auto py-6 max-w-4xl">
      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-2xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">场景管理</h1>
          <p className="text-muted-foreground">管理检测场景和对应的模型配置</p>
        </div>
        <Button onClick={() => {
          setEditingScene(null)
          setDialogOpen(true)
        }}>
          <Plus className="mr-2 h-4 w-4" />
          添加场景
        </Button>
      </div>

      <div className="grid gap-4">
        {scenes.map((scene) => (
          <Card key={scene.id} className={!scene.isActive ? 'opacity-60' : ''}>
            <CardHeader className="pb-3">
              <div className="flex items-start justify-between">
                <div className="space-y-1">
                  <CardTitle className="text-xl flex items-center gap-2">
                    {scene.name}
                    {!scene.isActive && (
                      <span className="text-xs font-normal text-gray-500 border border-gray-200 rounded px-2 py-0.5">
                        已禁用
                      </span>
                    )}
                  </CardTitle>
                  <div className="text-sm text-gray-500">
                    场景ID: {scene.id}
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  <Switch
                    checked={scene.isActive}
                    onCheckedChange={() => handleToggleActive(scene.id)}
                  />
                  <Button
                    variant="ghost"
                    size="icon"
                    onClick={() => handleEditScene(scene)}
                  >
                    <Pencil className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="ghost"
                    size="icon"
                    onClick={() => handleDeleteClick(scene)}
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>
              </div>
              <CardDescription className="mt-2">{scene.description}</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid gap-4">
                {(['image', 'video', 'stream'] as const).map((type) => {
                  const modelInfo = getModelInfo(scene.models[type].type, scene.models[type].version)
                  return (
                    <div key={type} className="space-y-2">
                      <div className="text-sm font-medium capitalize">{type} 模型</div>
                      <div className="grid grid-cols-2 gap-4 text-sm">
                        <div>
                          <span className="text-gray-500">类型：</span>
                          <span className="font-medium">
                            {MODEL_TYPES.find(m => m.id === scene.models[type].type)?.name || scene.models[type].type}
                          </span>
                        </div>
                        <div>
                          <span className="text-gray-500">版本：</span>
                          <span className="font-medium">
                            {modelInfo?.name || scene.models[type].version}
                          </span>
                        </div>
                      </div>
                      {modelInfo && (
                        <div className="text-sm text-gray-500">
                          {modelInfo.description}
                        </div>
                      )}
                    </div>
                  )
                })}
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      <SceneDialog
        open={dialogOpen}
        onOpenChange={setDialogOpen}
        onConfirm={editingScene ? handleSaveEdit : handleAddScene}
        mode={editingScene ? 'edit' : 'add'}
        initialData={editingScene}
      />

      <AlertDialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>确认删除场景</AlertDialogTitle>
            <AlertDialogDescription>
              您确定要删除场景 "{deletingScene?.name}" 吗？此操作无法撤销。
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>取消</AlertDialogCancel>
            <AlertDialogAction
              className="bg-red-600 hover:bg-red-700"
              onClick={handleConfirmDelete}
            >
              删除
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  )
} 