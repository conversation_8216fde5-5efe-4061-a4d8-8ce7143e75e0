import { format } from 'date-fns'
import { ColumnDef } from '@tanstack/react-table'
import { ActionContentCell, UserLog } from './action-content-cell'
import { actionTypeMap } from './constants'

export const tableColumns: ColumnDef<UserLog>[] = [
  {
    accessorKey: 'username',
    header: '用户名',
  },
  {
    accessorKey: 'actionType',
    header: '操作行为',
    cell: ({ row }) => actionTypeMap[row.original.actionType as keyof typeof actionTypeMap],
  },
  {
    accessorKey: 'actionContent',
    header: '操作内容',
    cell: ({ row }) => <ActionContentCell row={row} />,
  },
  {
    accessorKey: 'createdAt',
    header: '发生时间',
    cell: ({ row }) => format(new Date(row.original.createdAt), 'yyyy-MM-dd HH:mm:ss'),
  },
]