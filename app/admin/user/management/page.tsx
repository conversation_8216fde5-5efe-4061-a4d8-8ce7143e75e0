'use client'

import { useState } from 'react'
import { DataTable } from '@/components/data-table'
import {
  User,
  createTableColumns,
  tableFilters,
  UserLogDrawer,
  CreateUserDialog,
} from './_components'


export default function UserManagementPage() {
  const [refreshKey, setRefreshKey] = useState(0)
  const [selectedUser, setSelectedUser] = useState<User | null>(null)
  const [showLogDrawer, setShowLogDrawer] = useState(false)

  const handleSuccess = () => {
    setRefreshKey(prev => prev + 1)
  }

  const handleViewLogs = (user: User) => {
    setSelectedUser(user)
    setShowLogDrawer(true)
  }

  const columns = createTableColumns(handleSuccess, handleViewLogs)

  return (
    <>
      <DataTable
        key={refreshKey}
        url="/api/users"
        columns={columns}
        filters={tableFilters}
        pageSize={10}
        pageSizeOptions={[10, 20, 50, 100]}
        actions={<CreateUserDialog onSuccess={handleSuccess} />}
      />
      
      {selectedUser && (
        <UserLogDrawer 
          userId={selectedUser.id} 
          username={selectedUser.name}
          open={showLogDrawer} 
          onOpenChange={setShowLogDrawer} 
        />
      )}
    </>
  )
}
