'use client'

import { useState, useEffect } from 'react'
import { format } from 'date-fns'
import { zhCN } from 'date-fns/locale'
import { History, X } from 'lucide-react'
import { ColumnDef } from '@tanstack/react-table'
import { DataTable } from '@/components/data-table'
import type { Filter } from '@/components/data-table/table-filter-section'
import { RightDrawer } from './right-drawer'
import { ActionContentCell } from './action-content-cell'

interface UserLog {
  id: string
  userId: string
  username: string
  actionType: 'EMOTION_DETECT' | 'TRAIN_MODEL' | 'UPLOAD_DATASET' | 'DOWNLOAD_DATASET' | 'DOWNLOAD_MODEL'
  actionContent: {
    videoPath?: string
    videoResult?: string
    modelId?: string
    modelName?: string
    datasetId?: string
    datasetName?: string
    iterations?: number
    accuracy?: string
    datasetSize?: string
    sampleCount?: number
    purpose?: string
  }
  createdAt: string
}

interface UserLogDrawerProps {
  userId: string
  username: string
  open: boolean
  onOpenChange: (open: boolean) => void
}

const actionTypeMap = {
  EMOTION_DETECT: '表情检测',
  TRAIN_MODEL: '训练模型',
  UPLOAD_DATASET: '上传数据集',
  DOWNLOAD_DATASET: '下载数据集',
  DOWNLOAD_MODEL: '下载模型',
}

export function UserLogDrawer({ userId, username, open, onOpenChange }: UserLogDrawerProps) {
  const [logs, setLogs] = useState<UserLog[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [refreshKey, setRefreshKey] = useState(0);

  // 模拟获取用户日志数据
  const mockFetchUserLogs = async (userId: string): Promise<UserLog[]> => {
    // 在实际应用中，这里会通过API获取特定用户的日志
    await new Promise(resolve => setTimeout(resolve, 500));
    
    // 使用用户ID作为随机种子，确保每个用户的日志数据保持一致
    const seedRandom = (min: number, max: number, seed: string) => {
      // 简单的字符串哈希函数
      const hash = Array.from(seed).reduce((acc, char) => char.charCodeAt(0) + ((acc << 5) - acc), 0);
      const rand = Math.sin(hash) * 10000;
      return Math.floor((rand - Math.floor(rand)) * (max - min + 1)) + min;
    };
    
    // 基于用户ID确定该用户的行为模式
    const userPattern = {
      // 基于用户ID确定该用户偏好的操作类型
      favoriteAction: ['EMOTION_DETECT', 'TRAIN_MODEL', 'UPLOAD_DATASET', 'DOWNLOAD_DATASET', 'DOWNLOAD_MODEL'][
        seedRandom(0, 4, userId)
      ] as keyof typeof actionTypeMap,
      // 用户活跃度 (决定日志数量)
      activityLevel: seedRandom(10, 30, userId + 'activity'),
      // 日志时间范围 (过去多少天内)
      timeRange: seedRandom(7, 60, userId + 'time'),
    };
    
    // 生成模拟日志数据
    const logTypes = Object.keys(actionTypeMap) as Array<keyof typeof actionTypeMap>;
    const logs: UserLog[] = [];
    
    const logCount = userPattern.activityLevel;
    
    for (let i = 0; i < logCount; i++) {
      // 根据用户模式，有更高概率选择用户偏好的操作类型
      const isFavoriteAction = seedRandom(0, 100, userId + i.toString()) < 70; // 70%概率使用偏好操作
      
      let actionType: keyof typeof actionTypeMap;
      if (isFavoriteAction) {
        actionType = userPattern.favoriteAction;
      } else {
        // 随机选择一个非偏好操作
        const filteredTypes = logTypes.filter(type => type !== userPattern.favoriteAction);
        actionType = filteredTypes[seedRandom(0, filteredTypes.length - 1, userId + i.toString() + 'type')];
      }
      
      // 生成日期 - 越近的日期出现概率越高
      const daysAgo = Math.floor(Math.pow(seedRandom(0, 100, userId + i.toString() + 'date') / 100, 2) * userPattern.timeRange);
      const date = new Date();
      date.setDate(date.getDate() - daysAgo);
      date.setHours(seedRandom(8, 23, userId + i.toString() + 'hour'));
      date.setMinutes(seedRandom(0, 59, userId + i.toString() + 'min'));
      date.setSeconds(seedRandom(0, 59, userId + i.toString() + 'sec'));
      
      const actionContent: any = {};
      
      switch (actionType) {
        case 'EMOTION_DETECT':
          // 视频检测的模拟数据
          const videoId = seedRandom(1, 100, userId + i.toString() + 'video');
          actionContent.videoPath = `/uploads/users/${userId.substring(0, 8)}/videos/sample-${videoId}.mp4`;
          
          // 根据用户ID确定检测结果分布
          const emotionBias = seedRandom(0, 100, userId) / 100; // 0-1之间的值，越高越积极
          const isPositive = seedRandom(0, 100, userId + i.toString() + 'result') / 100 < emotionBias;
          
          const emotions = isPositive 
            ? ['积极情绪', '高兴', '惊喜', '满足', '放松'] 
            : ['消极情绪', '悲伤', '愤怒', '恐惧', '厌恶'];
          
          actionContent.videoResult = emotions[seedRandom(0, emotions.length - 1, userId + i.toString() + 'emotion')];
          break;
          
        case 'TRAIN_MODEL':
          // 模型训练的模拟数据
          actionContent.modelId = `MOD-${seedRandom(10000, 99999, userId + i.toString() + 'model')}`;
          
          // 版本号格式 - 主版本与用户相关，次版本随机
          const majorVersion = seedRandom(1, 3, userId);
          const minorVersion = seedRandom(0, 9, userId + i.toString() + 'version');
          actionContent.modelName = `情绪识别模型 v${majorVersion}.${minorVersion}`;
          
          // 添加训练相关信息
          actionContent.iterations = seedRandom(1000, 5000, userId + i.toString() + 'iter');
          actionContent.accuracy = (85 + seedRandom(0, 1499, userId + i.toString() + 'acc') / 100).toFixed(2) + '%';
          break;
          
        case 'UPLOAD_DATASET':
        case 'DOWNLOAD_DATASET':
          // 数据集相关的模拟数据
          actionContent.datasetId = `DATA-${seedRandom(10000, 99999, userId + i.toString() + 'dataset')}`;
          
          // 数据集名称 - 基于用户和索引生成
          const datasetTypes = ['面部表情', '人群情绪', '视频情绪', '声音情绪', '多模态情绪'];
          const datasetType = datasetTypes[seedRandom(0, datasetTypes.length - 1, userId + i.toString() + 'dtype')];
          const datasetIndex = seedRandom(1, 50, userId + i.toString() + 'dindex');
          
          actionContent.datasetName = `${datasetType}数据集 #${datasetIndex}`;
          
          // 添加数据集大小信息
          const sizeInMB = seedRandom(50, 2000, userId + i.toString() + 'size');
          actionContent.datasetSize = sizeInMB > 1000 ? `${(sizeInMB/1000).toFixed(2)}GB` : `${sizeInMB}MB`;
          actionContent.sampleCount = seedRandom(1000, 50000, userId + i.toString() + 'samples');
          break;
          
        case 'DOWNLOAD_MODEL':
          // 模型下载的模拟数据
          actionContent.modelId = `MOD-${seedRandom(10000, 99999, userId + i.toString() + 'model')}`;
          
          // 版本号格式
          const dlMajorVersion = seedRandom(1, 3, userId);
          const dlMinorVersion = seedRandom(0, 9, userId + i.toString() + 'version');
          actionContent.modelName = `情绪识别模型 v${dlMajorVersion}.${dlMinorVersion}`;
          
          // 添加下载目的
          const purposes = ['测试部署', '本地验证', '生产环境', '二次开发', '模型评估'];
          actionContent.purpose = purposes[seedRandom(0, purposes.length - 1, userId + i.toString() + 'purpose')];
          break;
      }
      
      logs.push({
        id: `LOG-${seedRandom(10000, 99999, userId + i.toString() + 'id')}`,
        userId,
        username,
        actionType,
        actionContent,
        createdAt: date.toISOString(),
      });
    }
    
    // 按日期排序
    return logs.sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());
  }

  // 当抽屉打开或用户ID变化时，获取日志数据
  useEffect(() => {
    if (open) {
      setIsLoading(true);
      mockFetchUserLogs(userId).then(data => {
        setLogs(data);
        setIsLoading(false);
      });
    }
  }, [open, userId]);

  // 定义用户日志表格的列
  const columns: ColumnDef<UserLog>[] = [
    {
      accessorKey: 'actionType',
      header: '操作行为',
      cell: ({ row }) => actionTypeMap[row.original.actionType as keyof typeof actionTypeMap],
    },
    {
      accessorKey: 'actionContent',
      header: '操作内容',
      cell: ({ row }) => <ActionContentCell row={row} />,
    },
    {
      accessorKey: 'createdAt',
      header: '发生时间',
      cell: ({ row }) => format(new Date(row.original.createdAt), 'yyyy-MM-dd HH:mm:ss'),
    },
  ]

  // 定义过滤器
  const filters: Filter[] = [
    {
      type: 'dateRange',
      name: 'dateRange',
      label: '操作时间',
      placeholder: '选择日期范围',
      locale: zhCN,
    },
  ]

  // 创建一个用于模拟DataTable的URL请求处理器
  const mockApiHandler = (url: string): Promise<any> => {
    return new Promise((resolve) => {
      setTimeout(() => {
        // 解析URL查询参数
        const params = new URLSearchParams(url.split('?')[1]);
        const page = parseInt(params.get('page') || '1');
        const pageSize = parseInt(params.get('pageSize') || '10');
        const actionType = params.get('actionType');
        const dateRange = params.get('dateRange');
        
        // 根据过滤条件筛选日志
        let filteredLogs = [...logs];
        
        if (actionType) {
          filteredLogs = filteredLogs.filter(log => log.actionType === actionType);
        }
        
        if (dateRange) {
          try {
            const range = JSON.parse(dateRange);
            if (range.from && range.to) {
              const fromDate = new Date(range.from);
              const toDate = new Date(range.to);
              toDate.setHours(23, 59, 59, 999); // 设置到当天结束
              
              filteredLogs = filteredLogs.filter(log => {
                const logDate = new Date(log.createdAt);
                return logDate >= fromDate && logDate <= toDate;
              });
            }
          } catch (e) {
            console.error('Error parsing date range:', e);
          }
        }
        
        // 计算分页
        const total = filteredLogs.length;
        const startIndex = (page - 1) * pageSize;
        const endIndex = Math.min(startIndex + pageSize, total);
        const paginatedLogs = filteredLogs.slice(startIndex, endIndex);
        
        resolve({
          rows: paginatedLogs,
          total,
          page,
          pageSize
        });
      }, 300);
    });
  };

  // 重写fetch方法以使用模拟数据
  const originalFetch = window.fetch;
  useEffect(() => {
    if (open) {
      window.fetch = (input: RequestInfo | URL, init?: RequestInit) => {
        const url = typeof input === 'string' ? input : input.toString();
        
        if (url.startsWith('/api/user-logs')) {
          return mockApiHandler(url) as Promise<Response>;
        }
        
        return originalFetch(input, init);
      };
    }
    
    return () => {
      window.fetch = originalFetch;
    };
  }, [open, logs]);

  return (
    <RightDrawer open={open} onOpenChange={onOpenChange}>
      <div className="absolute left-0 top-0 h-1.5 w-full bg-gradient-to-r from-blue-500 to-blue-600" />
      <div className="relative h-full flex flex-col">
        <div className="px-6 pt-4 pb-4 relative border-b border-gray-200 bg-gray-50/80 backdrop-blur-sm flex items-center justify-between">
          <div>
            <h2 className="text-lg font-semibold leading-none tracking-tight text-gray-900 flex items-center">
              <History className="mr-2 h-4 w-4 text-blue-600" />
              用户操作记录 - {username}
            </h2>
            <p className="text-xs text-gray-600 mt-1.5">查看该用户的所有操作记录和行为历史</p>
          </div>
          
          <button
            className="rounded-full p-2 bg-gray-200/80 text-gray-700 hover:bg-gray-300 hover:text-gray-900 transition-colors flex items-center justify-center"
            onClick={() => onOpenChange(false)}
            aria-label="关闭"
          >
            <X className="h-4 w-4" />
          </button>
        </div>

        <div className="flex-1 overflow-auto bg-white">
          {isLoading ? (
            <div className="py-8 text-center">
              <div className="inline-block h-8 w-8 animate-spin rounded-full border-4 border-solid border-blue-500 border-r-transparent"></div>
              <p className="mt-2 text-gray-600">加载中...</p>
            </div>
          ) : (
            <DataTable
              key={refreshKey}
              url="/api/user/log"
              columns={columns}
              filters={filters}
              pageSize={10}
              pageSizeOptions={[10, 20, 50]}
            />
          )}
        </div>
      </div>
    </RightDrawer>
  )
}