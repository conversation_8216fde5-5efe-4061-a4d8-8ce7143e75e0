'use client'

import { useState } from 'react'
import { History, UserCog, KeyRound, Trash2 } from 'lucide-react'
import { useToast } from '@/components/ui/use-toast'
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog'

interface User {
  id: string
  name: string
  role: string
}

interface UserActionsCellProps {
  user: User
  onViewLogs: (user: User) => void
  onSuccess: () => void
}

export function UserActionsCell({ user, onViewLogs, onSuccess }: UserActionsCellProps) {
  const [showDeleteDialog, setShowDeleteDialog] = useState(false)
  const [showRoleDialog, setShowRoleDialog] = useState(false)
  const { toast } = useToast()

  const handleRoleChange = async () => {
    try {
      const newRole = user.role === 'admin' ? 'user' : 'admin'
      const response = await fetch(`/api/users`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          userId: user.id,
          role: newRole,
        }),
      })

      if (!response.ok) {
        throw new Error('更新失败')
      }

      toast({
        description: '角色更新成功',
      })
      setShowRoleDialog(false)
      onSuccess()
    } catch (error) {
      toast({
        variant: 'destructive',
        description: '角色更新失败',
      })
    }
  }

  const handleResetPassword = async () => {
    try {
      const response = await fetch(`/api/users`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          userId: user.id,
        }),
      })

      if (!response.ok) {
        throw new Error('重置失败')
      }

      toast({
        description: '密码重置成功',
      })
    } catch (error) {
      toast({
        variant: 'destructive',
        description: '密码重置失败',
      })
    }
  }

  const handleDelete = async () => {
    try {
      const response = await fetch(`/api/users?userId=${user.id}`, {
        method: 'DELETE',
      })

      if (!response.ok) {
        const data = await response.json()
        throw new Error(data.error || '删除失败')
      }

      toast({
        description: '用户删除成功',
      })
      setShowDeleteDialog(false)
      onSuccess()
    } catch (error) {
      toast({
        variant: 'destructive',
        description: error instanceof Error ? error.message : '用户删除失败',
      })
    }
  }

  const handleViewLogs = () => {
    onViewLogs(user)
  }

  return (
    <>
      <div className="flex items-center justify-center gap-4">
        <button
          onClick={handleViewLogs}
          className="text-blue-500 hover:text-blue-700 flex items-center text-sm"
        >
          <History className="h-3.5 w-3.5 mr-1" />
          操作记录
        </button>
        <button
          onClick={() => setShowRoleDialog(true)}
          className="text-blue-500 hover:text-blue-700 flex items-center text-sm"
        >
          <UserCog className="h-3.5 w-3.5 mr-1" />
          修改角色
        </button>
        <button
          onClick={handleResetPassword}
          className="text-blue-500 hover:text-blue-700 flex items-center text-sm"
        >
          <KeyRound className="h-3.5 w-3.5 mr-1" />
          重置密码
        </button>
        {user.role !== 'admin' && (
          <button
            onClick={() => setShowDeleteDialog(true)}
            className="text-red-500 hover:text-red-700 flex items-center text-sm"
          >
            <Trash2 className="h-3.5 w-3.5 mr-1" />
            删除
          </button>
        )}
      </div>

      {/* 角色修改确认对话框 */}
      <AlertDialog open={showRoleDialog} onOpenChange={setShowRoleDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>确认修改角色</AlertDialogTitle>
            <AlertDialogDescription>
              您确定要将用户 "{user.name}" 的角色从
              {user.role === 'admin' ? ' 管理员 ' : ' 普通用户 '}
              修改为
              {user.role === 'admin' ? ' 普通用户 ' : ' 管理员 '}
              吗？
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>取消</AlertDialogCancel>
            <AlertDialogAction onClick={handleRoleChange} className="bg-blue-500 hover:bg-blue-600">
              确认修改
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* 删除确认对话框 */}
      <AlertDialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>确认删除用户</AlertDialogTitle>
            <AlertDialogDescription>
              您确定要删除用户 "{user.name}" 吗？此操作不可撤销。
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>取消</AlertDialogCancel>
            <AlertDialogAction onClick={handleDelete} className="bg-red-500 hover:bg-red-600">
              删除
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  )
}