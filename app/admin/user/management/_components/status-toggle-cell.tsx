'use client'

import { useState } from 'react'
import { Switch } from '@/components/ui/switch'
import { Label } from '@/components/ui/label'
import { useToast } from '@/components/ui/use-toast'

interface User {
  id: string
  status: string
}

interface StatusToggleCellProps {
  user: User
  onSuccess: () => void
}

export function StatusToggleCell({ user, onSuccess }: StatusToggleCellProps) {
  const [isLoading, setIsLoading] = useState(false)
  const [currentStatus, setCurrentStatus] = useState(user.status)
  const isActive = currentStatus === 'active'
  const { toast } = useToast()

  const handleToggle = async () => {
    try {
      setIsLoading(true)
      const newStatus = isActive ? 'inactive' : 'active'

      const response = await fetch(`/api/users`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          userId: user.id,
          status: newStatus,
        }),
      })

      if (!response.ok) {
        const data = await response.json()
        throw new Error(data.error || '状态更新失败')
      }

      setCurrentStatus(newStatus)
      toast({
        title: '状态更新成功',
        description: `用户状态已更新为${newStatus === 'active' ? '启用' : '禁用'}`,
      })
      onSuccess()
    } catch (error) {
      console.error('Error toggling status:', error)
      toast({
        variant: 'destructive',
        title: '状态更新失败',
        description: error instanceof Error ? error.message : '请稍后重试',
      })
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className="flex items-center justify-center">
      <div className="flex items-center space-x-2">
        <Switch
          checked={isActive}
          onCheckedChange={handleToggle}
          disabled={isLoading}
          className={`${isActive ? '!bg-green-500 hover:!bg-green-600' : ''}`}
        />
        <Label className={isActive ? 'text-green-500' : 'text-gray-500'}>
          {isActive ? '启用' : '禁用'}
        </Label>
      </div>
    </div>
  )
}