interface ActionContent {
  videoPath?: string
  videoResult?: string
  modelId?: string
  modelName?: string
  datasetId?: string
  datasetName?: string
  iterations?: number
  accuracy?: string
  datasetSize?: string
  sampleCount?: number
  purpose?: string
}

interface UserLog {
  actionType: 'EMOTION_DETECT' | 'TRAIN_MODEL' | 'UPLOAD_DATASET' | 'DOWNLOAD_DATASET' | 'DOWNLOAD_MODEL'
  actionContent: ActionContent
}

export function ActionContentCell({ row }: { row: { original: UserLog } }) {
  const log = row.original
  
  switch (log.actionType) {
    case 'EMOTION_DETECT':
      return (
        <div className="space-y-1">
          <div className="text-sm">视频路径: {log.actionContent.videoPath}</div>
          <div className="text-sm">检测结果: {log.actionContent.videoResult}</div>
        </div>
      )
    case 'TRAIN_MODEL':
      return (
        <div className="space-y-1">
          <div className="text-sm">模型ID: {log.actionContent.modelId}</div>
          <div className="text-sm">模型名称: {log.actionContent.modelName}</div>
        </div>
      )
    case 'UPLOAD_DATASET':
    case 'DOWNLOAD_DATASET':
      return (
        <div className="space-y-1">
          <div className="text-sm">数据集ID: {log.actionContent.datasetId}</div>
          <div className="text-sm">数据集名称: {log.actionContent.datasetName}</div>
        </div>
      )
    case 'DOWNLOAD_MODEL':
      return (
        <div className="space-y-1">
          <div className="text-sm">模型ID: {log.actionContent.modelId}</div>
          <div className="text-sm">模型名称: {log.actionContent.modelName}</div>
        </div>
      )
    default:
      return null
  }
}