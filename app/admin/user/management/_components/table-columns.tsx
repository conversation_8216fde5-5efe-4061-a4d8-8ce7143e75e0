'use client'

import { ColumnDef } from '@tanstack/react-table'
import { StatusToggleCell } from './status-toggle-cell'
import { UserActionsCell } from './user-actions-cell'

export interface User {
  id: string
  username: string
  name: string
  email: string
  role: string
  status: string
  createdAt: string
  expiredAt: string
}

export const createTableColumns = (
  onSuccess: () => void,
  onViewLogs: (user: User) => void
): ColumnDef<User>[] => [
  {
    accessorKey: 'username',
    header: ({ column }) => <div className="text-center font-bold">用户名</div>,
    cell: ({ row }) => <div className="text-center">{row.getValue('username')}</div>,
  },
  {
    accessorKey: 'name',
    header: ({ column }) => <div className="text-center font-bold">姓名</div>,
    cell: ({ row }) => <div className="text-center">{row.getValue('name')}</div>,
  },
  {
    accessorKey: 'email',
    header: ({ column }) => <div className="text-center font-bold">邮箱</div>,
    cell: ({ row }) => <div className="text-center">{row.getValue('email')}</div>,
  },
  {
    accessorKey: 'role',
    header: ({ column }) => <div className="text-center font-bold">角色</div>,
    cell: ({ row }) => {
      const role = row.getValue('role') as string
      return <div className="text-center">{role === 'admin' ? '管理员' : '普通用户'}</div>
    },
  },
  {
    accessorKey: 'status',
    header: ({ column }) => <div className="text-center font-bold">状态</div>,
    cell: ({ row }) => (
      <StatusToggleCell 
        user={row.original} 
        onSuccess={onSuccess} 
      />
    ),
  },
  {
    accessorKey: 'createdAt',
    header: ({ column }) => <div className="text-center font-bold">创建时间</div>,
    cell: ({ row }) => <div className="text-center">{row.getValue('createdAt')}</div>,
  },
  {
    accessorKey: 'expiredAt',
    header: ({ column }) => <div className="text-center font-bold">过期时间</div>,
    cell: ({ row }) => <div className="text-center">{row.getValue('expiredAt')}</div>,
  },
  {
    id: 'actions',
    header: ({ column }) => <div className="text-center font-bold">操作</div>,
    cell: ({ row }) => (
      <UserActionsCell 
        user={row.original} 
        onViewLogs={onViewLogs}
        onSuccess={onSuccess}
      />
    ),
  },
]