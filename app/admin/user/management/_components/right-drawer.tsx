'use client'

import { cn } from '@/lib/utils'

export interface RightDrawerProps {
  children: React.ReactNode
  open: boolean
  onOpenChange: (open: boolean) => void
}

export function RightDrawer({ children, open, onOpenChange }: RightDrawerProps) {
  return (
    <>
      {open && (
        <div 
          className="fixed inset-0 z-50 bg-black/60 backdrop-blur-sm transition-opacity duration-300" 
          onClick={() => onOpenChange(false)} 
        />
      )}
      <div
        className={cn(
          'fixed inset-y-0 right-0 z-50 w-[90vw] max-w-[1200px] bg-background shadow-xl transform transition-all duration-300 ease-in-out rounded-l-2xl border-l border-gray-100',
          open ? 'translate-x-0' : 'translate-x-full'
        )}
      >
        {children}
      </div>
    </>
  )
}