import type { Filter } from '@/components/data-table/table-filter-section'

export const tableFilters: Filter[] = [
  {
    type: 'input',
    name: 'username',
    label: '用户名',
    placeholder: '请输入用户名',
  },
  {
    type: 'input',
    name: 'name',
    label: '姓名',
    placeholder: '请输入姓名',
  },
  {
    type: 'select',
    name: 'role',
    label: '角色',
    placeholder: '请选择角色',
    options: [
      { value: 'admin', label: '管理员' },
      { value: 'user', label: '普通用户' },
    ],
  },
  {
    type: 'select',
    name: 'status',
    label: '状态',
    placeholder: '请选择状态',
    options: [
      { value: 'active', label: '启用' },
      { value: 'inactive', label: '禁用' },
    ],
  },
]