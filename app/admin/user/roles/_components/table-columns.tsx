'use client'

import { ColumnDef } from '@tanstack/react-table'
import { Shield } from 'lucide-react'
import { Badge } from '@/components/ui/badge'
import { Role, Permission } from '@/lib/mock/roleData'
import { RoleActionsCell } from './role-actions-cell'

export const createRoleColumns = (onReload: () => void): ColumnDef<Role>[] => [
  {
    id: 'index',
    header: ({ column }) => <div className="text-center font-bold">序号</div>,
    cell: ({ row, table }) => {
      const pageSize = table.getState().pagination.pageSize || 10
      const pageIndex = table.getState().pagination.pageIndex || 0
      return <div className="text-center">{pageIndex * pageSize + row.index + 1}</div>
    },
  },
  {
    accessorKey: 'name',
    header: ({ column }) => <div className="text-center font-bold">角色名称</div>,
    cell: ({ row }) => (
      <div className="text-center font-medium">
        <div className="flex items-center justify-center gap-2">
          <Shield className="h-4 w-4 text-blue-500" />
          {row.getValue('name')}
        </div>
      </div>
    ),
  },
  {
    accessorKey: 'code',
    header: ({ column }) => <div className="text-center font-bold">角色标识</div>,
    cell: ({ row }) => (
      <div className="text-center">
        <Badge variant="outline">{row.getValue('code')}</Badge>
      </div>
    ),
  },
  {
    accessorKey: 'description',
    header: ({ column }) => <div className="text-center font-bold">描述</div>,
    cell: ({ row }) => <div className="text-center">{row.getValue('description')}</div>,
  },
  {
    accessorKey: 'permissions',
    header: ({ column }) => <div className="text-center font-bold">权限数量</div>,
    cell: ({ row }) => {
      const permissions = row.getValue('permissions') as Permission[]
      return (
        <div className="text-center">
          <Badge variant="secondary">{permissions.length}</Badge>
        </div>
      )
    },
  },
  {
    accessorKey: 'userCount',
    header: ({ column }) => <div className="text-center font-bold">用户数量</div>,
    cell: ({ row }) => (
      <div className="text-center">
        <Badge>{row.getValue('userCount')}</Badge>
      </div>
    ),
  },
  {
    accessorKey: 'createdAt',
    header: ({ column }) => <div className="text-center font-bold">创建时间</div>,
    cell: ({ row }) => <div className="text-center">{row.getValue('createdAt')}</div>,
  },
  {
    id: 'actions',
    header: ({ column }) => <div className="text-center font-bold">操作</div>,
    cell: ({ row }) => (
      <RoleActionsCell 
        role={row.original} 
        onReload={onReload}
      />
    ),
  },
]