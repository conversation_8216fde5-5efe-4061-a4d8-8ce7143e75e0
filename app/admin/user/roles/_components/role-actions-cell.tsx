'use client'

import { useState } from 'react'
import { Settings2, Trash2 } from 'lucide-react'
import { useToast } from '@/components/ui/use-toast'
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog'
import { PermissionDialog } from '@/components/role/permission-dialog'
import { Role } from '@/lib/mock/roleData'

interface RoleActionsCellProps {
  role: Role
  onReload: () => void
}

export function RoleActionsCell({ role, onReload }: RoleActionsCellProps) {
  const [showDeleteDialog, setShowDeleteDialog] = useState(false)
  const [showPermissionDialog, setShowPermissionDialog] = useState(false)
  const { toast } = useToast()

  const handleConfigPermissions = () => {
    setShowPermissionDialog(true)
  }

  const handleDelete = async () => {
    try {
      const response = await fetch(`/api/roles/${role.id}`, {
        method: 'DELETE',
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.error || '删除失败')
      }

      toast({
        description: '角色删除成功',
      })
      onReload()
    } catch (error: any) {
      toast({
        variant: 'destructive',
        description: error.message || '角色删除失败',
      })
    }
  }

  return (
    <>
      <div className="flex items-center justify-center gap-4">
        <button
          onClick={handleConfigPermissions}
          className="text-blue-500 hover:text-blue-700 flex items-center text-sm"
        >
          <Settings2 className="h-3.5 w-3.5 mr-1" />
          配置权限
        </button>
        <button
          onClick={() => setShowDeleteDialog(true)}
          className="text-red-500 hover:text-red-700 flex items-center text-sm"
          disabled={role.code === 'admin'}
        >
          <Trash2 className="h-3.5 w-3.5 mr-1" />
          删除
        </button>
      </div>

      <AlertDialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>确认删除角色</AlertDialogTitle>
            <AlertDialogDescription>
              您确定要删除角色 "{role.name}" 吗？此操作不可撤销。
              {role.userCount > 0 && (
                <div className="mt-2 text-red-500">
                  注意：当前有 {role.userCount}{' '}
                  个用户关联此角色，删除后这些用户将失去相关权限。
                </div>
              )}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>取消</AlertDialogCancel>
            <AlertDialogAction onClick={handleDelete} className="bg-red-500 hover:bg-red-600">
              删除
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      <PermissionDialog
        open={showPermissionDialog}
        onOpenChange={setShowPermissionDialog}
        role={role}
        onSuccess={onReload}
      />
    </>
  )
}