'use client'

import { useState } from 'react'
import { PlusCircle } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { DataTable } from '@/components/data-table'
import { RoleDialog } from '@/components/role/role-dialog'
import { createRoleColumns, tableFilters } from './_components'


export default function RoleManagementPage() {
  const [showCreateDialog, setShowCreateDialog] = useState(false)

  const handleReload = () => {
    window.location.reload()
  }

  const columns = createRoleColumns(handleReload)

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-xl font-bold">角色管理</h1>
          <p className="text-sm text-muted-foreground mt-1">管理系统角色及其对应的权限配置</p>
        </div>
      </div>

      <DataTable
        url="/api/roles"
        columns={columns}
        filters={tableFilters}
        pageSize={10}
        pageSizeOptions={[10, 20, 50]}
        actions={
          <Button
            onClick={() => setShowCreateDialog(true)}
            className="bg-blue-600 hover:bg-blue-700"
          >
            <PlusCircle className="mr-2 h-4 w-4" />
            新建角色
          </Button>
        }
      />

      <RoleDialog
        open={showCreateDialog}
        onOpenChange={setShowCreateDialog}
        onSuccess={() => window.location.reload()}
      />
    </div>
  )
}
