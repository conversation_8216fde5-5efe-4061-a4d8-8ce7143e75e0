'use client'

import '../globals.css'
import { LayoutProvider } from '@/components/layout-context'
import Navigation from '@/components/Navigation'
import AppSidebar from '@/components/Sidebar'

export default function AdminLayout({ children }: { children: React.ReactNode }) {
  return (
    <LayoutProvider>
      <div className="h-screen flex flex-col">
        <Navigation />
        <div className="flex-1 flex min-h-0">
          {/* 左侧侧边栏 - 独立滚动 */}
          <aside className="flex-shrink-0 border-r border-gray-200 dark:border-gray-800 flex flex-col">
            <AppSidebar />
          </aside>
          {/* 右侧主内容区域 - 独立滚动 */}
          <main className="flex-1 bg-slate-50 dark:bg-slate-900 flex flex-col min-w-0">
            <div className="flex-1 p-3 overflow-y-auto">{children}</div>
          </main>
        </div>
      </div>
    </LayoutProvider>
  )
}
