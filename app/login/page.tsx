'use client'

import LoginForm from '@/components/LoginForm'
import { EmotionIcon } from '@/components/icons'
import { Card, CardContent } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'

export default function LoginPage() {
  return (
    <div className="relative min-h-screen flex items-center justify-center bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100 dark:from-slate-900 dark:via-slate-800 dark:to-slate-900 overflow-hidden">
      {/* 背景装饰元素 */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute -top-40 -right-40 w-80 h-80 bg-gradient-to-br from-blue-400/15 to-indigo-500/15 rounded-full blur-3xl animate-pulse"></div>
        <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-gradient-to-tr from-indigo-600/15 to-blue-500/15 rounded-full blur-3xl animate-pulse delay-1000"></div>
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-96 h-96 bg-gradient-to-r from-blue-400/10 to-indigo-500/10 rounded-full blur-3xl animate-pulse delay-500"></div>
      </div>

      <div className="relative z-10 w-full max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="grid lg:grid-cols-2 gap-8 items-center">
          {/* 左侧品牌区域 */}
          <div className="hidden lg:block space-y-8 animate-in slide-in-from-left-full duration-700">
            <div className="space-y-6">
              <div className="flex items-center space-x-3">
                <div className="relative">
                  <div className="absolute inset-0 bg-gradient-to-r from-blue-600 to-indigo-600 rounded-xl blur-lg opacity-75"></div>
                  <div className="relative bg-gradient-to-r from-blue-600 to-indigo-600 p-3 rounded-xl">
                    <EmotionIcon className="h-8 w-8 text-white" />
                  </div>
                </div>
                <div>
                  <h1 className="text-3xl font-bold bg-gradient-to-r from-slate-900 to-blue-700 dark:from-white dark:to-blue-300 bg-clip-text text-transparent">
                    情感识别分析平台
                  </h1>
                  <p className="text-slate-600 dark:text-slate-400 text-sm">
                    Emotion Recognition Platform
                  </p>
                </div>
              </div>

              <div className="space-y-4">
                <h2 className="text-2xl font-semibold text-slate-900 dark:text-white">
                  专业的情感分析解决方案
                </h2>
                <p className="text-lg text-slate-600 dark:text-slate-300 leading-relaxed">
                  基于先进的人工智能技术，为企事业单位提供专业、可靠的情感识别分析服务，助力提升工作效率和服务质量。
                </p>
              </div>

              <div className="flex flex-wrap gap-2">
                <Badge
                  variant="secondary"
                  className="bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300"
                >
                  专业可靠
                </Badge>
                <Badge
                  variant="secondary"
                  className="bg-indigo-100 text-indigo-800 dark:bg-indigo-900/30 dark:text-indigo-300"
                >
                  技术先进
                </Badge>
                <Badge
                  variant="secondary"
                  className="bg-slate-100 text-slate-800 dark:bg-slate-900/30 dark:text-slate-300"
                >
                  安全稳定
                </Badge>
              </div>
            </div>

            <div className="space-y-4">
              <div className="flex items-center space-x-3 text-slate-600 dark:text-slate-400">
                <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                <span className="text-sm">多维度情感分析</span>
              </div>
              <div className="flex items-center space-x-3 text-slate-600 dark:text-slate-400">
                <div className="w-2 h-2 bg-indigo-500 rounded-full"></div>
                <span className="text-sm">实时数据处理</span>
              </div>
              <div className="flex items-center space-x-3 text-slate-600 dark:text-slate-400">
                <div className="w-2 h-2 bg-slate-500 rounded-full"></div>
                <span className="text-sm">企业级安全保障</span>
              </div>
            </div>
          </div>

          {/* 右侧登录表单 */}
          <div className="flex justify-center lg:justify-end">
            <Card className="w-full max-w-md border-0 shadow-2xl bg-white/90 dark:bg-slate-900/90 backdrop-blur-xl hover:shadow-3xl transition-all duration-300 border-l-4 border-l-blue-600">
              <CardContent className="p-8">
                <div className="space-y-6">
                  {/* 移动端品牌显示 */}
                  <div className="lg:hidden text-center space-y-2">
                    <div className="flex justify-center">
                      <div className="relative">
                        <div className="absolute inset-0 bg-gradient-to-r from-blue-600 to-indigo-600 rounded-xl blur-lg opacity-75"></div>
                        <div className="relative bg-gradient-to-r from-blue-600 to-indigo-600 p-2 rounded-xl">
                          <EmotionIcon className="h-6 w-6 text-white" />
                        </div>
                      </div>
                    </div>
                    <h1 className="text-xl font-bold bg-gradient-to-r from-slate-900 to-blue-700 dark:from-white dark:to-blue-300 bg-clip-text text-transparent">
                      情感识别分析平台
                    </h1>
                  </div>

                  <div className="text-center space-y-2">
                    <h2 className="text-2xl font-semibold text-slate-900 dark:text-white">
                      系统登录
                    </h2>
                    <p className="text-slate-600 dark:text-slate-400">请输入您的用户名和密码</p>
                  </div>
                  <LoginForm />
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>

      {/* 底部装饰 */}
      <div className="absolute bottom-0 left-0 right-0 h-px bg-gradient-to-r from-transparent via-blue-300 dark:via-blue-600 to-transparent"></div>
    </div>
  )
}
