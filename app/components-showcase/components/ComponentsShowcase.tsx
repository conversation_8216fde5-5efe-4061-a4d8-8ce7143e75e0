'use client'

import { useState } from 'react'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON>bsList, TabsTrigger } from '@/components/ui/tabs'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'

import { UIComponentsSection } from './UIComponentsSection'
import { BusinessComponentsSection } from './BusinessComponentsSection'
import { ComplexComponentsSection } from './ComplexComponentsSection'
import { DebugSection } from './DebugSection'

export function ComponentsShowcase() {
  const [activeTab, setActiveTab] = useState('ui-components')

  return (
    <div className="space-y-6">
      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="ui-components">UI 基础组件</TabsTrigger>
          <TabsTrigger value="business-components">业务组件</TabsTrigger>
          <TabsTrigger value="complex-components">复合组件</TabsTrigger>
          <TabsTrigger value="debug-tools">调试工具</TabsTrigger>
        </TabsList>

        <TabsContent value="ui-components" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>UI 基础组件</CardTitle>
              <CardDescription>
                项目中所有基础UI组件的展示，包括按钮、输入框、选择器等通用组件
              </CardDescription>
            </CardHeader>
            <CardContent>
              <UIComponentsSection />
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="business-components" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>业务组件</CardTitle>
              <CardDescription>
                与具体业务逻辑相关的组件，如表情识别、设备选择等功能组件
              </CardDescription>
            </CardHeader>
            <CardContent>
              <BusinessComponentsSection />
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="complex-components" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>复合组件</CardTitle>
              <CardDescription>
                由多个基础组件组合而成的复杂组件，如数据表格、对话框等
              </CardDescription>
            </CardHeader>
            <CardContent>
              <ComplexComponentsSection />
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="debug-tools" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>调试工具</CardTitle>
              <CardDescription>
                用于调试组件属性、状态和行为的工具集合
              </CardDescription>
            </CardHeader>
            <CardContent>
              <DebugSection />
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}