'use client'

import { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { But<PERSON> } from '@/components/ui/button'
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import { Sheet, SheetContent, SheetDescription, SheetHeader, SheetTitle, SheetTrigger } from '@/components/ui/sheet'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Checkbox } from '@/components/ui/checkbox'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Ta<PERSON>, <PERSON>bs<PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs'
import { DynamicTags } from '@/components/ui/dynamic-tags'
import { ChevronLeft, ChevronRight, Info, Settings, Plus, Download, Upload } from 'lucide-react'

// 模拟数据表格数据
const mockTableData = [
  { id: 1, name: '张三', email: '<EMAIL>', role: '管理员', status: 'active', created: '2024-01-15' },
  { id: 2, name: '李四', email: '<EMAIL>', role: '用户', status: 'active', created: '2024-01-14' },
  { id: 3, name: '王五', email: '<EMAIL>', role: '用户', status: 'inactive', created: '2024-01-13' },
  { id: 4, name: '赵六', email: '<EMAIL>', role: '编辑', status: 'active', created: '2024-01-12' },
]

function MockDataTable() {
  const [selectedRows, setSelectedRows] = useState<number[]>([])
  const [searchTerm, setSearchTerm] = useState('')
  
  const filteredData = mockTableData.filter(item => 
    item.name.includes(searchTerm) || item.email.includes(searchTerm)
  )
  
  const toggleRowSelection = (id: number) => {
    setSelectedRows(prev => 
      prev.includes(id) 
        ? prev.filter(rowId => rowId !== id)
        : [...prev, id]
    )
  }
  
  const toggleAllSelection = () => {
    setSelectedRows(prev => 
      prev.length === filteredData.length ? [] : filteredData.map(item => item.id)
    )
  }
  
  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <Input
            placeholder="搜索用户..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-64"
          />
          <Button variant="outline" size="sm">
            <Settings className="h-4 w-4 mr-2" />
            筛选
          </Button>
        </div>
        <div className="flex items-center gap-2">
          <Button size="sm">
            <Plus className="h-4 w-4 mr-2" />
            新增
          </Button>
          <Button variant="outline" size="sm">
            <Download className="h-4 w-4 mr-2" />
            导出
          </Button>
        </div>
      </div>
      
      <div className="border rounded-lg">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead className="w-12">
                <Checkbox
                  checked={selectedRows.length === filteredData.length}
                  onCheckedChange={toggleAllSelection}
                />
              </TableHead>
              <TableHead>姓名</TableHead>
              <TableHead>邮箱</TableHead>
              <TableHead>角色</TableHead>
              <TableHead>状态</TableHead>
              <TableHead>创建时间</TableHead>
              <TableHead>操作</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {filteredData.map((item) => (
              <TableRow key={item.id}>
                <TableCell>
                  <Checkbox
                    checked={selectedRows.includes(item.id)}
                    onCheckedChange={() => toggleRowSelection(item.id)}
                  />
                </TableCell>
                <TableCell className="font-medium">{item.name}</TableCell>
                <TableCell>{item.email}</TableCell>
                <TableCell>{item.role}</TableCell>
                <TableCell>
                  <Badge variant={item.status === 'active' ? 'default' : 'secondary'}>
                    {item.status === 'active' ? '活跃' : '非活跃'}
                  </Badge>
                </TableCell>
                <TableCell>{item.created}</TableCell>
                <TableCell>
                  <div className="flex gap-1">
                    <Button variant="ghost" size="sm">编辑</Button>
                    <Button variant="ghost" size="sm">删除</Button>
                  </div>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>
      
      <div className="flex items-center justify-between text-sm text-gray-600">
        <div>已选择 {selectedRows.length} 项，共 {filteredData.length} 项</div>
        <div className="flex items-center gap-2">
          <Button variant="ghost" size="sm" disabled>
            <ChevronLeft className="h-4 w-4" />
          </Button>
          <span>第 1 页，共 1 页</span>
          <Button variant="ghost" size="sm" disabled>
            <ChevronRight className="h-4 w-4" />
          </Button>
        </div>
      </div>
    </div>
  )
}

function MockAnnotationDialog() {
  const [imageUrl, setImageUrl] = useState('/placeholder.svg')
  const [annotations, setAnnotations] = useState([
    { id: 1, x: 100, y: 100, width: 50, height: 50, label: '脸部' },
    { id: 2, x: 200, y: 150, width: 30, height: 30, label: '眼睛' },
  ])
  
  return (
    <div className="space-y-4">
      <div className="aspect-video bg-gray-100 border rounded-lg flex items-center justify-center relative">
        <img 
          src={imageUrl} 
          alt="Annotation target"
          className="max-w-full max-h-full object-contain"
        />
        {annotations.map((annotation) => (
          <div
            key={annotation.id}
            className="absolute border-2 border-red-500 bg-red-500/20"
            style={{
              left: annotation.x,
              top: annotation.y,
              width: annotation.width,
              height: annotation.height,
            }}
          >
            <span className="absolute -top-6 left-0 text-xs bg-red-500 text-white px-1 rounded">
              {annotation.label}
            </span>
          </div>
        ))}
      </div>
      
      <div className="space-y-2">
        <h4 className="font-medium">标注列表</h4>
        {annotations.map((annotation) => (
          <div key={annotation.id} className="flex items-center justify-between p-2 border rounded">
            <span>{annotation.label}</span>
            <div className="text-sm text-gray-500">
              ({annotation.x}, {annotation.y}) {annotation.width}×{annotation.height}
            </div>
          </div>
        ))}
      </div>
      
      <div className="flex gap-2">
        <Button size="sm">
          <Plus className="h-4 w-4 mr-2" />
          添加标注
        </Button>
        <Button variant="outline" size="sm">保存</Button>
        <Button variant="outline" size="sm">重置</Button>
      </div>
    </div>
  )
}

export function ComplexComponentsSection() {
  const [tags, setTags] = useState(['React', 'TypeScript', 'Next.js'])
  
  return (
    <div className="space-y-8">
      <Alert>
        <Info className="h-4 w-4" />
        <AlertDescription>
          复合组件通常由多个基础组件组合而成，提供更完整的功能体验。
        </AlertDescription>
      </Alert>

      {/* 数据表格组件 */}
      <Card>
        <CardHeader>
          <CardTitle>数据表格 DataTable</CardTitle>
          <CardDescription>
            功能完整的数据表格组件，支持搜索、筛选、分页、选择等功能
          </CardDescription>
        </CardHeader>
        <CardContent>
          <MockDataTable />
        </CardContent>
      </Card>

      {/* 对话框组件 */}
      <Card>
        <CardHeader>
          <CardTitle>对话框和抽屉 Dialog & Sheet</CardTitle>
          <CardDescription>
            各种弹出式容器组件，用于展示详细信息或收集用户输入
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex gap-4">
            <Dialog>
              <DialogTrigger asChild>
                <Button variant="outline">打开对话框</Button>
              </DialogTrigger>
              <DialogContent>
                <DialogHeader>
                  <DialogTitle>示例对话框</DialogTitle>
                  <DialogDescription>
                    这是一个模态对话框的示例，可以用于显示重要信息或收集用户输入。
                  </DialogDescription>
                </DialogHeader>
                <div className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="dialog-input">输入内容</Label>
                    <Input id="dialog-input" placeholder="请输入内容..." />
                  </div>
                  <div className="flex justify-end gap-2">
                    <Button variant="outline">取消</Button>
                    <Button>确认</Button>
                  </div>
                </div>
              </DialogContent>
            </Dialog>

            <Sheet>
              <SheetTrigger asChild>
                <Button variant="outline">打开侧边栏</Button>
              </SheetTrigger>
              <SheetContent>
                <SheetHeader>
                  <SheetTitle>侧边抽屉</SheetTitle>
                  <SheetDescription>
                    这是一个从侧边滑出的抽屉组件，适合显示详细信息或设置面板。
                  </SheetDescription>
                </SheetHeader>
                <div className="space-y-4 mt-4">
                  <div className="space-y-2">
                    <Label>设置选项</Label>
                    <div className="space-y-2">
                      <div className="flex items-center space-x-2">
                        <Checkbox id="option1" />
                        <Label htmlFor="option1">启用通知</Label>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Checkbox id="option2" />
                        <Label htmlFor="option2">自动保存</Label>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Checkbox id="option3" />
                        <Label htmlFor="option3">暗色主题</Label>
                      </div>
                    </div>
                  </div>
                </div>
              </SheetContent>
            </Sheet>
          </div>
        </CardContent>
      </Card>

      {/* 标签页组件 */}
      <Card>
        <CardHeader>
          <CardTitle>标签页 Tabs</CardTitle>
          <CardDescription>
            用于在多个相关内容之间切换的标签页组件
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Tabs defaultValue="tab1" className="w-full">
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="tab1">基本信息</TabsTrigger>
              <TabsTrigger value="tab2">高级设置</TabsTrigger>
              <TabsTrigger value="tab3">权限管理</TabsTrigger>
            </TabsList>
            <TabsContent value="tab1" className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="name">姓名</Label>
                <Input id="name" placeholder="请输入姓名" />
              </div>
              <div className="space-y-2">
                <Label htmlFor="email">邮箱</Label>
                <Input id="email" type="email" placeholder="请输入邮箱" />
              </div>
            </TabsContent>
            <TabsContent value="tab2" className="space-y-4">
              <div className="space-y-2">
                <Label>语言设置</Label>
                <select className="w-full p-2 border rounded">
                  <option>中文</option>
                  <option>English</option>
                  <option>日本語</option>
                </select>
              </div>
              <div className="space-y-2">
                <Label>时区设置</Label>
                <select className="w-full p-2 border rounded">
                  <option>Asia/Shanghai</option>
                  <option>America/New_York</option>
                  <option>Europe/London</option>
                </select>
              </div>
            </TabsContent>
            <TabsContent value="tab3" className="space-y-4">
              <div className="space-y-2">
                <Label>权限列表</Label>
                <div className="space-y-2">
                  <div className="flex items-center space-x-2">
                    <Checkbox id="perm1" />
                    <Label htmlFor="perm1">读取权限</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Checkbox id="perm2" />
                    <Label htmlFor="perm2">写入权限</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Checkbox id="perm3" />
                    <Label htmlFor="perm3">管理权限</Label>
                  </div>
                </div>
              </div>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>

      {/* 动态标签组件 */}
      <Card>
        <CardHeader>
          <CardTitle>动态标签 DynamicTags</CardTitle>
          <CardDescription>
            允许用户动态添加和删除标签的复合组件
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <DynamicTags
              value={tags}
              onChange={setTags}
              placeholder="添加技术标签..."
            />
            <p className="text-sm text-gray-600">
              当前标签: {tags.join(', ')}
            </p>
          </div>
        </CardContent>
      </Card>

      {/* 图像标注组件 */}
      <Card>
        <CardHeader>
          <CardTitle>图像标注组件</CardTitle>
          <CardDescription>
            用于图像标注和区域选择的复合组件
          </CardDescription>
        </CardHeader>
        <CardContent>
          <MockAnnotationDialog />
        </CardContent>
      </Card>

      {/* 文件上传组件 */}
      <Card>
        <CardHeader>
          <CardTitle>文件上传组件</CardTitle>
          <CardDescription>
            支持拖放上传的文件处理组件
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center">
            <Upload className="mx-auto h-12 w-12 text-gray-400 mb-4" />
            <p className="text-lg font-medium text-gray-700 mb-2">
              拖放文件到这里或点击上传
            </p>
            <p className="text-sm text-gray-500 mb-4">
              支持 JPG, PNG, GIF 格式，最大 10MB
            </p>
            <Button>选择文件</Button>
          </div>
          <div className="mt-4 space-y-2">
            <div className="flex items-center justify-between p-2 bg-gray-50 rounded">
              <span className="text-sm">example-image.jpg</span>
              <div className="flex items-center gap-2">
                <Badge variant="outline">已上传</Badge>
                <Button variant="ghost" size="sm">删除</Button>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}