'use client'

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Separator } from '@/components/ui/separator'
import { useState } from 'react'
import { Info, AlertTriangle } from 'lucide-react'

// 模拟组件展示，避免实际的依赖问题
function MockEmotionResults() {
  const [emotion, setEmotion] = useState('😊 快乐')
  
  const emotions = ['😊 快乐', '😢 悲伤', '😠 愤怒', '😨 恐惧', '😲 惊讶', '😐 中性']
  
  return (
    <div className="bg-white p-6 rounded-lg shadow-md border">
      <h3 className="text-lg font-bold mb-4">表情识别结果</h3>
      <div className="text-3xl font-bold text-center p-4 bg-gray-100 rounded-lg mb-4">
        {emotion}
      </div>
      <div className="flex gap-2 flex-wrap">
        {emotions.map((emo) => (
          <Button
            key={emo}
            variant={emotion === emo ? "default" : "outline"}
            size="sm"
            onClick={() => setEmotion(emo)}
          >
            {emo}
          </Button>
        ))}
      </div>
    </div>
  )
}

function MockDeviceSelector() {
  const [selectedDevice, setSelectedDevice] = useState('camera-0')
  
  const devices = [
    { id: 'camera-0', name: '内置摄像头', status: 'online' },
    { id: 'camera-1', name: '外置摄像头', status: 'online' },
    { id: 'camera-2', name: '网络摄像头', status: 'offline' },
  ]
  
  return (
    <div className="space-y-3">
      <h3 className="text-lg font-semibold">设备选择</h3>
      <div className="space-y-2">
        {devices.map((device) => (
          <div
            key={device.id}
            className={`p-3 border rounded-lg cursor-pointer transition-colors ${
              selectedDevice === device.id
                ? 'border-blue-500 bg-blue-50'
                : 'border-gray-200 hover:border-gray-300'
            }`}
            onClick={() => setSelectedDevice(device.id)}
          >
            <div className="flex items-center justify-between">
              <span className="font-medium">{device.name}</span>
              <Badge variant={device.status === 'online' ? 'default' : 'secondary'}>
                {device.status === 'online' ? '在线' : '离线'}
              </Badge>
            </div>
          </div>
        ))}
      </div>
    </div>
  )
}

function MockModelSelector() {
  const [selectedModel, setSelectedModel] = useState('emotion-v2')
  
  const models = [
    { id: 'emotion-v1', name: '表情识别模型 v1.0', accuracy: '85%' },
    { id: 'emotion-v2', name: '表情识别模型 v2.0', accuracy: '92%' },
    { id: 'emotion-v3', name: '表情识别模型 v3.0 (Beta)', accuracy: '95%' },
  ]
  
  return (
    <div className="space-y-3">
      <h3 className="text-lg font-semibold">模型选择</h3>
      <div className="space-y-2">
        {models.map((model) => (
          <div
            key={model.id}
            className={`p-3 border rounded-lg cursor-pointer transition-colors ${
              selectedModel === model.id
                ? 'border-green-500 bg-green-50'
                : 'border-gray-200 hover:border-gray-300'
            }`}
            onClick={() => setSelectedModel(model.id)}
          >
            <div className="flex items-center justify-between">
              <div>
                <span className="font-medium">{model.name}</span>
                <p className="text-sm text-gray-600">准确率: {model.accuracy}</p>
              </div>
              {model.id === 'emotion-v3' && (
                <Badge variant="outline">Beta</Badge>
              )}
            </div>
          </div>
        ))}
      </div>
    </div>
  )
}

function MockVideoStream() {
  const [isStreaming, setIsStreaming] = useState(false)
  
  return (
    <div className="space-y-3">
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-semibold">视频流</h3>
        <Button
          onClick={() => setIsStreaming(!isStreaming)}
          variant={isStreaming ? "destructive" : "default"}
        >
          {isStreaming ? '停止' : '开始'}
        </Button>
      </div>
      <div className="aspect-video bg-gray-900 rounded-lg flex items-center justify-center text-white">
        {isStreaming ? (
          <div className="text-center">
            <div className="w-3 h-3 bg-red-500 rounded-full animate-pulse mb-2 mx-auto"></div>
            <p>实时视频流</p>
            <p className="text-sm text-gray-300">1920x1080 @ 30fps</p>
          </div>
        ) : (
          <div className="text-center text-gray-400">
            <p>点击开始按钮启动视频流</p>
          </div>
        )}
      </div>
    </div>
  )
}

function MockHistoryList() {
  const historyItems = [
    { id: 1, timestamp: '2024-01-15 14:30:25', emotion: '快乐', confidence: 0.95 },
    { id: 2, timestamp: '2024-01-15 14:28:12', emotion: '中性', confidence: 0.88 },
    { id: 3, timestamp: '2024-01-15 14:25:45', emotion: '惊讶', confidence: 0.92 },
    { id: 4, timestamp: '2024-01-15 14:23:18', emotion: '悲伤', confidence: 0.79 },
  ]
  
  return (
    <div className="space-y-3">
      <h3 className="text-lg font-semibold">识别历史</h3>
      <div className="space-y-2 max-h-64 overflow-y-auto">
        {historyItems.map((item) => (
          <div key={item.id} className="p-3 border rounded-lg bg-white">
            <div className="flex items-center justify-between">
              <div>
                <p className="font-medium">{item.emotion}</p>
                <p className="text-sm text-gray-600">{item.timestamp}</p>
              </div>
              <div className="text-right">
                <Badge variant="outline">
                  {Math.round(item.confidence * 100)}%
                </Badge>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  )
}

export function BusinessComponentsSection() {
  const [showLoginDemo, setShowLoginDemo] = useState(false)
  
  return (
    <div className="space-y-8">
      <Alert>
        <Info className="h-4 w-4" />
        <AlertDescription>
          这里展示的业务组件使用了模拟数据，实际运行时会连接到真实的服务和数据源。
        </AlertDescription>
      </Alert>

      {/* 表情识别相关组件 */}
      <Card>
        <CardHeader>
          <CardTitle>表情识别组件</CardTitle>
          <CardDescription>
            与表情识别功能相关的核心业务组件
          </CardDescription>
        </CardHeader>
        <CardContent>
          <MockEmotionResults />
        </CardContent>
      </Card>

      {/* 设备和模型选择组件 */}
      <Card>
        <CardHeader>
          <CardTitle>选择器组件</CardTitle>
          <CardDescription>
            用于选择设备、模型等配置的业务组件
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <MockDeviceSelector />
            <MockModelSelector />
          </div>
        </CardContent>
      </Card>

      {/* 视频相关组件 */}
      <Card>
        <CardHeader>
          <CardTitle>视频处理组件</CardTitle>
          <CardDescription>
            用于视频流展示和回放的组件
          </CardDescription>
        </CardHeader>
        <CardContent>
          <MockVideoStream />
        </CardContent>
      </Card>

      {/* 历史记录组件 */}
      <Card>
        <CardHeader>
          <CardTitle>历史记录组件</CardTitle>
          <CardDescription>
            展示识别历史和相关数据的组件
          </CardDescription>
        </CardHeader>
        <CardContent>
          <MockHistoryList />
        </CardContent>
      </Card>

      {/* 登录表单组件 */}
      <Card>
        <CardHeader>
          <CardTitle>登录表单组件</CardTitle>
          <CardDescription>
            用户认证相关的表单组件（演示模式）
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Alert className="mb-4">
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>
              登录表单组件需要特殊的上下文环境，在实际项目中会连接到认证服务。
              这里提供简化的展示版本。
            </AlertDescription>
          </Alert>
          
          {!showLoginDemo ? (
            <Button onClick={() => setShowLoginDemo(true)}>
              显示登录表单演示
            </Button>
          ) : (
            <div className="space-y-4 p-4 border rounded-lg bg-gray-50">
              <div className="space-y-2">
                <label className="text-sm font-medium">用户名</label>
                <input 
                  type="text" 
                  className="w-full p-2 border rounded"
                  placeholder="请输入用户名"
                  defaultValue="demo_user"
                />
              </div>
              <div className="space-y-2">
                <label className="text-sm font-medium">密码</label>
                <input 
                  type="password" 
                  className="w-full p-2 border rounded"
                  placeholder="请输入密码"
                />
              </div>
              <div className="space-y-2">
                <label className="text-sm font-medium">验证码</label>
                <div className="flex gap-2">
                  <input 
                    type="text" 
                    className="flex-1 p-2 border rounded"
                    placeholder="验证码"
                  />
                  <div className="w-24 h-10 bg-gray-200 rounded flex items-center justify-center text-sm">
                    ABCD
                  </div>
                </div>
              </div>
              <Button className="w-full">登录 (演示)</Button>
              <Button 
                variant="outline" 
                className="w-full"
                onClick={() => setShowLoginDemo(false)}
              >
                隐藏演示
              </Button>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}