'use client'

import { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Checkbox } from '@/components/ui/checkbox'
import { Switch } from '@/components/ui/switch'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Badge } from '@/components/ui/badge'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert'
import { Progress } from '@/components/ui/progress'
import { Separator } from '@/components/ui/separator'
import { Skeleton } from '@/components/ui/skeleton'
import { Slider } from '@/components/ui/slider'
import { Textarea } from '@/components/ui/textarea'
import { Toggle } from '@/components/ui/toggle'
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group'
import { Bell, AlertCircle, CheckCircle, User } from 'lucide-react'

export function UIComponentsSection() {
  const [inputValue, setInputValue] = useState('')
  const [isChecked, setIsChecked] = useState(false)
  const [isSwitchOn, setIsSwitchOn] = useState(false)
  const [selectValue, setSelectValue] = useState('')
  const [sliderValue, setSliderValue] = useState([50])
  const [textareaValue, setTextareaValue] = useState('')
  const [radioValue, setRadioValue] = useState('')

  return (
    <div className="space-y-8">
      {/* 按钮组件 */}
      <Card>
        <CardHeader>
          <CardTitle>Button 按钮</CardTitle>
          <CardDescription>
            各种样式和大小的按钮组件，支持不同的变体和状态
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <h4 className="text-sm font-medium mb-3">按钮变体</h4>
            <div className="flex flex-wrap gap-3">
              <Button variant="default">Default</Button>
              <Button variant="secondary">Secondary</Button>
              <Button variant="destructive">Destructive</Button>
              <Button variant="outline">Outline</Button>
              <Button variant="ghost">Ghost</Button>
              <Button variant="link">Link</Button>
            </div>
          </div>
          <div>
            <h4 className="text-sm font-medium mb-3">按钮大小</h4>
            <div className="flex flex-wrap items-center gap-3">
              <Button size="sm">Small</Button>
              <Button size="default">Default</Button>
              <Button size="lg">Large</Button>
              <Button size="icon"><User className="h-4 w-4" /></Button>
            </div>
          </div>
          <div>
            <h4 className="text-sm font-medium mb-3">按钮状态</h4>
            <div className="flex flex-wrap gap-3">
              <Button>Normal</Button>
              <Button disabled>Disabled</Button>
              <Button>
                <Bell className="mr-2 h-4 w-4" />
                With Icon
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 表单组件 */}
      <Card>
        <CardHeader>
          <CardTitle>Form 表单组件</CardTitle>
          <CardDescription>
            各种表单输入组件，包括输入框、选择器、复选框等
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-2">
              <Label htmlFor="input-demo">输入框</Label>
              <Input
                id="input-demo"
                placeholder="请输入内容..."
                value={inputValue}
                onChange={(e) => setInputValue(e.target.value)}
              />
              <p className="text-xs text-muted-foreground">
                当前值: {inputValue || '(空)'}
              </p>
            </div>

            <div className="space-y-2">
              <Label htmlFor="select-demo">选择器</Label>
              <Select value={selectValue} onValueChange={setSelectValue}>
                <SelectTrigger>
                  <SelectValue placeholder="请选择选项" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="option1">选项一</SelectItem>
                  <SelectItem value="option2">选项二</SelectItem>
                  <SelectItem value="option3">选项三</SelectItem>
                </SelectContent>
              </Select>
              <p className="text-xs text-muted-foreground">
                当前值: {selectValue || '(未选择)'}
              </p>
            </div>

            <div className="space-y-3">
              <Label>复选框和开关</Label>
              <div className="flex items-center space-x-4">
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="checkbox-demo"
                    checked={isChecked}
                    onCheckedChange={(checked) => setIsChecked(checked === true)}
                  />
                  <Label htmlFor="checkbox-demo">复选框</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <Switch
                    id="switch-demo"
                    checked={isSwitchOn}
                    onCheckedChange={setIsSwitchOn}
                  />
                  <Label htmlFor="switch-demo">开关</Label>
                </div>
              </div>
              <p className="text-xs text-muted-foreground">
                复选框: {isChecked ? '选中' : '未选中'}, 开关: {isSwitchOn ? '开启' : '关闭'}
              </p>
            </div>

            <div className="space-y-3">
              <Label>单选按钮组</Label>
              <RadioGroup value={radioValue} onValueChange={setRadioValue}>
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="radio1" id="radio1" />
                  <Label htmlFor="radio1">选项 1</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="radio2" id="radio2" />
                  <Label htmlFor="radio2">选项 2</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="radio3" id="radio3" />
                  <Label htmlFor="radio3">选项 3</Label>
                </div>
              </RadioGroup>
              <p className="text-xs text-muted-foreground">
                当前值: {radioValue || '(未选择)'}
              </p>
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="textarea-demo">文本域</Label>
            <Textarea
              id="textarea-demo"
              placeholder="请输入多行文本..."
              value={textareaValue}
              onChange={(e) => setTextareaValue(e.target.value)}
              className="min-h-[100px]"
            />
            <p className="text-xs text-muted-foreground">
              字符数: {textareaValue.length}
            </p>
          </div>

          <div className="space-y-2">
            <Label>滑块 (当前值: {sliderValue[0]})</Label>
            <Slider
              value={sliderValue}
              onValueChange={setSliderValue}
              max={100}
              min={0}
              step={1}
              className="w-full"
            />
          </div>
        </CardContent>
      </Card>

      {/* 显示组件 */}
      <Card>
        <CardHeader>
          <CardTitle>Display 显示组件</CardTitle>
          <CardDescription>
            用于展示信息的各种组件，包括徽章、头像、警告框等
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <div>
            <h4 className="text-sm font-medium mb-3">徽章 Badge</h4>
            <div className="flex flex-wrap gap-2">
              <Badge>Default</Badge>
              <Badge variant="secondary">Secondary</Badge>
              <Badge variant="destructive">Destructive</Badge>
              <Badge variant="outline">Outline</Badge>
            </div>
          </div>

          <div>
            <h4 className="text-sm font-medium mb-3">头像 Avatar</h4>
            <div className="flex gap-3">
              <Avatar>
                <AvatarImage src="/placeholder.svg" alt="@user" />
                <AvatarFallback>U1</AvatarFallback>
              </Avatar>
              <Avatar>
                <AvatarFallback>U2</AvatarFallback>
              </Avatar>
              <Avatar className="w-12 h-12">
                <AvatarFallback>LG</AvatarFallback>
              </Avatar>
            </div>
          </div>

          <div>
            <h4 className="text-sm font-medium mb-3">警告框 Alert</h4>
            <div className="space-y-3">
              <Alert>
                <AlertCircle className="h-4 w-4" />
                <AlertTitle>提示</AlertTitle>
                <AlertDescription>
                  这是一个普通的提示信息。
                </AlertDescription>
              </Alert>
              <Alert variant="destructive">
                <AlertCircle className="h-4 w-4" />
                <AlertTitle>错误</AlertTitle>
                <AlertDescription>
                  这是一个错误信息，需要用户注意。
                </AlertDescription>
              </Alert>
            </div>
          </div>

          <div>
            <h4 className="text-sm font-medium mb-3">进度条 Progress</h4>
            <div className="space-y-2">
              <Progress value={33} className="w-full" />
              <Progress value={66} className="w-full" />
              <Progress value={90} className="w-full" />
            </div>
          </div>

          <div>
            <h4 className="text-sm font-medium mb-3">分隔线 Separator</h4>
            <div className="space-y-2">
              <p>内容上方</p>
              <Separator />
              <p>内容下方</p>
            </div>
          </div>

          <div>
            <h4 className="text-sm font-medium mb-3">骨架屏 Skeleton</h4>
            <div className="space-y-2">
              <Skeleton className="h-4 w-full" />
              <Skeleton className="h-4 w-[80%]" />
              <Skeleton className="h-4 w-[60%]" />
            </div>
          </div>

          <div>
            <h4 className="text-sm font-medium mb-3">切换按钮 Toggle</h4>
            <div className="flex gap-2">
              <Toggle>Normal</Toggle>
              <Toggle pressed>Pressed</Toggle>
              <Toggle disabled>Disabled</Toggle>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}