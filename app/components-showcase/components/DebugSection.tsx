'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Switch } from '@/components/ui/switch'
import { Slider } from '@/components/ui/slider'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { <PERSON><PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Monitor, Smartphone, Tablet, RefreshCw, Bug, Palette, Zap } from 'lucide-react'

// 响应式布局测试器
function ResponsiveDebugger() {
  const [screenSize, setScreenSize] = useState('desktop')
  const [windowSize, setWindowSize] = useState({ width: 0, height: 0 })
  
  useEffect(() => {
    const updateSize = () => {
      setWindowSize({ width: window.innerWidth, height: window.innerHeight })
    }
    
    updateSize()
    window.addEventListener('resize', updateSize)
    return () => window.removeEventListener('resize', updateSize)
  }, [])
  
  const presets = {
    mobile: { width: 375, height: 667, icon: Smartphone },
    tablet: { width: 768, height: 1024, icon: Tablet },
    desktop: { width: 1920, height: 1080, icon: Monitor },
  }
  
  return (
    <div className="space-y-4">
      <div className="flex gap-2">
        {Object.entries(presets).map(([key, preset]) => {
          const Icon = preset.icon
          return (
            <Button
              key={key}
              variant={screenSize === key ? "default" : "outline"}
              size="sm"
              onClick={() => setScreenSize(key)}
            >
              <Icon className="h-4 w-4 mr-2" />
              {key}
            </Button>
          )
        })}
      </div>
      
      <div className="grid grid-cols-2 gap-4 text-sm">
        <div>当前窗口: {windowSize.width} × {windowSize.height}</div>
        <div>预设尺寸: {presets[screenSize as keyof typeof presets].width} × {presets[screenSize as keyof typeof presets].height}</div>
      </div>
      
      <div className="space-y-2">
        <Label>断点测试</Label>
        <div className="flex gap-2 text-xs">
          <Badge variant={windowSize.width < 640 ? "default" : "outline"}>sm (&lt;640px)</Badge>
          <Badge variant={windowSize.width >= 640 && windowSize.width < 768 ? "default" : "outline"}>md (≥640px)</Badge>
          <Badge variant={windowSize.width >= 768 && windowSize.width < 1024 ? "default" : "outline"}>lg (≥768px)</Badge>
          <Badge variant={windowSize.width >= 1024 && windowSize.width < 1280 ? "default" : "outline"}>xl (≥1024px)</Badge>
          <Badge variant={windowSize.width >= 1280 ? "default" : "outline"}>2xl (≥1280px)</Badge>
        </div>
      </div>
    </div>
  )
}

// 组件属性调试器
function PropsDebugger() {
  const [buttonVariant, setButtonVariant] = useState('default')
  const [buttonSize, setButtonSize] = useState('default')
  const [buttonDisabled, setButtonDisabled] = useState(false)
  const [buttonText, setButtonText] = useState('示例按钮')
  
  const [inputValue, setInputValue] = useState('')
  const [inputPlaceholder, setInputPlaceholder] = useState('请输入内容...')
  const [inputDisabled, setInputDisabled] = useState(false)
  
  return (
    <div className="space-y-6">
      <div>
        <h4 className="font-medium mb-3">Button 组件调试</h4>
        <div className="grid grid-cols-2 gap-6">
          <div className="space-y-3">
            <div className="space-y-2">
              <Label>变体 (variant)</Label>
              <Select value={buttonVariant} onValueChange={setButtonVariant}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="default">default</SelectItem>
                  <SelectItem value="secondary">secondary</SelectItem>
                  <SelectItem value="destructive">destructive</SelectItem>
                  <SelectItem value="outline">outline</SelectItem>
                  <SelectItem value="ghost">ghost</SelectItem>
                  <SelectItem value="link">link</SelectItem>
                </SelectContent>
              </Select>
            </div>
            
            <div className="space-y-2">
              <Label>大小 (size)</Label>
              <Select value={buttonSize} onValueChange={setButtonSize}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="sm">sm</SelectItem>
                  <SelectItem value="default">default</SelectItem>
                  <SelectItem value="lg">lg</SelectItem>
                  <SelectItem value="icon">icon</SelectItem>
                </SelectContent>
              </Select>
            </div>
            
            <div className="space-y-2">
              <Label>文本内容</Label>
              <Input
                value={buttonText}
                onChange={(e) => setButtonText(e.target.value)}
              />
            </div>
            
            <div className="flex items-center space-x-2">
              <Switch
                id="button-disabled"
                checked={buttonDisabled}
                onCheckedChange={setButtonDisabled}
              />
              <Label htmlFor="button-disabled">禁用状态</Label>
            </div>
          </div>
          
          <div className="flex items-center justify-center p-8 border rounded-lg bg-gray-50">
            <Button
              variant={buttonVariant as any}
              size={buttonSize as any}
              disabled={buttonDisabled}
            >
              {buttonText}
            </Button>
          </div>
        </div>
        
        <div className="mt-3 p-3 bg-gray-100 rounded text-sm font-mono">
          {`<Button variant="${buttonVariant}" size="${buttonSize}"${buttonDisabled ? ' disabled' : ''}>{buttonText}</Button>`}
        </div>
      </div>
      
      <Separator />
      
      <div>
        <h4 className="font-medium mb-3">Input 组件调试</h4>
        <div className="grid grid-cols-2 gap-6">
          <div className="space-y-3">
            <div className="space-y-2">
              <Label>占位符 (placeholder)</Label>
              <Input
                value={inputPlaceholder}
                onChange={(e) => setInputPlaceholder(e.target.value)}
              />
            </div>
            
            <div className="flex items-center space-x-2">
              <Switch
                id="input-disabled"
                checked={inputDisabled}
                onCheckedChange={setInputDisabled}
              />
              <Label htmlFor="input-disabled">禁用状态</Label>
            </div>
          </div>
          
          <div className="flex items-center justify-center p-8 border rounded-lg bg-gray-50">
            <Input
              value={inputValue}
              onChange={(e) => setInputValue(e.target.value)}
              placeholder={inputPlaceholder}
              disabled={inputDisabled}
              className="w-full max-w-xs"
            />
          </div>
        </div>
        
        <div className="mt-3 p-3 bg-gray-100 rounded text-sm font-mono">
          {`<Input value="${inputValue}" placeholder="${inputPlaceholder}"${inputDisabled ? ' disabled' : ''} />`}
        </div>
      </div>
    </div>
  )
}

// 状态监视器
function StateMonitor() {
  const [counters, setCounters] = useState({ a: 0, b: 0, c: 0 })
  const [logs, setLogs] = useState<string[]>([])
  
  const addLog = (message: string) => {
    const timestamp = new Date().toLocaleTimeString()
    setLogs(prev => [`[${timestamp}] ${message}`, ...prev.slice(0, 9)])
  }
  
  const increment = (key: keyof typeof counters) => {
    setCounters(prev => ({ ...prev, [key]: prev[key] + 1 }))
    addLog(`计数器 ${key.toUpperCase()} 增加到 ${counters[key] + 1}`)
  }
  
  const reset = () => {
    setCounters({ a: 0, b: 0, c: 0 })
    addLog('所有计数器已重置')
  }
  
  return (
    <div className="space-y-4">
      <div className="grid grid-cols-3 gap-4">
        {Object.entries(counters).map(([key, value]) => (
          <div key={key} className="text-center p-4 border rounded-lg">
            <div className="text-2xl font-bold mb-2">{value}</div>
            <div className="text-sm text-gray-600 mb-2">计数器 {key.toUpperCase()}</div>
            <Button size="sm" onClick={() => increment(key as keyof typeof counters)}>
              +1
            </Button>
          </div>
        ))}
      </div>
      
      <div className="flex gap-2">
        <Button onClick={reset} variant="outline">
          <RefreshCw className="h-4 w-4 mr-2" />
          重置所有
        </Button>
        <Button onClick={() => setLogs([])} variant="outline">
          清空日志
        </Button>
      </div>
      
      <div>
        <h4 className="font-medium mb-2">状态变更日志</h4>
        <div className="bg-black text-green-400 p-3 rounded font-mono text-xs h-32 overflow-y-auto">
          {logs.length === 0 ? (
            <div className="text-gray-500">暂无日志...</div>
          ) : (
            logs.map((log, index) => (
              <div key={index}>{log}</div>
            ))
          )}
        </div>
      </div>
    </div>
  )
}

// 主题调试器
function ThemeDebugger() {
  const [primaryHue, setPrimaryHue] = useState([220])
  const [borderRadius, setBorderRadius] = useState([6])
  const [fontSize, setFontSize] = useState([14])
  
  return (
    <div className="space-y-6">
      <div className="space-y-4">
        <div className="space-y-2">
          <Label>主色调 (Hue): {primaryHue[0]}°</Label>
          <Slider
            value={primaryHue}
            onValueChange={setPrimaryHue}
            max={360}
            min={0}
            step={1}
            className="w-full"
          />
          <div 
            className="w-full h-8 rounded"
            style={{ backgroundColor: `hsl(${primaryHue[0]}, 70%, 50%)` }}
          />
        </div>
        
        <div className="space-y-2">
          <Label>圆角大小 (Border Radius): {borderRadius[0]}px</Label>
          <Slider
            value={borderRadius}
            onValueChange={setBorderRadius}
            max={20}
            min={0}
            step={1}
            className="w-full"
          />
        </div>
        
        <div className="space-y-2">
          <Label>字体大小 (Font Size): {fontSize[0]}px</Label>
          <Slider
            value={fontSize}
            onValueChange={setFontSize}
            max={20}
            min={10}
            step={1}
            className="w-full"
          />
        </div>
      </div>
      
      <div 
        className="p-4 border bg-white"
        style={{ 
          borderRadius: `${borderRadius[0]}px`,
          fontSize: `${fontSize[0]}px`
        }}
      >
        <h4 className="font-bold mb-2" style={{ color: `hsl(${primaryHue[0]}, 70%, 40%)` }}>
          主题预览
        </h4>
        <p className="text-gray-600 mb-3">
          这是使用当前主题设置的示例文本。你可以通过上面的滑块来调整各种样式参数。
        </p>
        <Button 
          className="mr-2"
          style={{ backgroundColor: `hsl(${primaryHue[0]}, 70%, 50%)` }}
        >
          主按钮
        </Button>
        <Button variant="outline">次要按钮</Button>
      </div>
      
      <div className="p-3 bg-gray-100 rounded text-sm font-mono">
        {`--primary-hue: ${primaryHue[0]};\n--border-radius: ${borderRadius[0]}px;\n--font-size: ${fontSize[0]}px;`}
      </div>
    </div>
  )
}

export function DebugSection() {
  const [activeDebugTab, setActiveDebugTab] = useState('responsive')
  
  return (
    <div className="space-y-6">
      <Alert>
        <Bug className="h-4 w-4" />
        <AlertDescription>
          调试工具帮助开发者测试组件的各种状态、属性和响应式行为。
        </AlertDescription>
      </Alert>

      <Tabs value={activeDebugTab} onValueChange={setActiveDebugTab} className="w-full">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="responsive">
            <Monitor className="h-4 w-4 mr-2" />
            响应式
          </TabsTrigger>
          <TabsTrigger value="props">
            <Zap className="h-4 w-4 mr-2" />
            属性调试
          </TabsTrigger>
          <TabsTrigger value="state">
            <RefreshCw className="h-4 w-4 mr-2" />
            状态监控
          </TabsTrigger>
          <TabsTrigger value="theme">
            <Palette className="h-4 w-4 mr-2" />
            主题调试
          </TabsTrigger>
        </TabsList>

        <TabsContent value="responsive">
          <Card>
            <CardHeader>
              <CardTitle>响应式布局调试器</CardTitle>
              <CardDescription>
                测试组件在不同屏幕尺寸下的表现和响应式断点
              </CardDescription>
            </CardHeader>
            <CardContent>
              <ResponsiveDebugger />
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="props">
          <Card>
            <CardHeader>
              <CardTitle>组件属性调试器</CardTitle>
              <CardDescription>
                实时调整组件属性，观察变化效果并生成对应代码
              </CardDescription>
            </CardHeader>
            <CardContent>
              <PropsDebugger />
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="state">
          <Card>
            <CardHeader>
              <CardTitle>状态变更监控器</CardTitle>
              <CardDescription>
                监控组件状态变化，记录状态变更日志
              </CardDescription>
            </CardHeader>
            <CardContent>
              <StateMonitor />
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="theme">
          <Card>
            <CardHeader>
              <CardTitle>主题样式调试器</CardTitle>
              <CardDescription>
                实时调整主题参数，预览样式效果
              </CardDescription>
            </CardHeader>
            <CardContent>
              <ThemeDebugger />
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}