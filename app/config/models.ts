export interface ModelVersion {
    id: string
    name: string
    version: string
    description: string
    isActive: boolean
}

export interface ModelType {
    id: string
    name: string
    description: string
    versions: ModelVersion[]
}

export const MODEL_TYPES: ModelType[] = [
    {
        id: 'basic_image',
        name: '基础图片模型',
        description: '适用于单一场景下的静态图片表情识别',
        versions: [
            {
                id: 'basic_image_v1',
                name: '基础图片模型 v1.0',
                version: '1.0.0',
                description: '支持7种基本表情识别，准确率95%',
                isActive: true
            },
            {
                id: 'basic_image_v2',
                name: '基础图片模型 v2.0',
                version: '2.0.0',
                description: '支持12种表情识别，准确率98%',
                isActive: true
            }
        ]
    },
    {
        id: 'basic_video',
        name: '基础视频模型',
        description: '适用于单一场景下的视频表情识别',
        versions: [
            {
                id: 'basic_video_v1',
                name: '基础视频模型 v1.0',
                version: '1.0.0',
                description: '支持7种基本表情识别，准确率92%',
                isActive: true
            },
            {
                id: 'basic_video_v2',
                name: '基础视频模型 v2.0',
                version: '2.0.0',
                description: '支持12种表情识别，准确率96%',
                isActive: true
            }
        ]
    },
    {
        id: 'basic_stream',
        name: '基础实时模型',
        description: '适用于单一场景下的实时表情识别',
        versions: [
            {
                id: 'basic_stream_v1',
                name: '基础实时模型 v1.0',
                version: '1.0.0',
                description: '支持7种基本表情识别，延迟<100ms',
                isActive: true
            },
            {
                id: 'basic_stream_v2',
                name: '基础实时模型 v2.0',
                version: '2.0.0',
                description: '支持12种表情识别，延迟<50ms',
                isActive: true
            }
        ]
    },
    {
        id: 'advanced_image',
        name: '高级图片模型',
        description: '适用于复杂场景下的静态图片表情识别',
        versions: [
            {
                id: 'advanced_image_v1',
                name: '高级图片模型 v1.0',
                version: '1.0.0',
                description: '支持15种表情识别，准确率97%',
                isActive: true
            },
            {
                id: 'advanced_image_v2',
                name: '高级图片模型 v2.0',
                version: '2.0.0',
                description: '支持20种表情识别，准确率99%',
                isActive: true
            }
        ]
    },
    {
        id: 'advanced_video',
        name: '高级视频模型',
        description: '适用于复杂场景下的视频表情识别',
        versions: [
            {
                id: 'advanced_video_v1',
                name: '高级视频模型 v1.0',
                version: '1.0.0',
                description: '支持15种表情识别，准确率94%',
                isActive: true
            },
            {
                id: 'advanced_video_v2',
                name: '高级视频模型 v2.0',
                version: '2.0.0',
                description: '支持20种表情识别，准确率98%',
                isActive: true
            }
        ]
    },
    {
        id: 'advanced_stream',
        name: '高级实时模型',
        description: '适用于复杂场景下的实时表情识别',
        versions: [
            {
                id: 'advanced_stream_v1',
                name: '高级实时模型 v1.0',
                version: '1.0.0',
                description: '支持15种表情识别，延迟<150ms',
                isActive: true
            },
            {
                id: 'advanced_stream_v2',
                name: '高级实时模型 v2.0',
                version: '2.0.0',
                description: '支持20种表情识别，延迟<80ms',
                isActive: true
            }
        ]
    },
    {
        id: 'multi_image',
        name: '多人图片模型',
        description: '适用于多人场景下的静态图片表情识别',
        versions: [
            {
                id: 'multi_image_v1',
                name: '多人图片模型 v1.0',
                version: '1.0.0',
                description: '支持10人同时识别，准确率93%',
                isActive: true
            },
            {
                id: 'multi_image_v2',
                name: '多人图片模型 v2.0',
                version: '2.0.0',
                description: '支持20人同时识别，准确率97%',
                isActive: true
            }
        ]
    },
    {
        id: 'multi_video',
        name: '多人视频模型',
        description: '适用于多人场景下的视频表情识别',
        versions: [
            {
                id: 'multi_video_v1',
                name: '多人视频模型 v1.0',
                version: '1.0.0',
                description: '支持10人同时识别，准确率90%',
                isActive: true
            },
            {
                id: 'multi_video_v2',
                name: '多人视频模型 v2.0',
                version: '2.0.0',
                description: '支持20人同时识别，准确率95%',
                isActive: true
            }
        ]
    },
    {
        id: 'multi_stream',
        name: '多人实时模型',
        description: '适用于多人场景下的实时表情识别',
        versions: [
            {
                id: 'multi_stream_v1',
                name: '多人实时模型 v1.0',
                version: '1.0.0',
                description: '支持10人同时识别，延迟<200ms',
                isActive: true
            },
            {
                id: 'multi_stream_v2',
                name: '多人实时模型 v2.0',
                version: '2.0.0',
                description: '支持20人同时识别，延迟<100ms',
                isActive: true
            }
        ]
    }
] 