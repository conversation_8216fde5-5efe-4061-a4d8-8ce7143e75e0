export interface Scene {
  id: string
  name: string
  description: string
  models: {
    image: {
      type: string
      version: string
    }
    video: {
      type: string
      version: string
    }
    stream: {
      type: string
      version: string
    }
  }
}

export const DETECTION_SCENES: Scene[] = [
  {
    id: 'interview',
    name: '询问场景',
    description: '适用于单一环境下的一对一询问场景，如警务询问、心理咨询等。环境光线较为稳定，主要识别单人面部表情变化。',
    models: {
      image: {
        type: 'basic_image',
        version: '2.0.0'
      },
      video: {
        type: 'basic_video',
        version: '2.0.0'
      },
      stream: {
        type: 'basic_stream',
        version: '2.0.0'
      }
    }
  },
  {
    id: 'meeting',
    name: '会议室约谈场景',
    description: '适用于室内会议室的约谈场景，如面试、业务洽谈等。环境相对封闭，光线条件稳定，需要同时关注2-3人的表情互动。',
    models: {
      image: {
        type: 'advanced_image',
        version: '2.0.0'
      },
      video: {
        type: 'advanced_video',
        version: '2.0.0'
      },
      stream: {
        type: 'advanced_stream',
        version: '2.0.0'
      }
    }
  },
  {
    id: 'indoor_public',
    name: '室内公共场所场景',
    description: '适用于商场、车站等室内公共场所，环境光线复杂多变，需要处理多人群体的表情识别，应对部分人脸遮挡等情况。',
    models: {
      image: {
        type: 'multi_image',
        version: '2.0.0'
      },
      video: {
        type: 'multi_video',
        version: '2.0.0'
      },
      stream: {
        type: 'multi_stream',
        version: '2.0.0'
      }
    }
  },
  {
    id: 'outdoor',
    name: '室外场景',
    description: '适用于街道、广场等室外环境，需要处理自然光线变化、天气影响等复杂情况，支持远距离人群表情捕捉和分析。',
    models: {
      image: {
        type: 'multi_image',
        version: '2.0.0'
      },
      video: {
        type: 'multi_video',
        version: '2.0.0'
      },
      stream: {
        type: 'multi_stream',
        version: '2.0.0'
      }
    }
  }
] 