'use client'

import { Inter } from 'next/font/google'
import './globals.css'
import { AppProvider } from '@/contexts/AppContext'
import { Toaster } from '@/components/ui/toaster'
import { TitleBar } from '@/components/ui/title-bar'
import React, { useEffect } from 'react'
import { AuthProvider } from '@/contexts/AuthContext'
import RouteGuard from '@/components/RouteGuard'

const inter = Inter({ subsets: ['latin'] })

export default function RootLayout({ children }: { children: React.ReactNode }) {
  useEffect(() => {
    // 监听认证状态变化
    const handleAuthLogout = () => {
      // 强制刷新页面到登录页
      window.location.href = '/login'
    }

    window.addEventListener('auth:logout', handleAuthLogout)

    return () => {
      window.removeEventListener('auth:logout', handleAuthLogout)
    }
  }, [])
  return (
    <html lang="zh">
      <body className={`${inter.className} h-screen relative`}>
        <AppProvider>
          <AuthProvider>
            <RouteGuard>
              {/*<TitleBar />*/}
              <div className=" w-full h-full">{children}</div>
              <Toaster />
            </RouteGuard>
          </AuthProvider>
        </AppProvider>
      </body>
    </html>
  )
}

// // 5. 应用入口配置
// // pages/_app.js
// import { AuthProvider } from '../contexts/AuthContext';
// import RouteGuard from '../components/RouteGuard';
// import { useEffect } from 'react';
//
// function MyApp({ Component, pageProps }) {
//   useEffect(() => {
//     // 监听认证状态变化
//     const handleAuthLogout = () => {
//       // 强制刷新页面到登录页
//       window.location.href = '/login';
//     };
//
//     window.addEventListener('auth:logout', handleAuthLogout);
//
//     return () => {
//       window.removeEventListener('auth:logout', handleAuthLogout);
//     };
//   }, []);
//
//   return (
//     <AuthProvider>
//       <RouteGuard>
//         <Component {...pageProps} />
//       </RouteGuard>
//     </AuthProvider>
//   );
// }
//
// export default MyApp;
