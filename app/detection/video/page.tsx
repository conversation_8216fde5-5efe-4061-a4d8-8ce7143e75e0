'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { ArrowLeft, Upload, Play, Pause, RotateCw } from 'lucide-react'
import Link from 'next/link'
import { toast } from '@/components/ui/use-toast'
import { DETECTION_SCENES } from '@/app/config/scenes'

export default function VideoDetectionPage() {
  const router = useRouter()
  const [selectedScene, setSelectedScene] = useState('')
  const [videoFile, setVideoFile] = useState<File | null>(null)
  const [videoUrl, setVideoUrl] = useState<string | null>(null)
  const [isPlaying, setIsPlaying] = useState(false)
  const [videoDuration, setVideoDuration] = useState<number | null>(null)
  const [detectionInfo, setDetectionInfo] = useState<{
    name: string
    description: string
    createdAt: string
  } | null>(null)
  const [isProcessing, setIsProcessing] = useState(false)
  const [progress, setProgress] = useState(0)
  const [progressInterval, setProgressInterval] = useState<NodeJS.Timeout | null>(null)
  const [processingMessage, setProcessingMessage] = useState('')
  const [isComplete, setIsComplete] = useState(false)
  const [detectionId, setDetectionId] = useState<string>('')

  // 加载检测信息
  useEffect(() => {
    const info = localStorage.getItem('pending_detection_info')
    if (!info) {
      toast({
        title: '未找到检测信息',
        description: '请返回工作台填写检测信息',
        variant: 'destructive',
      })
      router.push('/')
      return
    }
    setDetectionInfo(JSON.parse(info))
  }, [router])

  // 清理视频URL
  useEffect(() => {
    return () => {
      if (videoUrl) {
        URL.revokeObjectURL(videoUrl)
      }
    }
  }, [videoUrl])

  // 处理进度条
  const startProgress = () => {
    setIsProcessing(true)
    setProgress(0)
    setIsComplete(false)
    
    // 获取当前场景的处理信息
    const sceneConfig = DETECTION_SCENES.find(s => s.id === selectedScene)
    const sceneName = sceneConfig?.name || '未知场景'
    
    // 设置初始处理消息
    setProcessingMessage(`正在准备${sceneName}的检测环境...`)
    
    let isDetectionPhase = false
    const interval = setInterval(() => {
      setProgress(prev => {
        let newProgress
        if (!isDetectionPhase) {
          // 准备阶段：快速达到30%
          newProgress = Math.min(prev + 0.02, 0.3)
          if (newProgress >= 0.3) {
            isDetectionPhase = true
          }
        } else {
          // 检测阶段：缓慢达到100%
          newProgress = Math.min(prev + 0.005, 1)
        }
        
        // 根据进度更新处理消息
        if (newProgress < 0.1) {
          setProcessingMessage(`正在准备${sceneName}的检测环境...`)
        } else if (newProgress < 0.2) {
          setProcessingMessage(`正在加载${sceneName}的检测模型...`)
        } else if (newProgress < 0.3) {
          setProcessingMessage(`正在初始化${sceneName}的检测参数...`)
        } else if (newProgress < 0.5) {
          setProcessingMessage(`正在分析视频中的表情特征...`)
        } else if (newProgress < 0.7) {
          setProcessingMessage(`正在识别${sceneName}中的情绪变化...`)
        } else if (newProgress < 0.9) {
          setProcessingMessage(`正在生成情绪分析报告...`)
        } else if (newProgress < 1) {
          setProcessingMessage(`正在完成最后的处理...`)
        } else {
          setProcessingMessage(`检测完成！正在跳转...`)
          setIsComplete(true)
          clearInterval(interval)
          setProgressInterval(null)
        }
        
        return newProgress
      })
    }, 100)

    setProgressInterval(interval)
  }

  // 监听完成状态变化
  useEffect(() => {
    if (isComplete) {
      const timer = setTimeout(() => {
        router.push(`/detection/${detectionId}?scene=${selectedScene}&mode=video`)
      }, 500)
      return () => clearTimeout(timer)
    }
  }, [isComplete, router, selectedScene])

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0]
    if (file) {
      if (!file.type.startsWith('video/')) {
        toast({
          title: '文件类型错误',
          description: '请选择视频文件',
          variant: 'destructive',
        })
        return
      }

      // 检查文件大小（限制为100MB）
      if (file.size > 100 * 1024 * 1024) {
        toast({
          title: '文件太大',
          description: '视频文件大小不能超过100MB',
          variant: 'destructive',
        })
        return
      }

      setVideoFile(file)
      // 创建视频预览URL
      const url = URL.createObjectURL(file)
      setVideoUrl(url)
      
      // 创建一个临时的视频元素来获取持续时间
      const video = document.createElement('video')
      video.src = url
      video.onloadedmetadata = () => {
        setVideoDuration(video.duration)
      }
    }
  }

  const handleStartDetection = async () => {
    if (!selectedScene) {
      toast({
        title: '请选择场景',
        description: '在开始检测之前，请先选择一个检测场景',
        variant: 'destructive',
      })
      return
    }

    if (!videoFile) {
      toast({
        title: '请选择视频',
        description: '在开始检测之前，请先上传一个视频',
        variant: 'destructive',
      })
      return
    }

    // 获取选中场景的模型
    const selectedSceneConfig = DETECTION_SCENES.find(s => s.id === selectedScene)
    if (!selectedSceneConfig) {
      toast({
        title: '场景配置错误',
        description: '无法获取场景配置信息',
        variant: 'destructive',
      })
      return
    }

    // 生成检测ID
    const newDetectionId = Math.random().toString(36).substring(7)
    setDetectionId(newDetectionId)

    try {
      // 创建一个简化版的检测信息对象
      const detectionMetadata = {
        ...detectionInfo,
        scene: selectedScene,
        model: {
          type: selectedSceneConfig.models.video.type,
          version: selectedSceneConfig.models.video.version
        },
        mode: 'video',
        fileName: videoFile.name,
        timestamp: new Date().toISOString(),
        fileSize: videoFile.size,
        fileType: videoFile.type,
        ...(videoDuration ? { duration: videoDuration } : {}),
        previewUrl: videoUrl
      }
      
      // 存储元数据信息
      localStorage.setItem(
        `detection_${newDetectionId}_info`,
        JSON.stringify(detectionMetadata)
      )
      
      // 清除临时信息
      localStorage.removeItem('pending_detection_info')
      
      // 开始进度条
      startProgress()

      // 滚动到进度条位置
      setTimeout(() => {
        const progressElement = document.querySelector('.progress-bar-container')
        if (progressElement) {
          progressElement.scrollIntoView({ behavior: 'smooth', block: 'center' })
        }
      }, 100)
    } catch (error) {
      console.error('Error saving detection data:', error)
      toast({
        title: '保存失败',
        description: '无法保存检测数据，请尝试使用较小的视频文件',
        variant: 'destructive',
      })
    }
  }

  const togglePlay = () => {
    const video = document.querySelector('video')
    if (video) {
      if (video.paused) {
        video.play()
        setIsPlaying(true)
      } else {
        video.pause()
        setIsPlaying(false)
      }
    }
  }

  const handleVideoEnded = () => {
    setIsPlaying(false)
  }

  const resetVideo = () => {
    const video = document.querySelector('video')
    if (video) {
      video.currentTime = 0
      setIsPlaying(false)
    }
  }

  return (
    <div className="min-h-[calc(100vh-4rem)] p-6 flex flex-col">
      {/* 页面头部 */}
      <div className="flex items-center mb-8">
        <Link href="/" className="mr-4 transition-transform hover:scale-105">
          <Button variant="outline" size="icon" className="shadow-sm">
            <ArrowLeft className="h-4 w-4" />
          </Button>
        </Link>
        <div className="flex-1">
          <h1 className="text-2xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
            视频检测
          </h1>
          {detectionInfo && (
            <p className="text-sm text-gray-600 mt-1">
              {detectionInfo.name}
            </p>
          )}
        </div>
      </div>

      {/* 主要内容区域 */}
      <div className="max-w-3xl mx-auto w-full">
        {/* 步骤指引 */}
        <div className="flex justify-between items-center mb-8 px-4">
          <div className="flex items-center">
            <div
              className={`w-10 h-10 rounded-full flex items-center justify-center ${
                !selectedScene ? 'bg-blue-100 text-blue-600' : 'bg-green-100 text-green-600'
              }`}
            >
              1
            </div>
            <span className="ml-3 font-medium">选择场景</span>
          </div>
          <div className="flex-1 mx-6 h-0.5 bg-gray-200" />
          <div className="flex items-center">
            <div
              className={`w-10 h-10 rounded-full flex items-center justify-center ${
                !selectedScene
                  ? 'bg-gray-100 text-gray-400'
                  : !videoFile
                    ? 'bg-blue-100 text-blue-600'
                    : 'bg-green-100 text-green-600'
              }`}
            >
              2
            </div>
            <span className="ml-3 font-medium">上传视频</span>
          </div>
        </div>

        {/* 场景选择卡片 */}
        <Card className="shadow-md hover:shadow-lg transition-shadow mb-6">
          <CardHeader className="pb-3">
            <CardTitle className="text-lg flex items-center">
              <span className="w-7 h-7 rounded-full bg-blue-100 text-blue-600 text-sm flex items-center justify-center mr-3">
                1
              </span>
              选择场景
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <Select value={selectedScene} onValueChange={setSelectedScene}>
                <SelectTrigger id="scene" className="h-12">
                  <SelectValue placeholder="选择检测场景" />
                </SelectTrigger>
                <SelectContent>
                  {DETECTION_SCENES.map(scene => (
                    <SelectItem key={scene.id} value={scene.id} className="py-3">
                      {scene.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              {selectedScene ? (
                <p className="text-sm text-gray-600 bg-blue-50 p-4 rounded-lg">
                  <span className="font-medium block mb-1">场景说明</span>
                  {DETECTION_SCENES.find(s => s.id === selectedScene)?.description}
                </p>
              ) : (
                <p className="text-sm text-gray-500 bg-gray-50 p-4 rounded-lg">
                  请选择一个适合您需求的检测场景
                </p>
              )}
            </div>
          </CardContent>
        </Card>

        {/* 视频上传卡片 */}
        <Card className="shadow-md hover:shadow-lg transition-shadow mb-8">
          <CardHeader className="pb-3">
            <CardTitle className="text-lg flex items-center">
              <span className="w-7 h-7 rounded-full bg-blue-100 text-blue-600 text-sm flex items-center justify-center mr-3">
                2
              </span>
              上传视频
            </CardTitle>
          </CardHeader>
          <CardContent>
            {/* 文件信息 */}
            {videoFile && (
              <div className="bg-gray-50 p-4 rounded-lg text-sm text-gray-600 mb-6">
                <div className="font-medium mb-1">已选择文件</div>
                <div className="flex flex-col gap-1">
                  <p>文件名: {videoFile.name}</p>
                  <p>大小: {(videoFile.size / (1024 * 1024)).toFixed(2)} MB</p>
                  {videoDuration && <p>时长: {Math.floor(videoDuration / 60)}分{Math.floor(videoDuration % 60)}秒</p>}
                </div>
              </div>
            )}

            {/* 进度条 */}
            {isProcessing && (
              <div className="bg-white rounded-lg shadow-md p-4 mb-6">
                <h2 className="text-xl font-semibold mb-3">正在处理</h2>
                <div className="w-full bg-gray-200 rounded-full h-2.5">
                  <div
                    className={`h-2.5 rounded-full transition-all duration-500 ease-out ${
                      isComplete ? 'bg-green-600' : 'bg-blue-600'
                    }`}
                    style={{ width: `${Math.round(progress * 100)}%` }}
                  ></div>
                </div>
                <p className={`text-sm mt-2 ${
                  isComplete ? 'text-green-600' : 'text-gray-500'
                }`}>
                  {processingMessage}
                </p>
              </div>
            )}

            {/* 上传区域 */}
            <div className="h-[400px] relative mb-6">
              {videoUrl ? (
                <div className="relative w-full h-full">
                  <video
                    src={videoUrl}
                    className="w-full h-full rounded-lg object-contain border-2 border-blue-200 p-4"
                    onEnded={handleVideoEnded}
                  />
                  <div 
                    className="absolute inset-x-0 bottom-0 p-4 bg-gradient-to-t from-black/50 to-transparent flex items-center justify-center gap-3"
                    onClick={e => e.stopPropagation()} // 阻止事件冒泡
                  >
                    <Button
                      variant="outline"
                      size="icon"
                      className="h-9 w-9 bg-white hover:bg-white/90"
                      onClick={togglePlay}
                    >
                      {isPlaying ? <Pause className="h-4 w-4" /> : <Play className="h-4 w-4" />}
                    </Button>
                    <Button
                      variant="outline"
                      size="icon"
                      className="h-9 w-9 bg-white hover:bg-white/90"
                      onClick={resetVideo}
                    >
                      <RotateCw className="h-4 w-4" />
                    </Button>
                  </div>
                  
                  {/* 更换视频按钮 - 单独处理 */}
                  <div 
                    className="absolute top-3 right-3"
                    onClick={e => e.stopPropagation()} // 阻止事件冒泡
                  >
                    <label
                      htmlFor="video-upload"
                      className="inline-flex items-center gap-1.5 rounded-md bg-white/90 hover:bg-white px-3 py-1.5 text-sm font-medium text-gray-700 shadow-sm hover:text-blue-600 cursor-pointer"
                    >
                      <Upload className="h-3.5 w-3.5" />
                      更换视频
                    </label>
                  </div>
                </div>
              ) : (
                <label
                  htmlFor="video-upload"
                  className={`absolute inset-0 flex flex-col items-center justify-center border-2 border-dashed rounded-lg cursor-pointer transition-colors ${
                    'bg-gray-50 hover:bg-gray-100'
                  } ${
                    !selectedScene ? 'opacity-50 cursor-not-allowed' : 'border-blue-200 hover:border-blue-300'
                  }`}
                >
                  <div className="flex flex-col items-center justify-center p-8">
                    <div className="w-20 h-20 rounded-full bg-blue-50 flex items-center justify-center mb-5">
                      <Upload className="w-10 h-10 text-blue-500" />
                    </div>
                    <p className="text-lg text-gray-600 mb-2">
                      <span className="font-semibold text-blue-600">点击上传</span> 或拖放视频
                    </p>
                    <p className="text-sm text-gray-500">
                      支持MP4、MOV、AVI格式，大小不超过100MB
                    </p>
                  </div>
                </label>
              )}
              
              <Input
                id="video-upload"
                type="file"
                accept="video/*"
                className="hidden"
                onChange={handleFileChange}
                disabled={!selectedScene}
              />
            </div>

            {/* 操作按钮 */}
            <Button
              className="w-full h-14 text-base font-medium shadow-md flex items-center justify-center gap-2"
              size="lg"
              onClick={handleStartDetection}
              disabled={!videoFile || !selectedScene}
            >
              下一步
            </Button>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
