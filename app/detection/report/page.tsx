'use client'

import { useEffect, useState } from 'react'
import { useRouter, useSearchParams } from 'next/navigation'
import { format } from 'date-fns'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { AlertCircle, ChevronLeft, Download, Loader2, Printer } from 'lucide-react'

// 导入报告组件
import { BasicInfo } from './components/basic-info'
import { DetectionOverview } from './components/detection-overview'
import { EventTable } from './components/event-table'

export default function ReportPage() {
  const router = useRouter()
  const searchParams = useSearchParams()
  const recordId = searchParams.get('id')

  const [isLoading, setIsLoading] = useState(false)
  const [isReportLoading, setIsReportLoading] = useState(true)
  const [reportData, setReportData] = useState<any>(null)
  const [error, setError] = useState<string | null>(null)

  // 加载特定记录的报告数据
  useEffect(() => {
    const loadReportData = async () => {
      setIsReportLoading(true)

      try {
        // 实际项目中，这里会从API获取数据
        // 这里使用模拟数据
        await new Promise(resolve => setTimeout(resolve, 1200)) // 模拟加载时间

        // 模拟检测数据
        const data = {
          id: recordId || 'DET-58742',
          name: `安全会议—嫌疑人微表情分析 ${recordId ? `(${recordId})` : ''}`,
          date: new Date(),
          duration: '00:32:15',
          operator: '李国强（技术部）',
          method: '视频微表情分析',
          subject: '陈某（嫌疑人）',
          confidenceLevel: 'medium',
          detectionSummary: {
            totalEvents: 28,
            significantEvents: 12,
            neutralExpressions: 65,
            negativeExpressions: 23,
            positiveExpressions: 12,
            concernPoints: 8,
            analysisConfidence: 87,
          },
          timelineData: [
            { time: 65, score: 0.2 },
            { time: 125, score: 0.7 },
            { time: 220, score: 0.5 },
            { time: 350, score: 0.8 },
            { time: 460, score: 0.3 },
            { time: 580, score: 0.9 },
            { time: 720, score: 0.4 },
            { time: 850, score: 0.6 },
            { time: 925, score: 0.2 },
            { time: 1050, score: 0.7 },
            { time: 1180, score: 0.85 },
            { time: 1250, score: 0.3 },
          ],
        }

        setReportData(data)
        setError(null)
      } catch (err) {
        console.error('Error loading report data:', err)
        setError('无法加载报告数据，请稍后重试')
      } finally {
        setIsReportLoading(false)
      }
    }

    loadReportData()
  }, [recordId])

  const handlePrint = () => {
    setIsLoading(true)
    setTimeout(() => {
      window.print()
      setIsLoading(false)
    }, 500)
  }

  const handleExport = () => {
    setIsLoading(true)
    // 模拟导出操作
    setTimeout(() => {
      setIsLoading(false)
      alert('报告已导出为PDF格式并保存至文档管理系统')
    }, 1500)
  }

  // 加载状态
  if (isReportLoading) {
    return (
      <div className="min-h-screen flex flex-col items-center justify-center bg-gray-50">
        <Loader2 className="w-12 h-12 text-blue-600 animate-spin mb-4" />
        <h2 className="text-lg font-medium text-gray-700">正在加载报告数据...</h2>
        <p className="text-sm text-gray-500 mt-2">请稍候，系统正在处理您的请求</p>
      </div>
    )
  }

  // 错误状态
  if (error || !reportData) {
    return (
      <div className="min-h-screen flex flex-col items-center justify-center bg-gray-50">
        <AlertCircle className="w-12 h-12 text-red-600 mb-4" />
        <h2 className="text-lg font-medium text-gray-700">无法加载报告</h2>
        <p className="text-sm text-gray-500 mt-2">{error || '报告数据不存在'}</p>
        <Button variant="outline" className="mt-6" onClick={() => router.back()}>
          返回上一页
        </Button>
      </div>
    )
  }

  return (
    <div className="bg-gray-50 min-h-screen pb-12 print:bg-white print:pb-0">
      {/* 顶部导航栏 - 打印时隐藏 */}
      <div className="bg-white shadow-sm print:hidden">
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-4">
            <div className="flex items-center space-x-4">
              <h1 className="text-xl font-semibold text-gray-900">微表情检测报告</h1>
              {recordId && (
                <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200">
                  ID: {recordId}
                </Badge>
              )}
            </div>
            <div className="flex items-center space-x-3">
              <Button
                variant="outline"
                className="flex items-center space-x-2"
                onClick={handleExport}
                disabled={isLoading}
              >
                <Download className="h-4 w-4" />
                <span>{isLoading ? '处理中...' : '导出报告'}</span>
              </Button>
              <Button
                variant="default"
                className="flex items-center space-x-2"
                onClick={handlePrint}
                disabled={isLoading}
              >
                <Printer className="h-4 w-4" />
                <span>{isLoading ? '准备中...' : '打印报告'}</span>
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* 报告内容 */}
      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 pt-8 print:pt-0 space-y-8 print:space-y-6">
        {/* 报告头部信息 */}
        <div className="flex justify-between items-start">
          <div>
            <div className="flex items-center">
              <h1 className="text-2xl font-bold text-gray-900">{reportData.name}</h1>
              <Badge
                variant="outline"
                className="ml-3 px-3 py-1 text-sm bg-yellow-50 text-yellow-700 border-yellow-200 rounded-lg"
              >
                机密文件
              </Badge>
            </div>
            <p className="text-gray-500 mt-2">
              报告编号: {reportData.id} · 生成时间: {format(new Date(), 'yyyy-MM-dd HH:mm:ss')}
            </p>
          </div>
        </div>

        {/* 打印版本的水印提示 - 仅在打印时显示 */}
        <div className="hidden print:block absolute top-0 left-0 w-full h-full pointer-events-none">
          <div className="absolute top-1/3 left-1/2 transform -translate-x-1/2 -translate-y-1/2 rotate-45 text-gray-200 text-9xl font-bold opacity-10">
            机密文件
          </div>
        </div>

        {/* 报告主体 - 将分为多个独立组件 */}
        <div className="space-y-8 print:space-y-6">
          {/* 1. 基本信息 */}
          <BasicInfo data={reportData} />

          {/* 2. 检测概览 */}
          <DetectionOverview data={reportData} />

          {/* 3. 事件记录表格 */}
          <EventTable data={reportData} />

          {/* 4. 结论与建议 */}
          {/*<Conclusion data={reportData} />*/}

          {/* 5. 附录 */}
          {/*<Appendix data={reportData} />*/}
        </div>

        {/* 报告尾部 */}
        <div className="mt-12 pt-6 border-t border-gray-200 text-center print:mt-8 print:pt-4">
          <p className="text-sm text-gray-500">
            本报告由微表情识别系统自动生成 © {new Date().getFullYear()}
          </p>
          <p className="text-xs text-gray-400 mt-1">
            报告完成于 {format(new Date(), 'yyyy年MM月dd日 HH:mm')}，如有疑问请联系系统管理员
          </p>
        </div>
      </div>
    </div>
  )
}
