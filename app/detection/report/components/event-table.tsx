'use client'

import { useEffect, useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import { Badge } from '@/components/ui/badge'
import { X } from 'lucide-react'

// 不使用MicroExpressionEvent接口更新事件对象
interface EventTableProps {
  data: any
}

// 生成随机的上下文描述
function generateRandomContext(emotion: string) {
  const topics = [
    '讨论嫌疑人行为',
    '提及受害人',
    '询问案件细节',
    '提及不在场证明',
    '谈论案发现场',
    '提及关键证据',
    '询问时间线',
    '谈论作案动机',
    '提及目击证人',
    '询问社会关系',
  ]

  const reactions: Record<string, string[]> = {
    生气: ['面部肌肉紧绷', '眉头突然紧锁', '嘴角下压'],
    厌恶: ['上唇轻微抬起', '鼻子微皱', '下巴略微抬起'],
    恐惧: ['眉毛上扬', '眼睛睁大', '嘴角微颤'],
    高兴: ['嘴角上扬', '眼睛微眯', '面部放松'],
    悲伤: ['眉头内收下垂', '嘴角下拉', '面部松弛'],
    惊讶: ['双眉上扬', '眼睛睁大', '嘴部微张'],
  }

  const topicIndex = Math.floor(Math.random() * topics.length)
  const defaultReactions = ['面部表情变化', '微表情明显', '情绪波动']
  const reactionList = reactions[emotion] || defaultReactions
  const reactionIndex = Math.floor(Math.random() * reactionList.length)

  return `${topics[topicIndex]}时，${reactionList[reactionIndex]}`
}

// 生成表情解读文字
function generateInterpretation(emotion: string, intensity: string, context: string) {
  const interpretations: Record<string, string[]> = {
    生气: [
      '表现出明显的情绪抵触，可能与问题内容直接相关',
      '对特定话题表现出防御性反应，暗示敏感点被触及',
      '短暂但强烈的情绪波动，可能是真实反应而非伪装',
    ],
    厌恶: [
      '表现出对提及内容的明显反感，可能涉及个人道德底线',
      '下意识流露出拒绝态度，暗示与事件可能有不愿提及的联系',
      '面部微表情显示对话题的潜在抵触，值得深入探讨',
    ],
    恐惧: [
      '表现出非典型性恐惧反应，可能与案件关键细节有关',
      '面部短暂呈现恐惧模式，暗示可能隐藏重要信息',
      '对特定问题的恐惧反应强烈，建议进一步调查相关内容',
    ],
    高兴: [
      '在谈及敏感话题时出现不协调的微笑，可能是掩饰行为',
      '表现出与情境不符的积极情绪，疑为紧张状态下的自我调节',
      '情绪表现与话题不匹配，显示可能存在心理防御机制',
    ],
    悲伤: [
      '表现出真实的情感波动，可能与事件有深层次联系',
      '悲伤微表情暗示内心罪恶感或遗憾情绪',
      '情绪流露自然且短暂，考虑为真实反应而非表演',
    ],
    惊讶: [
      '对理应知晓的信息表现出惊讶，可能是装作不知情',
      '惊讶反应时机与强度均不自然，建议关注相关话题',
      '面部表情与言语内容不一致，显示可能隐瞒信息',
    ],
  }

  const intensityModifiers: Record<string, string[]> = {
    high: ['显著', '强烈', '明确', '毫无掩饰的'],
    medium: ['中等程度', '明显', '清晰可见的', '相当程度的'],
    low: ['轻微', '短暂', '细微', '若隐若现的'],
  }

  // 获取对应情绪的解读列表
  const emotionInterpretations = interpretations[emotion] || [
    '表情变化可能暗示内心真实想法',
    '微表情流露出与叙述不一致的情绪状态',
    '面部表情与谈话内容存在矛盾，值得注意',
  ]

  // 获取强度修饰词
  const intensityWords = intensityModifiers[intensity] || ['一定程度的']

  // 随机选择一个解读和强度修饰词
  const randomInterpretation =
    emotionInterpretations[Math.floor(Math.random() * emotionInterpretations.length)]
  const randomIntensity = intensityWords[Math.floor(Math.random() * intensityWords.length)]

  return `表现出${randomIntensity}${emotion}情绪。${randomInterpretation}。`
}

// 图片查看模态框组件
function ImageModal({
  isOpen,
  imageUrl,
  onClose,
}: {
  isOpen: boolean
  imageUrl: string
  onClose: () => void
}) {
  if (!isOpen) return null

  return (
    <div
      className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4"
      onClick={onClose}
    >
      <div className="relative bg-white rounded-lg p-2 max-w-3xl max-h-[90vh] overflow-hidden">
        <button
          onClick={onClose}
          className="absolute right-2 top-2 bg-gray-100 rounded-full p-1 hover:bg-gray-200 transition-colors"
          aria-label="关闭"
        >
          <X size={20} />
        </button>
        <img
          src={imageUrl.replace('/80/80', '/400/400')}
          alt="表情大图"
          className="max-w-full max-h-[80vh] object-contain"
        />
      </div>
    </div>
  )
}

// 生成随机缩略图URL
function generateRandomThumbnail() {
  // 使用随机图像生成服务
  const imageIds = [
    '237',
    '238',
    '239',
    '240',
    '241',
    '242',
    '243',
    '244',
    '247',
    '248',
    '249',
    '250',
    '251',
    '252',
    '253',
    '254',
    '256',
    '257',
    '258',
    '259',
  ]
  const randomId = imageIds[Math.floor(Math.random() * imageIds.length)]
  // 使用Picsum Photos生成不同的人脸图像
  return `https://picsum.photos/seed/${randomId}/80/80`
}

// 生成模拟的微表情事件数据
const generateMicroExpressionEvents = () => {
  const emotions = [
    { name: '生气', level: 'high', color: 'red' },
    { name: '厌恶', level: 'medium', color: 'amber' },
    { name: '恐惧', level: 'high', color: 'red' },
    { name: '高兴', level: 'low', color: 'green' },
    { name: '悲伤', level: 'medium', color: 'amber' },
    { name: '惊讶', level: 'medium', color: 'amber' },
  ]

  const events = []

  for (let i = 1; i <= 15; i++) {
    const emotionIndex = Math.floor(Math.random() * emotions.length)
    const emotion = emotions[emotionIndex]
    const minutes = Math.floor(Math.random() * 30)
    const seconds = Math.floor(Math.random() * 60)
    const duration = (Math.random() * 0.8 + 0.2).toFixed(2)
    const confidence = Math.floor(Math.random() * 30 + 70)
    const context = generateRandomContext(emotion.name)

    events.push({
      id: i,
      timestamp: `${String(minutes).padStart(2, '0')}:${String(seconds).padStart(2, '0')}`,
      duration: `${duration}s`,
      emotion: emotion.name,
      intensity: emotion.level,
      confidence: `${confidence}%`,
      context: context,
      significant: Math.random() > 0.6,
      color: emotion.color,
      interpretation: generateInterpretation(emotion.name, emotion.level, context),
      thumbnail: generateRandomThumbnail(), // 添加缩略图URL
    })
  }

  // 按时间戳排序
  return events.sort((a, b) => {
    const [aMin, aSec] = a.timestamp.split(':').map(Number)
    const [bMin, bSec] = b.timestamp.split(':').map(Number)
    const aTime = aMin * 60 + aSec
    const bTime = bMin * 60 + bSec
    return aTime - bTime
  })
}

export function EventTable({ data }: EventTableProps) {
  const [searchTerm, setSearchTerm] = useState('')
  const [events, setEvents] = useState<any[]>([])
  const [modalOpen, setModalOpen] = useState(false)
  const [selectedImage, setSelectedImage] = useState('')

  // 打开图片查看器
  const openImageViewer = (imageUrl: string) => {
    setSelectedImage(imageUrl)
    setModalOpen(true)
  }

  // 关闭图片查看器
  const closeImageViewer = () => {
    setModalOpen(false)
  }

  // 初始化事件数据
  useEffect(() => {
    const generatedEvents = generateMicroExpressionEvents()
    setEvents(generatedEvents)
  }, [])

  // 过滤事件数据
  const filteredEvents = events.filter(
    event =>
      event.emotion.includes(searchTerm) ||
      event.context.includes(searchTerm) ||
      (event.interpretation && event.interpretation.includes(searchTerm))
  )

  // 获取强度标签的样式
  const getIntensityBadgeStyle = (intensity: string) => {
    // 更改为现代设计风格
    switch (intensity) {
      case 'high':
        return 'bg-purple-100 text-purple-800 border-purple-300 hover:bg-purple-200 font-medium'
      case 'medium':
        return 'bg-indigo-100 text-indigo-800 border-indigo-300 hover:bg-indigo-200 font-medium'
      case 'low':
        return 'bg-violet-100 text-violet-800 border-violet-300 hover:bg-violet-200 font-medium'
      default:
        return 'bg-gray-100 text-gray-800 border-gray-300 hover:bg-gray-200 font-medium'
    }
  }

  // 获取情绪类型标签样式
  const getEmotionBadgeStyle = (color: string) => {
    // 更改为现代设计风格
    switch (color) {
      case 'red':
        return 'bg-rose-100 text-rose-800 border-rose-300 font-medium'
      case 'amber':
        return 'bg-amber-100 text-amber-800 border-amber-300 font-medium'
      case 'green':
        return 'bg-emerald-100 text-emerald-800 border-emerald-300 font-medium'
      default:
        return 'bg-gray-100 text-gray-800 border-gray-300 font-medium'
    }
  }

  // 格式化时间点为更易读的形式
  const formatTimestamp = (timestamp: string) => {
    const [minutes, seconds] = timestamp.split(':')
    return (
      <div className="flex items-center ">
        <span className="font-medium text-gray-800 text-xs ">
          {minutes}分{seconds}秒
        </span>
      </div>
    )
  }

  return (
    <Card className="shadow-md border-gray-200">
      <CardHeader className="pb-3 border-b border-gray-200 bg-gray-50">
        <div className="flex justify-between items-start">
          <div>
            <CardTitle className="text-lg font-semibold text-gray-900">三、事件记录</CardTitle>
            <CardDescription className="text-gray-600 mt-1">
              下表记录了检测过程中识别到的微表情事件，按时间顺序排列。
            </CardDescription>
          </div>
        </div>
      </CardHeader>
      <CardContent className="p-0">
        {/* 表格 */}
        <div className="overflow-x-auto">
          <Table>
            <TableHeader className="bg-gray-100/80">
              <TableRow className="border-gray-300">
                <TableHead className="w-[50px] text-center font-semibold text-gray-700">
                  序号
                </TableHead>
                <TableHead className="w-[80px] font-semibold text-gray-700">缩略图</TableHead>
                <TableHead className="w-[80px] font-semibold text-gray-700">时间点</TableHead>
                <TableHead className="w-[100px] font-semibold text-gray-700">持续时间</TableHead>
                <TableHead className="w-[100px] font-semibold text-gray-700">表情类型</TableHead>
                <TableHead className="w-[70px] font-semibold text-gray-700">置信度</TableHead>
                <TableHead className="w-[220px] font-semibold text-gray-700">表情解读</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredEvents.length === 0 ? (
                <TableRow className="border-gray-200">
                  <TableCell colSpan={10} className="h-24 text-center text-gray-500 bg-gray-50/30">
                    未找到匹配的事件记录
                  </TableCell>
                </TableRow>
              ) : (
                filteredEvents.map((event, index) => (
                  <TableRow key={event.id}>
                    <TableCell className="text-center font-medium text-gray-800">
                      {index + 1}
                    </TableCell>
                    <TableCell className="p-1">
                      <img
                        src={event.thumbnail}
                        alt={`时间点${event.timestamp}的表情截图`}
                        className="rounded-sm border border-gray-200 shadow-sm cursor-pointer hover:opacity-90 transition-opacity"
                        width={80}
                        height={60}
                        onClick={() => openImageViewer(event.thumbnail)}
                      />
                    </TableCell>
                    <TableCell className="text-gray-800 relative">
                      {formatTimestamp(event.timestamp)}
                    </TableCell>
                    <TableCell className="text-gray-800">
                      <div className="flex items-center">
                        <div className="w-1 h-6 bg-indigo-400 opacity-70 rounded mr-1.5"></div>
                        <span>{event.duration}</span>
                      </div>
                    </TableCell>
                    <TableCell>
                      <Badge variant="outline" className={getEmotionBadgeStyle(event.color)}>
                        {event.emotion}
                      </Badge>
                    </TableCell>
                    <TableCell className="text-gray-800 font-medium">
                      <Badge variant="outline" className={getIntensityBadgeStyle(event.intensity)}>
                        {event.confidence}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <div className="relative">{event.interpretation || ''}</div>
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </div>

        {/* 图片查看模态框 */}
        <ImageModal isOpen={modalOpen} imageUrl={selectedImage} onClose={closeImageViewer} />
      </CardContent>
    </Card>
  )
}
