'use client'

import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { AlertCircle, CheckCircle, AlertTriangle, Info } from 'lucide-react'

interface ConclusionProps {
  data: any
}

export function Conclusion({ data }: ConclusionProps) {
  // 生成模拟的结论和建议数据
  const conclusionData = {
    summary: "基于微表情分析结果，被检测对象在讨论案件关键细节时展现出显著的情绪波动，特别是在谈及案发现场和不在场证明时，多次出现短暂的恐惧、蔑视和不安表情。综合分析表明，被检测对象在特定话题上可能存在刻意掩饰的情况，建议进一步深入调查。",
    
    keyFindings: [
      {
        type: 'caution',
        content: '在描述案发时间线时，检测到强烈的恐惧微表情，与口头陈述内容形成明显反差',
        importance: 'high'
      },
      {
        type: 'alert',
        content: '提及关键证据时，出现多次蔑视表情，表明可能对相关证据有所隐瞒或轻视',
        importance: 'high'
      },
      {
        type: 'info',
        content: '谈论社会关系时情绪波动较小，微表情与口头陈述基本一致',
        importance: 'medium'
      },
      {
        type: 'caution',
        content: '在被询问不在场证明时，出现明显的不安和紧张微表情',
        importance: 'medium'
      },
      {
        type: 'positive',
        content: '部分陈述中情绪反应自然，未检测到特殊微表情',
        importance: 'low'
      }
    ],
    
    recommendations: [
      '建议对案发时间线和不在场证明进行重点核查',
      '在后续询问中，针对检测到异常微表情的话题进行更深入探讨',
      '结合其他证据链，对被检测对象的关键陈述进行交叉验证',
      '考虑使用间接提问方式，观察被检测对象在不同情境下的微表情变化',
      '建议对检测到的高强度微表情时间点的视频片段进行专家复核'
    ]
  }
  
  // 获取图标
  const getIcon = (type: string) => {
    switch (type) {
      case 'caution':
        return <AlertCircle className="h-5 w-5 text-amber-500" />
      case 'alert':
        return <AlertTriangle className="h-5 w-5 text-red-500" />
      case 'positive':
        return <CheckCircle className="h-5 w-5 text-green-500" />
      case 'info':
      default:
        return <Info className="h-5 w-5 text-blue-500" />
    }
  }
  
  // 获取背景色
  const getBgColor = (type: string) => {
    switch (type) {
      case 'caution':
        return 'bg-amber-50'
      case 'alert':
        return 'bg-red-50'
      case 'positive':
        return 'bg-green-50'
      case 'info':
      default:
        return 'bg-blue-50'
    }
  }

  return (
    <Card className="shadow-sm hover:shadow-md transition-shadow duration-300">
      <CardHeader className="pb-3 border-b border-gray-100 bg-gray-50">
        <CardTitle className="text-lg font-semibold text-gray-800">
          四、结论与建议
        </CardTitle>
      </CardHeader>
      <CardContent className="pt-5 space-y-6">
        {/* 总结性结论 */}
        <div className="p-4 border border-blue-100 rounded-lg bg-blue-50 text-blue-800">
          <p className="text-sm leading-relaxed">{conclusionData.summary}</p>
        </div>
        
        {/* 关键发现 */}
        <div className="space-y-4">
          <h3 className="text-md font-medium text-gray-800 border-b pb-2">关键发现</h3>
          <div className="space-y-3">
            {conclusionData.keyFindings.map((finding, index) => (
              <div 
                key={index} 
                className={`flex items-start space-x-3 p-3 rounded-md ${getBgColor(finding.type)} ${
                  finding.importance === 'high' ? 'ring-1 ring-inset ring-red-200' : ''
                }`}
              >
                <div className="flex-shrink-0 mt-0.5">
                  {getIcon(finding.type)}
                </div>
                <div className="flex-1">
                  <p className="text-sm text-gray-800">{finding.content}</p>
                </div>
              </div>
            ))}
          </div>
        </div>
        
        {/* 建议 */}
        <div className="space-y-4">
          <h3 className="text-md font-medium text-gray-800 border-b pb-2">行动建议</h3>
          <div className="space-y-2 pl-2">
            {conclusionData.recommendations.map((recommendation, index) => (
              <div key={index} className="flex items-start space-x-3 py-1">
                <div className="flex-shrink-0 w-5 h-5 rounded-full bg-purple-100 text-purple-800 flex items-center justify-center text-xs font-medium">
                  {index + 1}
                </div>
                <p className="text-sm text-gray-700 flex-1 pt-0.5">{recommendation}</p>
              </div>
            ))}
          </div>
        </div>
        
        {/* 专业提示 */}
        <div className="mt-6 bg-gray-50 border-l-4 border-gray-300 p-4 rounded-r-md">
          <p className="text-sm text-gray-600 italic">
            请注意：微表情分析为辅助手段，结论仅供参考，应结合其他调查手段和证据综合判断。微表情可能受多种因素影响，单一表情不足以确定被测对象的内在状态或意图。
          </p>
        </div>
      </CardContent>
    </Card>
  )
} 