'use client'

import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { 
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion"
import { ExternalLink, BookOpen, FileText } from 'lucide-react'

interface AppendixProps {
  data: any
}

export function Appendix({ data }: AppendixProps) {
  // 模拟术语表
  const glossary = [
    {
      term: '微表情',
      definition: '微表情是一种短暂的、非自主的面部表情，通常持续时间为0.04-0.5秒，反映了人们试图隐藏的真实情感。微表情的出现往往表明个体内心真实情绪与其试图表达的情绪之间存在冲突。'
    },
    {
      term: '基本情绪',
      definition: '人类的七种基本情绪：愤怒、厌恶、恐惧、高兴、悲伤、惊讶和蔑视。这些情绪在不同文化中表达方式基本一致，被认为是人类进化过程中形成的普遍情感反应。'
    },
    {
      term: '情绪泄漏',
      definition: '情绪泄漏指的是个体在试图掩饰或控制情绪时，真实情感通过微表情、身体语言等非语言线索不自觉地表露出来的现象。'
    },
    {
      term: '面部动作编码系统(FACS)',
      definition: '面部动作编码系统是一种用于分类人类面部表情的系统，通过识别和编码面部肌肉活动的组合来分析表情。FACS将面部动作分解为多个动作单元(AUs)，可以精确描述各种表情。'
    },
    {
      term: '表情分析置信度',
      definition: '表情分析置信度反映了系统对检测结果的确信程度，受多种因素影响，如图像质量、面部角度、光线条件等。高置信度表示系统对结果有较高把握，但仍需专业人员进行判断。'
    },
  ]
  
  // 模拟参考资料
  const references = [
    {
      title: '微表情识别在刑事侦查中的应用研究',
      authors: '张明, 李红, 王志强',
      publication: '公安学报',
      year: '2022',
      url: '#'
    },
    {
      title: '表情心理学：解码人类情感的奥秘',
      authors: 'Paul Ekman',
      publication: '心理科学出版社',
      year: '2018',
      url: '#'
    },
    {
      title: '人工智能在微表情分析中的应用进展',
      authors: '刘伟, 陈静, 赵芳',
      publication: '计算机视觉与模式识别',
      year: '2023',
      url: '#'
    },
    {
      title: '司法心理学视角下的非语言行为分析技术',
      authors: '郑建国, 马丽',
      publication: '法律科学',
      year: '2021',
      url: '#'
    },
  ]

  return (
    <Card className="shadow-sm hover:shadow-md transition-shadow duration-300">
      <CardHeader className="pb-3 border-b border-gray-100 bg-gray-50">
        <CardTitle className="text-lg font-semibold text-gray-800">
          五、附录
        </CardTitle>
      </CardHeader>
      <CardContent className="pt-5 space-y-8">
        {/* 术语表 */}
        <div className="space-y-4">
          <h3 className="text-md font-medium text-gray-800 flex items-center">
            <BookOpen className="h-4 w-4 mr-2 text-gray-500" />
            术语解释
          </h3>
          
          <Accordion type="single" collapsible className="w-full">
            {glossary.map((item, index) => (
              <AccordionItem key={index} value={`item-${index}`}>
                <AccordionTrigger className="text-sm font-medium text-gray-700 hover:text-blue-600 py-3">
                  {item.term}
                </AccordionTrigger>
                <AccordionContent className="text-sm text-gray-600 py-3 px-4 bg-gray-50 rounded-md">
                  {item.definition}
                </AccordionContent>
              </AccordionItem>
            ))}
          </Accordion>
        </div>
        
        {/* 参考资料 */}
        <div className="space-y-4">
          <h3 className="text-md font-medium text-gray-800 flex items-center">
            <FileText className="h-4 w-4 mr-2 text-gray-500" />
            参考资料
          </h3>
          
          <div className="space-y-4">
            {references.map((reference, index) => (
              <div key={index} className="text-sm border-b border-gray-100 pb-3 last:border-0 last:pb-0">
                <p className="font-medium text-gray-800">{reference.title}</p>
                <p className="text-gray-600 mt-1">
                  {reference.authors}. {reference.publication}, {reference.year}
                </p>
                <a 
                  href={reference.url} 
                  className="inline-flex items-center text-blue-600 hover:text-blue-800 mt-1 text-xs"
                >
                  查看原文
                  <ExternalLink className="h-3 w-3 ml-1" />
                </a>
              </div>
            ))}
          </div>
        </div>
        
        {/* 检测方法说明 */}
        <div className="p-4 bg-gray-50 rounded-lg border border-gray-200">
          <h3 className="text-sm font-medium text-gray-700 mb-2">检测方法说明</h3>
          <p className="text-sm text-gray-600">
            本报告采用多模态微表情检测技术，结合深度学习面部表情识别算法与传统FACS编码系统，对视频中被检测对象的面部微表情进行分析。
            系统通过高速摄像分析技术捕捉短暂的面部肌肉活动，并与标准情绪模型进行比对，提取情绪变化特征。
            检测过程中，系统会过滤正常表情变化，重点识别与语境不匹配或持续时间极短的异常表情，这些表情往往反映了个体试图隐藏的真实情绪。
          </p>
        </div>
      </CardContent>
    </Card>
  )
} 