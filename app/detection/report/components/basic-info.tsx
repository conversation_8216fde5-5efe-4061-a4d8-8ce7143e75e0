'use client'

import { format } from 'date-fns'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { User, Calendar, Clock, Tag } from 'lucide-react'

interface BasicInfoProps {
  data: {
    id: string
    name: string
    date: Date
    duration: string
    operator: string
    method: string
    subject: string
    confidenceLevel: string
  }
}

export function BasicInfo({ data }: BasicInfoProps) {
  // 信息项配置
  const infoItems = [
    {
      icon: <Calendar className="h-4 w-4 text-blue-500" />,
      label: '检测时间',
      value: format(data.date, 'yyyy-MM-dd HH:mm'),
    },
    {
      icon: <Clock className="h-4 w-4 text-blue-500" />,
      label: '检测时长',
      value: data.duration,
    },
    {
      icon: <User className="h-4 w-4 text-blue-500" />,
      label: '操作人员',
      value: data.operator,
    },
    {
      icon: <Tag className="h-4 w-4 text-blue-500" />,
      label: '检测方法',
      value: data.method,
    },
    {
      icon: <Tag className="h-4 w-4 text-blue-500" />,
      label: '置信度级别',
      value:
        {
          high: '高',
          medium: '中',
          low: '低',
        }[data.confidenceLevel] || data.confidenceLevel,
    },
  ]

  return (
    <Card className="shadow-sm hover:shadow-md transition-shadow duration-300">
      <CardHeader className="pb-3 border-b border-gray-100 bg-gray-50">
        <CardTitle className="text-lg font-semibold text-gray-800">一、检测基本信息</CardTitle>
      </CardHeader>
      <CardContent className="pt-5">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {infoItems.map((item, index) => (
            <div key={index} className="flex items-start space-x-3">
              <div className="mt-0.5 p-1.5 bg-blue-50 rounded-md">{item.icon}</div>
              <div>
                <p className="text-sm font-medium text-gray-500">{item.label}</p>
                <p className="text-base font-medium text-gray-900 mt-0.5">{item.value}</p>
              </div>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  )
}
