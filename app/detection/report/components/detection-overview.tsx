'use client'

import { <PERSON>, Card<PERSON>ontent, Card<PERSON>eader, CardTitle } from '@/components/ui/card'
import {
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
} from 'recharts'

interface DetectionOverviewProps {
  data: {
    detectionSummary: {
      totalEvents: number
      significantEvents: number
      neutralExpressions: number
      negativeExpressions: number
      positiveExpressions: number
      concernPoints: number
      analysisConfidence: number
    }
    timelineData: { time: number; score: number }[]
  }
}

export function DetectionOverview({ data }: DetectionOverviewProps) {
  const { detectionSummary } = data

  // 格式化时间轴数据
  const chartData = data.timelineData.map(point => ({
    time: formatSeconds(point.time),
    score: point.score,
    intensity: Math.round(point.score * 100),
  }))

  // 将秒数转换为时间格式
  function formatSeconds(seconds: number) {
    const minutes = Math.floor(seconds / 60)
    const remainingSeconds = Math.floor(seconds % 60)
    return `${String(minutes).padStart(2, '0')}:${String(remainingSeconds).padStart(2, '0')}`
  }

  // 概览数据项配置
  const summaryItems = [
    {
      label: '检测事件总数',
      value: detectionSummary.totalEvents,
      color: 'text-blue-600',
      bgColor: 'bg-blue-50',
    },
    {
      label: '中性表情占比',
      value: `${detectionSummary.neutralExpressions}%`,
      color: 'text-gray-600',
      bgColor: 'bg-gray-50',
    },
    {
      label: '负面表情占比',
      value: `${detectionSummary.negativeExpressions}%`,
      color: 'text-red-600',
      bgColor: 'bg-red-50',
    },
    {
      label: '正面表情占比',
      value: `${detectionSummary.positiveExpressions}%`,
      color: 'text-green-600',
      bgColor: 'bg-green-50',
    },
    {
      label: '关注点数量',
      value: detectionSummary.concernPoints,
      color: 'text-purple-600',
      bgColor: 'bg-purple-50',
    },
    {
      label: '分析置信度',
      value: `${detectionSummary.analysisConfidence}%`,
      color: 'text-indigo-600',
      bgColor: 'bg-indigo-50',
    },
  ]

  // 自定义tooltip内容
  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      return (
        <div className="bg-gray-800 text-white text-xs px-2 py-1 rounded whitespace-nowrap">
          <p>时间: {label}</p>
          <p>强度: {payload[0].value * 100}%</p>
        </div>
      )
    }
    return null
  }

  return (
    <Card className="shadow-sm hover:shadow-md transition-shadow duration-300">
      <CardHeader className="pb-3 border-b border-gray-100 bg-gray-50">
        <CardTitle className="text-lg font-semibold text-gray-800">二、检测概览</CardTitle>
      </CardHeader>
      <CardContent className="pt-5 space-y-8">
        {/* 统计数据 */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-5">
          {summaryItems.map((item, index) => (
            <div
              key={index}
              className={`${item.bgColor} rounded-lg p-4 text-center transition-all duration-300 hover:shadow-md`}
            >
              <p className="text-sm text-gray-600 font-medium">{item.label}</p>
              <p className={`text-2xl font-bold mt-1 ${item.color}`}>{item.value}</p>
            </div>
          ))}
        </div>

        {/* 情绪波动趋势图 - 使用 Recharts */}
        <div className="space-y-3">
          <h3 className="text-md font-medium text-gray-800">情绪波动趋势</h3>
          <div className="h-80 p-4 border border-gray-100 rounded-lg bg-white">
            <ResponsiveContainer width="100%" height="100%">
              <LineChart data={chartData} margin={{ top: 10, right: 10, left: 0, bottom: 20 }}>
                <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
                <XAxis dataKey="time" tick={{ fontSize: 12, fill: '#6b7280' }} tickMargin={10} />
                <YAxis
                  tickFormatter={value => `${value * 100}%`}
                  domain={[0, 1]}
                  tick={{ fontSize: 12, fill: '#6b7280' }}
                />
                <Tooltip content={<CustomTooltip />} />
                <Line
                  type="monotone"
                  dataKey="score"
                  stroke="#3b82f6"
                  strokeWidth={2}
                  dot={{ r: 4, fill: '#3b82f6', strokeWidth: 1, stroke: '#ffffff' }}
                  activeDot={{ r: 6, fill: '#2563eb', strokeWidth: 1, stroke: '#ffffff' }}
                />
              </LineChart>
            </ResponsiveContainer>
            <div className="mt-2 text-center text-xs text-gray-500">
              注: 波形图显示视频检测过程中的情绪变化情况，峰值处为关键微表情出现点
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
