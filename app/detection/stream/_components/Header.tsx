'use client'

import Link from 'next/link'
import { Button } from '@/components/ui/button'
import { ArrowLeft, FileText } from 'lucide-react'

interface HeaderProps {
  detectionInfo: {
    name: string
    description: string
    createdAt: string
  } | null
  isDetecting: boolean
  onEndDetection: () => void
}

export function Header({ detectionInfo, isDetecting, onEndDetection }: HeaderProps) {
  return (
    <div className="flex items-center mb-6">
      <Link href="/" className="mr-4 transition-transform hover:scale-105">
        <Button variant="outline" size="icon">
          <ArrowLeft className="h-4 w-4" />
        </Button>
      </Link>
      <div className="flex-1">
        <h1 className="text-2xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
          表情实时检测流
        </h1>
        {detectionInfo && <p className="text-sm text-gray-600 mt-1">{detectionInfo.name}</p>}
      </div>

      {isDetecting && (
        <Button
          variant="default"
          onClick={onEndDetection}
          className="bg-blue-600 hover:bg-blue-700"
        >
          <FileText className="h-4 w-4 mr-1" />
          结束检测
        </Button>
      )}
    </div>
  )
}
