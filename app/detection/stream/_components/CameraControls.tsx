'use client'

import { But<PERSON> } from '@/components/ui/button'
import { Pause, Play, RefreshCw } from 'lucide-react'

interface CameraControlsProps {
  isDetecting: boolean
  onToggleDetection: () => void
}

export function CameraControls({ isDetecting, onToggleDetection }: CameraControlsProps) {
  return (
    <div className="flex items-center gap-2">
      <Button
        variant={isDetecting ? 'default' : 'outline'}
        size="sm"
        onClick={onToggleDetection}
        className={isDetecting ? 'bg-green-600 hover:bg-green-700' : ''}
      >
        {isDetecting ? (
          <>
            <Pause className="h-4 w-4 mr-1" />
            停止检测
          </>
        ) : (
          <>
            <Play className="h-4 w-4 mr-1" />
            开始检测
          </>
        )}
      </Button>
    </div>
  )
}
