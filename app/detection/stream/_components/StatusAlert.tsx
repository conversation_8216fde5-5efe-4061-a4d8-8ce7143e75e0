'use client'

import { Card, CardContent } from '@/components/ui/card'
import { Info } from 'lucide-react'

interface StatusAlertProps {
  isLoading: boolean
  error: string | null
  stream: MediaStream | null
  isDetecting: boolean
  usingFallbackDevice: boolean
}

export function StatusAlert({
  isLoading,
  error,
  stream,
  isDetecting,
  usingFallbackDevice,
}: StatusAlertProps) {
  return (
    <Card>
      <CardContent className="p-3">
        <div className="flex items-start gap-2">
          <Info className="h-4 w-4 text-blue-500 mt-0.5 flex-shrink-0" />
          <div>
            <p className="text-sm text-gray-600">
              为获得最佳表情识别效果，请确保光线充足且面部正对摄像头。
              {!isDetecting && !isLoading && !error && stream && (
                <span className="ml-1 text-green-600 font-medium">
                  点击"开始检测"按钮开始分析表情。
                </span>
              )}
            </p>

            <div className="flex items-center gap-2 mt-2">
              <div
                className={`w-3 h-3 rounded-full ${!isLoading && !error && stream ? 'bg-green-500' : 'bg-amber-500'} ${isLoading ? 'animate-pulse' : ''}`}
              ></div>
              <span className="text-sm font-medium">
                {isLoading
                  ? '摄像头初始化中...'
                  : error
                    ? '摄像头出现错误'
                    : !stream
                      ? '等待摄像头连接'
                      : '摄像头已就绪，可以开始检测'}
              </span>
            </div>

            {usingFallbackDevice && (
              <p className="text-sm text-amber-600 mt-1">
                注意：无法使用您选择的摄像头，已切换至备用摄像头。
              </p>
            )}
            {error && <p className="text-sm text-red-500 mt-1">错误详情: {error}</p>}
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
