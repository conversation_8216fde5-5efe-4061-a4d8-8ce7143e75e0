'use client'

import { EmotionDashboard } from '@/components/emotion-analysis/EmotionDashboard'

interface VideoStreamProps {
  stream: MediaStream | null
  isLoading: boolean
  error: string | null
  selectedDevice: string | null
  isDetecting: boolean
  onAddDebugLog: (message: string) => void
}

export function VideoStream({
  stream,
  isLoading,
  error,
  selectedDevice,
  isDetecting,
  onAddDebugLog,
}: VideoStreamProps) {
  return (
    <div className="h-full relative bg-slate-50 rounded-lg overflow-hidden">
      {isLoading ? (
        <div className="absolute inset-0 flex items-center justify-center">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
        </div>
      ) : error ? (
        <div className="absolute inset-0 flex items-center justify-center flex-col p-4">
          <p className="text-red-500 mb-2 text-center font-medium">访问摄像头失败</p>
          <p className="text-gray-600 mb-4 text-center">{error}</p>
        </div>
      ) : selectedDevice ? (
        <EmotionDashboard />
      ) : (
        <div className="absolute inset-0 flex items-center justify-center">
          <p className="text-slate-500">请选择摄像头设备</p>
        </div>
      )}
    </div>
  )
}
