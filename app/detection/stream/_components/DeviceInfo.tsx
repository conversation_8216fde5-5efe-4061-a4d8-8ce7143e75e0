'use client'

import { Camera } from 'lucide-react'
import { Badge } from '@/components/ui/badge'

interface DeviceInfoProps {
  activeDeviceInfo: string | null
  isLoading: boolean
  error: string | null
  usingFallbackDevice: boolean
}

export function DeviceInfo({
  activeDeviceInfo,
  isLoading,
  error,
  usingFallbackDevice,
}: DeviceInfoProps) {
  if (!activeDeviceInfo || isLoading || error) return null

  return (
    <div className="flex items-center gap-2 text-sm text-gray-500 mt-2">
      <Camera className="h-4 w-4" />
      <span>当前使用: {activeDeviceInfo}</span>
      {usingFallbackDevice && (
        <Badge
          variant="outline"
          className="ml-2 text-xs bg-amber-50 text-amber-700 border-amber-200"
        >
          使用备用设备
        </Badge>
      )}
    </div>
  )
}
