'use client'

import { useState } from 'react'
import {
  ColumnDef,
  flexRender,
  getCoreRowModel,
  useReactTable,
  getPaginationRowModel,
  getSortedRowModel,
  getFilteredRowModel,
  ColumnFiltersState,
  SortingState,
} from '@tanstack/react-table'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import {
  Eye,
  Image,
  Video,
  Camera,
  Search,
  ChevronLeft,
  ChevronRight,
  ChevronsLeft,
  ChevronsRight,
  Calendar,
} from 'lucide-react'
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectLabel,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { CalendarIcon } from 'lucide-react'
import { cn } from '@/lib/utils'
import { format } from 'date-fns'
import { Calendar as DatePicker } from '@/components/ui/calendar'
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover'
import { Separator } from '@/components/ui/separator'

interface Detection {
  id: string
  name: string
  type: 'image' | 'video' | 'stream'
  model: string
  status: 'processing' | 'completed' | 'error'
  progress: number
  createdAt: string
  error?: string
}

export function DetectionTable({ data }: { data: Detection[] }) {
  const [sorting, setSorting] = useState<SortingState>([])
  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([])
  const [globalFilter, setGlobalFilter] = useState('')
  const [modelFilter, setModelFilter] = useState<string>('all')
  const [startDate, setStartDate] = useState<Date>()
  const [endDate, setEndDate] = useState<Date>()

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'processing':
        return 'text-blue-600 bg-blue-50'
      case 'completed':
        return 'text-green-600 bg-green-50'
      case 'error':
        return 'text-red-600 bg-red-50'
      default:
        return 'text-gray-600 bg-gray-50'
    }
  }

  const getStatusText = (status: string) => {
    switch (status) {
      case 'processing':
        return '处理中'
      case 'completed':
        return '已完成'
      case 'error':
        return '错误'
      default:
        return status
    }
  }

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'image':
        return <Image className="h-4 w-4" />
      case 'video':
        return <Video className="h-4 w-4" />
      case 'stream':
        return <Camera className="h-4 w-4" />
      default:
        return null
    }
  }

  const columns: ColumnDef<Detection>[] = [
    {
      accessorKey: 'type',
      header: '类型',
      cell: ({ row }) => {
        const type = row.getValue('type') as string
        return (
          <div className="flex items-center gap-2">
            {getTypeIcon(type)}
            <span className="text-sm text-gray-600">
              {type === 'image' ? '图片' : type === 'video' ? '视频' : '实时流'}
            </span>
          </div>
        )
      },
      filterFn: 'equals',
    },
    {
      accessorKey: 'name',
      header: '检测名称',
      cell: ({ row }) => {
        return (
          <div>
            <div className="font-medium text-gray-900">{row.getValue('name')}</div>
            <div className="text-sm text-gray-500">{row.original.id}</div>
          </div>
        )
      },
    },
    {
      accessorKey: 'model',
      header: '使用模型',
      cell: ({ row }) => {
        return <span className="text-gray-600">{row.getValue('model')}</span>
      },
    },
    {
      accessorKey: 'status',
      header: '状态',
      cell: ({ row }) => {
        const status = row.original.status
        const error = row.original.error

        return (
          <div className="flex flex-col gap-1">
            <span
              className={`inline-flex w-fit px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(status)}`}
            >
              {getStatusText(status)}
            </span>
            {error && (
              <span className="text-xs text-red-600 bg-red-50 px-2 py-0.5 rounded">{error}</span>
            )}
          </div>
        )
      },
      filterFn: 'equals',
    },
    {
      accessorKey: 'createdAt',
      header: '创建时间',
      cell: ({ row }) => {
        return <span className="text-gray-600">{row.getValue('createdAt')}</span>
      },
      sortingFn: 'datetime',
    },
    {
      id: 'actions',
      cell: ({ row }) => {
        return (
          <Button
            variant="outline"
            size="sm"
            onClick={() => console.log('View details:', row.original.id)}
            className="transition-all duration-300 hover:scale-105 hover:bg-blue-50 hover:text-blue-600"
          >
            <Eye className="h-4 w-4 mr-2" />
            查看详情
          </Button>
        )
      },
    },
  ]

  const table = useReactTable({
    data,
    columns,
    state: {
      sorting,
      columnFilters,
      globalFilter,
    },
    onSortingChange: setSorting,
    onColumnFiltersChange: setColumnFilters,
    onGlobalFilterChange: setGlobalFilter,
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
  })

  return (
    <div>
      <div className="space-y-4 py-4">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          {/* 搜索框 */}
          <div className="relative">
            <Search className="absolute left-2 top-2.5 h-4 w-4 text-gray-500" />
            <Input
              placeholder="搜索检测名称或ID..."
              value={globalFilter}
              onChange={event => setGlobalFilter(event.target.value)}
              className="pl-8 focus:ring-2 focus:ring-blue-50 focus:border-blue-600 transition-all duration-300"
            />
          </div>

          {/* 状态筛选 */}
          <Select
            value={(table.getColumn('status')?.getFilterValue() as string) ?? 'all'}
            onValueChange={value =>
              table.getColumn('status')?.setFilterValue(value === 'all' ? '' : value)
            }
          >
            <SelectTrigger className="focus:ring-2 focus:ring-blue-50 focus:border-blue-600 transition-all duration-300">
              <SelectValue placeholder="选择状态" />
            </SelectTrigger>
            <SelectContent>
              <SelectGroup>
                <SelectLabel>状态筛选</SelectLabel>
                <SelectItem value="all">全部状态</SelectItem>
                <SelectItem value="processing">处理中</SelectItem>
                <SelectItem value="completed">已完成</SelectItem>
                <SelectItem value="error">错误</SelectItem>
              </SelectGroup>
            </SelectContent>
          </Select>

          {/* 类型筛选 */}
          <Select
            value={(table.getColumn('type')?.getFilterValue() as string) ?? 'all'}
            onValueChange={value =>
              table.getColumn('type')?.setFilterValue(value === 'all' ? '' : value)
            }
          >
            <SelectTrigger className="focus:ring-2 focus:ring-blue-50 focus:border-blue-600 transition-all duration-300">
              <SelectValue placeholder="选择类型" />
            </SelectTrigger>
            <SelectContent>
              <SelectGroup>
                <SelectLabel>类型筛选</SelectLabel>
                <SelectItem value="all">全部类型</SelectItem>
                <SelectItem value="image">图片检测</SelectItem>
                <SelectItem value="video">视频检测</SelectItem>
                <SelectItem value="stream">实时流检测</SelectItem>
              </SelectGroup>
            </SelectContent>
          </Select>

          {/* 日期选择 */}
          <Popover>
            <PopoverTrigger asChild>
              <Button
                variant="outline"
                className={cn(
                  'w-full justify-start text-left font-normal focus:ring-2 focus:ring-blue-50 hover:bg-gray-50 transition-all duration-300',
                  !startDate && 'text-gray-500'
                )}
              >
                <CalendarIcon className="mr-2 h-4 w-4" />
                {startDate ? format(startDate, 'yyyy年MM月dd日') : '开始日期'}
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-auto p-0" align="start">
              <DatePicker mode="single" selected={startDate} onSelect={setStartDate} initialFocus />
            </PopoverContent>
          </Popover>
        </div>

        {/* 高级筛选区域 */}
        <div className="flex flex-col sm:flex-row items-center gap-4 pt-2">
          {/* 场景筛选 */}
          <Select value={modelFilter} onValueChange={setModelFilter}>
            <SelectTrigger className="w-[180px] focus:ring-2 focus:ring-blue-50 focus:border-blue-600 transition-all duration-300">
              <SelectValue placeholder="选择场景" />
            </SelectTrigger>
            <SelectContent>
              <SelectGroup>
                <SelectLabel>场景筛选</SelectLabel>
                <SelectItem value="all">全部场景</SelectItem>
                <SelectItem value="classroom">课堂教学场景</SelectItem>
                <SelectItem value="interview">面试场景</SelectItem>
                <SelectItem value="multi_person">多人互动场景</SelectItem>
              </SelectGroup>
            </SelectContent>
          </Select>

          <Separator orientation="vertical" className="h-8 hidden sm:block bg-gray-200" />

          {/* 筛选按钮组 */}
          <div className="flex items-center gap-2">
            <Popover>
              <PopoverTrigger asChild>
                <Button
                  variant="outline"
                  size="sm"
                  className="h-8 hover:bg-gray-50 transition-all duration-300"
                >
                  高级筛选
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-80 p-4" align="start">
                <div className="space-y-4">
                  <div className="space-y-2">
                    <h4 className="font-medium text-gray-900">时间范围</h4>
                    <div className="grid grid-cols-2 gap-2">
                      <div>
                        <Popover>
                          <PopoverTrigger asChild>
                            <Button
                              variant="outline"
                              className={cn(
                                'w-full justify-start text-left font-normal hover:bg-gray-50 transition-all duration-300',
                                !startDate && 'text-gray-500'
                              )}
                            >
                              <CalendarIcon className="mr-2 h-4 w-4" />
                              {startDate ? format(startDate, 'yyyy-MM-dd') : '开始日期'}
                            </Button>
                          </PopoverTrigger>
                          <PopoverContent className="w-auto p-0" align="start">
                            <DatePicker
                              mode="single"
                              selected={startDate}
                              onSelect={setStartDate}
                              initialFocus
                            />
                          </PopoverContent>
                        </Popover>
                      </div>
                      <div>
                        <Popover>
                          <PopoverTrigger asChild>
                            <Button
                              variant="outline"
                              className={cn(
                                'w-full justify-start text-left font-normal hover:bg-gray-50 transition-all duration-300',
                                !endDate && 'text-gray-500'
                              )}
                            >
                              <CalendarIcon className="mr-2 h-4 w-4" />
                              {endDate ? format(endDate, 'yyyy-MM-dd') : '结束日期'}
                            </Button>
                          </PopoverTrigger>
                          <PopoverContent className="w-auto p-0" align="start">
                            <DatePicker
                              mode="single"
                              selected={endDate}
                              onSelect={setEndDate}
                              initialFocus
                            />
                          </PopoverContent>
                        </Popover>
                      </div>
                    </div>
                  </div>
                  <div className="space-y-2">
                    <h4 className="font-medium text-gray-900">准确度范围</h4>
                    <div className="flex gap-2">
                      <Input
                        type="number"
                        placeholder="最小值"
                        className="w-20 focus:ring-2 focus:ring-blue-50 focus:border-blue-600 transition-all duration-300"
                      />
                      <span className="self-center text-gray-600">-</span>
                      <Input
                        type="number"
                        placeholder="最大值"
                        className="w-20 focus:ring-2 focus:ring-blue-50 focus:border-blue-600 transition-all duration-300"
                      />
                      <span className="self-center text-gray-600">%</span>
                    </div>
                  </div>
                  <div className="pt-4 flex justify-end gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      className="hover:bg-gray-50 transition-all duration-300"
                    >
                      取消
                    </Button>
                    <Button
                      size="sm"
                      className="bg-blue-600 hover:bg-blue-700 transition-all duration-300"
                    >
                      应用筛选
                    </Button>
                  </div>
                </div>
              </PopoverContent>
            </Popover>

            <Button
              variant="outline"
              size="sm"
              className="h-8 hover:bg-gray-50 transition-all duration-300"
              onClick={() => {
                setStartDate(undefined)
                setEndDate(undefined)
                setModelFilter('all')
                table.resetColumnFilters()
              }}
            >
              重置
            </Button>

            <Button
              variant="default"
              size="sm"
              className="h-8 bg-blue-600 hover:bg-blue-700 text-white transition-all duration-300"
              onClick={() => {
                // TODO: 实现查询逻辑
                console.log('执行查询', {
                  startDate,
                  endDate,
                  modelFilter,
                  columnFilters: table.getState().columnFilters,
                })
              }}
            >
              查询
            </Button>
          </div>
        </div>
      </div>

      <div className="rounded-md border border-gray-200">
        <Table>
          <TableHeader>
            {table.getHeaderGroups().map(headerGroup => (
              <TableRow key={headerGroup.id} className="hover:bg-gray-50">
                {headerGroup.headers.map(header => {
                  return (
                    <TableHead key={header.id} className="text-gray-900 font-medium">
                      {header.isPlaceholder
                        ? null
                        : flexRender(header.column.columnDef.header, header.getContext())}
                    </TableHead>
                  )
                })}
              </TableRow>
            ))}
          </TableHeader>
          <TableBody>
            {table.getRowModel().rows?.length ? (
              table.getRowModel().rows.map(row => (
                <TableRow
                  key={row.id}
                  data-state={row.getIsSelected() && 'selected'}
                  className="hover:bg-gray-50 transition-colors duration-200"
                >
                  {row.getVisibleCells().map(cell => (
                    <TableCell key={cell.id}>
                      {flexRender(cell.column.columnDef.cell, cell.getContext())}
                    </TableCell>
                  ))}
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell colSpan={columns.length} className="h-24 text-center text-gray-500">
                  暂无数据
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>

      <div className="flex items-center justify-between space-x-2 py-4">
        <div className="flex-1 text-sm text-gray-500">
          共 {table.getFilteredRowModel().rows.length} 条记录
        </div>
        <div className="flex items-center space-x-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => table.previousPage()}
            disabled={!table.getCanPreviousPage()}
            className="hover:bg-gray-50 transition-all duration-300"
          >
            上一页
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => table.nextPage()}
            disabled={!table.getCanNextPage()}
            className="hover:bg-gray-50 transition-all duration-300"
          >
            下一页
          </Button>
        </div>
      </div>
    </div>
  )
}
