'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { But<PERSON> } from '@/components/ui/button'
import { ArrowLeft, Loader2 } from 'lucide-react'
import Link from 'next/link'
import { useToast } from '@/components/ui/use-toast'
import { DetectionTable } from './table'

interface Detection {
  id: string
  name: string
  type: 'image' | 'video' | 'stream'
  model: string
  status: 'processing' | 'completed' | 'error'
  progress: number
  createdAt: string
  error?: string
}

export default function DetectionListPage() {
  const router = useRouter()
  const { toast } = useToast()

  const [detections, setDetections] = useState<Detection[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  // 加载检测记录
  useEffect(() => {
    const fetchDetections = async () => {
      try {
        setLoading(true)
        // TODO: 替换为实际的API调用
        // const response = await fetch('/api/detections')
        // const data = await response.json()

        // 模拟API响应
        await new Promise(resolve => setTimeout(resolve, 1000))
        const mockData: Detection[] = [
          {
            id: 'abc123',
            name: '会议室表情分析',
            type: 'image',
            model: '基础表情识别模型',
            status: 'completed',
            progress: 100,
            createdAt: '2024-03-20 10:30:00',
          },
          {
            id: 'def456',
            name: '产品发布会反馈',
            type: 'video',
            model: '高级表情识别模型',
            status: 'processing',
            progress: 60,
            createdAt: '2024-03-20 11:15:00',
          },
          {
            id: 'ghi789',
            name: '用户访谈分析',
            type: 'stream',
            model: '多人表情识别模型',
            status: 'error',
            progress: 30,
            createdAt: '2024-03-20 11:45:00',
            error: '视频流中断',
          },
        ]

        setDetections(mockData)
        setError(null)
      } catch (err) {
        setError('加载检测记录失败')
        toast({
          title: '错误',
          description: '加载检测记录失败，请重试',
          variant: 'destructive',
        })
      } finally {
        setLoading(false)
      }
    }

    fetchDetections()
  }, [toast])

  return (
    <div className="p-6">
      <div className="flex items-center mb-6">
        <Link href="/public" className="mr-4 transition-all duration-300 hover:scale-105">
          <Button variant="outline" size="icon" className="hover:bg-gray-50">
            <ArrowLeft className="h-4 w-4 text-gray-600" />
          </Button>
        </Link>
        <h1 className="text-2xl font-bold text-gray-900">检测记录</h1>
      </div>

      {loading ? (
        <div className="flex items-center justify-center py-8">
          <Loader2 className="h-8 w-8 animate-spin text-blue-600" />
        </div>
      ) : error ? (
        <div className="text-center py-8">
          <p className="text-red-600 mb-2">{error}</p>
          <Button
            variant="outline"
            onClick={() => setLoading(true)}
            className="hover:bg-gray-50 transition-all duration-300"
          >
            重试
          </Button>
        </div>
      ) : (
        <DetectionTable data={detections} />
      )}
    </div>
  )
}
