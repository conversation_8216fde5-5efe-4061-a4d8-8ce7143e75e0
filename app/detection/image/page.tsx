'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { ArrowLeft, Upload, Image as ImageIcon } from 'lucide-react'
import Link from 'next/link'
import { toast } from '@/components/ui/use-toast'
import { Textarea } from '@/components/ui/textarea'
import { DETECTION_SCENES } from '@/app/config/scenes'

export default function ImageDetectionPage() {
  const router = useRouter()
  const [selectedScene, setSelectedScene] = useState('')
  const [imageFile, setImageFile] = useState<File | null>(null)
  const [previewUrl, setPreviewUrl] = useState<string | null>(null)
  const [detectionInfo, setDetectionInfo] = useState<{
    name: string
    description: string
    createdAt: string
  } | null>(null)

  // 加载检测信息
  useEffect(() => {
    const info = localStorage.getItem('pending_detection_info')
    if (!info) {
      toast({
        title: '未找到检测信息',
        description: '请返回工作台填写检测信息',
        variant: 'destructive',
      })
      router.push('/')
      return
    }
    setDetectionInfo(JSON.parse(info))
  }, [router])

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0]
    if (file) {
      if (!file.type.startsWith('image/')) {
        toast({
          title: '文件类型错误',
          description: '请选择图片文件',
          variant: 'destructive',
        })
        return
      }
      setImageFile(file)
      // 创建预览URL
      const url = URL.createObjectURL(file)
      setPreviewUrl(url)
    }
  }

  const handleStartDetection = async () => {
    if (!selectedScene) {
      toast({
        title: '请选择场景',
        description: '在开始检测之前，请先选择一个检测场景',
        variant: 'destructive',
      })
      return
    }

    if (!imageFile) {
      toast({
        title: '请选择图片',
        description: '在开始检测之前，请先上传一张图片',
        variant: 'destructive',
      })
      return
    }

    // 获取选中场景的模型
    const selectedSceneConfig = DETECTION_SCENES.find(s => s.id === selectedScene)
    if (!selectedSceneConfig) {
      toast({
        title: '场景配置错误',
        description: '无法获取场景配置信息',
        variant: 'destructive',
      })
      return
    }

    // 生成检测ID
    const detectionId = Math.random().toString(36).substring(7)

    try {
      // 保存文件数据到localStorage
      const reader = new FileReader()
      reader.onload = function (e) {
        try {
          // 不再存储完整图片数据到localStorage或sessionStorage
          // 创建一个简化版的检测信息对象
          const detectionMetadata = {
            ...detectionInfo,
            scene: selectedScene,
            model: {
              type: selectedSceneConfig.models.image.type,
              version: selectedSceneConfig.models.image.version,
            },
            mode: 'image',
            fileName: imageFile.name,
            // 添加时间戳和简短描述，但不存储完整图片数据
            timestamp: new Date().toISOString(),
            fileSize: imageFile.size,
            fileType: imageFile.type,
            // 添加一个小的缩略图数据用于预览（可选）
            previewUrl: URL.createObjectURL(imageFile),
          }

          // 只存储元数据信息
          localStorage.setItem(`detection_${detectionId}_info`, JSON.stringify(detectionMetadata))

          // 清除临时信息
          localStorage.removeItem('pending_detection_info')

          // 跳转到检测页面
          router.push(`/detection/${detectionId}?scene=${selectedScene}&mode=image`)
        } catch (error) {
          console.error('Error saving detection data:', error)
          toast({
            title: '保存失败',
            description: '无法保存检测数据，请尝试使用较小的图片文件',
            variant: 'destructive',
          })
        }
      }
      reader.readAsDataURL(imageFile)
    } catch (error) {
      console.error('Error saving file data:', error)
      toast({
        title: '上传失败',
        description: '文件处理过程中发生错误，请重试',
        variant: 'destructive',
      })
    }
  }

  return (
    <div className="h-[calc(100vh-4rem)] p-6 flex flex-col">
      <div className="flex items-center mb-6">
        <Link href="/" className="mr-4 transition-transform hover:scale-105">
          <Button variant="outline" size="icon">
            <ArrowLeft className="h-4 w-4" />
          </Button>
        </Link>
        <div className="flex-1">
          <h1 className="text-2xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
            图片检测
          </h1>
          {detectionInfo && <p className="text-sm text-gray-600 mt-1">{detectionInfo.name}</p>}
        </div>
      </div>

      <div className="flex-1 flex gap-6 min-h-0">
        {/* 左侧 - 配置和上传区域 */}
        <div className="w-1/2 flex flex-col gap-6">
          {/* 步骤指引 */}
          <div className="flex justify-between items-center px-2">
            <div className="flex items-center">
              <div
                className={`w-8 h-8 rounded-full flex items-center justify-center ${
                  !selectedScene ? 'bg-blue-100 text-blue-600' : 'bg-green-100 text-green-600'
                }`}
              >
                1
              </div>
              <span className="ml-2 font-medium">选择场景</span>
            </div>
            <div className="flex-1 mx-4 h-0.5 bg-gray-200" />
            <div className="flex items-center">
              <div
                className={`w-8 h-8 rounded-full flex items-center justify-center ${
                  !selectedScene
                    ? 'bg-gray-100 text-gray-400'
                    : !imageFile
                      ? 'bg-blue-100 text-blue-600'
                      : 'bg-green-100 text-green-600'
                }`}
              >
                2
              </div>
              <span className="ml-2 font-medium">上传图片</span>
            </div>
          </div>

          {/* 场景选择卡片 */}
          <Card className="shadow-md hover:shadow-lg transition-shadow">
            <CardHeader className="pb-3">
              <CardTitle className="text-lg flex items-center">
                <span className="w-6 h-6 rounded-full bg-blue-100 text-blue-600 text-sm flex items-center justify-center mr-2">
                  1
                </span>
                选择场景
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <Select value={selectedScene} onValueChange={setSelectedScene}>
                  <SelectTrigger id="scene" className="h-11">
                    <SelectValue placeholder="选择检测场景" />
                  </SelectTrigger>
                  <SelectContent>
                    {DETECTION_SCENES.map(scene => (
                      <SelectItem key={scene.id} value={scene.id} className="py-2.5">
                        {scene.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                {selectedScene ? (
                  <p className="text-sm text-gray-600 bg-blue-50 p-3 rounded-lg">
                    <span className="font-medium block mb-1">场景说明</span>
                    {DETECTION_SCENES.find(s => s.id === selectedScene)?.description}
                  </p>
                ) : (
                  <p className="text-sm text-gray-500 bg-gray-50 p-3 rounded-lg">
                    请选择一个适合您需求的检测场景
                  </p>
                )}
              </div>
            </CardContent>
          </Card>

          {/* 图片上传卡片 */}
          <Card className="flex-1 shadow-md hover:shadow-lg transition-shadow flex flex-col">
            <CardHeader className="pb-3 flex-shrink-0">
              <CardTitle className="text-lg flex items-center">
                <span className="w-6 h-6 rounded-full bg-blue-100 text-blue-600 text-sm flex items-center justify-center mr-2">
                  2
                </span>
                上传图片
              </CardTitle>
            </CardHeader>
            <CardContent className="flex-1 flex flex-col min-h-0">
              {/* 上传区域 */}
              <div className="flex-1 min-h-0 relative mb-4">
                <label
                  htmlFor="image-upload"
                  className={`absolute inset-0 flex flex-col items-center justify-center border-2 border-dashed rounded-lg cursor-pointer transition-colors ${
                    previewUrl ? 'bg-white' : 'bg-gray-50 hover:bg-gray-100'
                  } ${!selectedScene ? 'opacity-50 cursor-not-allowed' : 'border-blue-200 hover:border-blue-300'}`}
                >
                  {previewUrl ? (
                    <div className="relative w-full h-full">
                      <img
                        src={previewUrl}
                        alt="Preview"
                        className="w-full h-full rounded-lg object-contain p-4"
                        onLoad={() => URL.revokeObjectURL(previewUrl)}
                      />
                      <div className="absolute inset-0 bg-black bg-opacity-0 hover:bg-opacity-10 transition-opacity rounded-lg flex items-center justify-center">
                        <p className="text-transparent hover:text-white transition-colors font-medium">
                          点击更换图片
                        </p>
                      </div>
                    </div>
                  ) : (
                    <div className="flex flex-col items-center justify-center p-8">
                      <div className="w-16 h-16 rounded-full bg-blue-50 flex items-center justify-center mb-4">
                        <Upload className="w-8 h-8 text-blue-500" />
                      </div>
                      <p className="text-base text-gray-600 mb-2">
                        <span className="font-semibold text-blue-600">点击上传</span> 或拖放图片
                      </p>
                      <p className="text-sm text-gray-500">支持PNG、JPG、JPEG格式</p>
                    </div>
                  )}
                  <Input
                    id="image-upload"
                    type="file"
                    accept="image/*"
                    className="hidden"
                    onChange={handleFileChange}
                    disabled={!selectedScene}
                  />
                </label>
              </div>

              {/* 操作按钮 */}
              <Button
                className="w-full h-12 text-base font-medium shadow-sm flex-shrink-0"
                size="lg"
                onClick={handleStartDetection}
                disabled={!imageFile || !selectedScene}
              >
                <ImageIcon className="mr-2 h-5 w-5" />
                开始检测
              </Button>
            </CardContent>
          </Card>
        </div>

        {/* 右侧 - 结果预览区域 */}
        <div className="w-1/2">
          <Card className="h-full shadow-md hover:shadow-lg transition-shadow flex flex-col">
            <CardHeader className="flex-shrink-0">
              <CardTitle className="text-lg">检测结果预览</CardTitle>
            </CardHeader>
            <CardContent className="flex-1 min-h-0 bg-gray-50 rounded-lg">
              <div className="h-full flex items-center justify-center">
                <div className="text-center text-gray-500 max-w-md mx-auto px-6">
                  <ImageIcon className="w-16 h-16 mx-auto mb-6 text-gray-400" />
                  <h3 className="text-lg font-medium mb-2">等待开始检测</h3>
                  <p className="text-sm text-gray-500">
                    请先完成左侧的配置步骤：
                    {!selectedScene ? (
                      <span className="block mt-2 text-blue-600">1. 选择一个合适的检测场景</span>
                    ) : !imageFile ? (
                      <span className="block mt-2 text-blue-600">2. 上传需要检测的图片</span>
                    ) : (
                      <span className="block mt-2 text-green-600">点击"开始检测"按钮启动检测</span>
                    )}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
