'use client'

import { useCallback, useEffect, useRef, useState } from 'react'
import { useRouter } from 'next/navigation'
import { Button } from '@/components/ui/button'
import { ArrowLeft, FileText, Info, Loader2, Play, RefreshCw, Square, Trash2 } from "lucide-react";
import { toast, useToast } from '@/components/ui/use-toast'
import { MODEL_TYPES } from '@/app/config/models'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import Link from "next/link";

type Detection = {
  id: string
  scene: string
  mode: string
  status: 'loading' | 'processing' | 'completed' | 'error'
  progress: number
  model?: {
    type: string
    version: string
  }
  results: any
  interpretations: Record<string, string>
  timestamp?: string // 添加可选的时间戳字段
}

// 获取场景名称
const getSceneName = (sceneId?: string) => {
  if (!sceneId) return '未知场景'

  const sceneNames: Record<string, string> = {
    interview: '询问场景',
    meeting: '会议室约谈场景',
    indoor_public: '室内公共场所场景',
    outdoor: '室外场景',
  }

  return sceneNames[sceneId] || sceneId
}

// 更新 ResultsSection 组件，改为表格形式
const ResultsSection = ({
  results,
  scene,
  interpretations = {},
  onUpdateInterpretations,
  videoRef,
  previewUrl,
  mode,
}: {
  results: any
  scene: string
  interpretations: Record<string, string>
  onUpdateInterpretations: (newInterpretations: Record<string, string>) => void
  videoRef: React.RefObject<HTMLVideoElement>
  previewUrl: string | null
  mode: string
}) => {
  // 添加状态管理和删除功能
  const [emotionResults, setEmotionResults] = useState<any[]>(results.emotions || [])
  const emotionTypes = ['微笑', '惊讶', '害怕', '愤怒', '悲伤', '紧张', '警惕', '轻蔑', '中性']
  const [activeRowIndex, setActiveRowIndex] = useState<number | null>(null)
  const [loopInterval, setLoopInterval] = useState<NodeJS.Timeout | null>(null)

  // 删除一行结果
  const handleDeleteRow = (index: number) => {
    const newResults = [...emotionResults]
    newResults.splice(index, 1)
    setEmotionResults(newResults)
  }

  // 更新情绪类型
  const handleEmotionTypeChange = (index: number, value: string) => {
    const newResults = [...emotionResults]
    newResults[index].emotion = value
    setEmotionResults(newResults)
  }

  // 更新置信度
  const handleConfidenceChange = (index: number, value: string) => {
    const newResults = [...emotionResults]
    const numValue = parseFloat(value) / 100
    newResults[index].confidence = Math.min(Math.max(numValue, 0), 1) // 确保在0-1之间
    setEmotionResults(newResults)
  }

  // 生成时间段显示
  const formatTimeRange = (timestamp: number) => {
    if (!timestamp) return '00:00 ~ 00:00'

    const startSec = Math.max(0, timestamp - 1)
    const endSec = timestamp + 1

    const formatTime = (sec: number) => {
      const mins = Math.floor(sec / 60)
      const secs = Math.floor(sec % 60)
      return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
    }

    return `${formatTime(startSec)} ~ ${formatTime(endSec)}`
  }

  // 获取时间范围的开始和结束秒数
  const getTimeRange = (timestamp: number) => {
    if (!timestamp) return { start: 0, end: 2 }
    const start = Math.max(0, timestamp - 1)
    const end = timestamp + 1
    return { start, end }
  }

  // 处理行点击，定位视频并循环播放
  const handleRowClick = (index: number) => {
    // 只在视频模式下才处理
    if (mode !== 'video' || !videoRef.current || !previewUrl) return

    // 如果已经在循环播放这一行，则停止循环
    if (activeRowIndex === index && loopInterval) {
      clearInterval(loopInterval)
      setLoopInterval(null)
      setActiveRowIndex(null)
      return
    }

    // 清除上一个循环
    if (loopInterval) {
      clearInterval(loopInterval)
      setLoopInterval(null)
    }

    const timestamp = emotionResults[index].timestamp
    const { start, end } = getTimeRange(timestamp)

    // 设置当前活动行
    setActiveRowIndex(index)

    // 设置视频时间并播放
    try {
      const video = videoRef.current

      // 设置视频到开始时间
      video.currentTime = start
      video.play()

      // 创建循环播放的间隔
      const interval = setInterval(() => {
        // 如果视频当前时间大于结束时间，重新设置到开始时间
        if (video.currentTime >= end) {
          video.currentTime = start
        }
      }, 100) // 每100毫秒检查一次

      setLoopInterval(interval)
    } catch (err) {
      console.error('Failed to control video playback:', err)
      toast({
        title: '视频控制失败',
        description: '无法定位或播放视频',
        variant: 'destructive',
      })
    }
  }

  // 组件卸载时清理定时器
  useEffect(() => {
    return () => {
      if (loopInterval) {
        clearInterval(loopInterval)
      }
    }
  }, [loopInterval])

  return (
    <div className="bg-white rounded-lg shadow-md p-4 h-full flex flex-col">
      <div className="flex justify-between items-center mb-3">
        <h2 className="text-xl font-semibold">检测结果</h2>
        {mode === 'video' && (
          <p className="text-sm text-gray-500 flex items-center">
            <Info className="h-4 w-4 mr-1" />
            点击行可定位到视频对应时间点
          </p>
        )}
      </div>
      {emotionResults.length > 0 ? (
        <div className="overflow-y-auto flex-1">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead className="w-[50px] py-2">序号</TableHead>
                <TableHead className="w-[140px] py-2">发生时间</TableHead>
                <TableHead className="w-[120px] py-2">情绪类型</TableHead>
                <TableHead className="w-[100px] py-2">置信度</TableHead>
                <TableHead className="py-2">表情解读</TableHead>
                <TableHead className="w-[60px] py-2">操作</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {emotionResults.map((item, index) => (
                <TableRow
                  key={index}
                  className={`${activeRowIndex === index ? 'bg-blue-50' : ''} ${mode === 'video' ? 'cursor-pointer hover:bg-gray-50' : ''}`}
                  onClick={() => handleRowClick(index)}
                >
                  <TableCell className="font-medium py-2">{index + 1}</TableCell>
                  <TableCell className="py-2">{formatTimeRange(item.timestamp)}</TableCell>
                  <TableCell
                    onClick={(e: React.MouseEvent) => e.stopPropagation()}
                    className="py-2"
                  >
                    <Select
                      value={item.emotion}
                      onValueChange={value => handleEmotionTypeChange(index, value)}
                    >
                      <SelectTrigger className="w-full h-8 text-sm">
                        <SelectValue placeholder="选择情绪类型" />
                      </SelectTrigger>
                      <SelectContent>
                        {emotionTypes.map(type => (
                          <SelectItem key={type} value={type}>
                            {type}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </TableCell>
                  <TableCell
                    onClick={(e: React.MouseEvent) => e.stopPropagation()}
                    className="py-2"
                  >
                    <Input
                      type="number"
                      min="0"
                      max="100"
                      value={(item.confidence * 100).toFixed(1)}
                      onChange={e => handleConfidenceChange(index, e.target.value)}
                      className="w-full h-8 text-sm"
                    />
                  </TableCell>
                  <TableCell
                    onClick={(e: React.MouseEvent) => e.stopPropagation()}
                    className="py-2"
                  >
                    <Textarea
                      placeholder="添加表情解读..."
                      value={interpretations[`emotion_${index}`] || ''}
                      onChange={e => {
                        const newInterpretations = {
                          ...interpretations,
                          [`emotion_${index}`]: e.target.value,
                        }
                        onUpdateInterpretations(newInterpretations)
                      }}
                      className="w-full min-h-[60px] text-sm"
                    />
                  </TableCell>
                  <TableCell className="py-2">
                    <Button
                      variant="ghost"
                      size="icon"
                      onClick={(e: React.MouseEvent) => {
                        e.stopPropagation()
                        handleDeleteRow(index)
                      }}
                      className="text-red-500 hover:text-red-700 hover:bg-red-50 h-8 w-8"
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
      ) : (
        <p className="text-gray-500 text-center py-4">暂无检测结果</p>
      )}
    </div>
  )
}

export default function DetectionPage({ params }: { params: { id: string } }) {
  const detectionId = params.id
  const router = useRouter()
  const { toast } = useToast()

  // 添加视频引用
  const videoRef = useRef<HTMLVideoElement>(null)

  // 添加进度间隔状态
  const [progressInterval, setProgressInterval] = useState<NodeJS.Timeout | null>(null)

  // 设置初始检测状态，使用 mock 数据
  const [detection, setDetection] = useState<Detection>({
    id: detectionId,
    scene: 'interview',
    mode: 'video',
    status: 'completed',
    progress: 1,
    model: {
      type: 'emotion',
      version: '1.0.0'
    },
    results: {
      emotions: [
        { emotion: '警惕', confidence: 0.89, timestamp: 1.2 },
        { emotion: '紧张', confidence: 0.76, timestamp: 2.5 },
        { emotion: '微笑', confidence: 0.65, timestamp: 3.8 },
      ]
    },
    interpretations: {},
    timestamp: new Date().toISOString()
  })

  const [error, setError] = useState<string | null>(null)
  const [previewUrl, setPreviewUrl] = useState<string | null>('https://www.w3schools.com/html/mov_bbb.mp4')

  // 移除 useEffect 中的 localStorage 相关代码
  useEffect(() => {
    // 这里可以添加其他初始化逻辑
  }, [detectionId])

  // 移除不需要的处理函数
  const handleUpdateInterpretations = (newInterpretations: Record<string, string>) => {
    setDetection(prev => ({
      ...prev,
      interpretations: newInterpretations,
    }))
  }

  const handleRestartDetection = () => {
    // 实现重新检测逻辑
    setDetection(prev => ({
      ...prev,
      status: 'completed',
      progress: 1,
      results: {
        emotions: [
          { emotion: '警惕', confidence: 0.89, timestamp: 1.2 },
          { emotion: '紧张', confidence: 0.76, timestamp: 2.5 },
          { emotion: '微笑', confidence: 0.65, timestamp: 3.8 },
        ],
      },
    }))
  }

  // 在完成检测后模拟结果
  const completeDetection = useCallback(() => {
    if (progressInterval) {
      clearInterval(progressInterval)
      setProgressInterval(null)
    }

    // 根据检测类型生成模拟结果
    const mockResult = {
      emotions: [
        { emotion: '警惕', confidence: 0.89, timestamp: 1.2 },
        { emotion: '紧张', confidence: 0.76, timestamp: 2.5 },
        { emotion: '微笑', confidence: 0.65, timestamp: 3.8 },
      ],
    }

    setDetection(prev => ({
      ...prev,
      status: 'completed',
      progress: 1,
      results: mockResult,
    }))
  }, [progressInterval])

  // 在进度达到一定程度时自动完成检测
  useEffect(() => {
    if (detection.status === 'processing' && detection.progress > 0.95) {
      completeDetection()
    }
  }, [detection.status, detection.progress, completeDetection])

  // 开启检测并观察进度
  useEffect(() => {
    // 模拟从检测进度到完成的过程
    if (detection.status === 'processing' && detection.progress >= 0.99) {
      completeDetection()
    }
  }, [detection.status, detection.progress, completeDetection])

  // 清理effect
  useEffect(() => {
    // 组件卸载时清理任何进行中的计时器
    return () => {
      if (progressInterval) {
        clearInterval(progressInterval)
      }
    }
  }, [progressInterval])

  // 生成报告
  const handleGenerateReport = () => {
    const url = `/detection/report?id=${detectionId}`
    window.open(url, '_blank')
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'processing':
        return 'text-blue-600 bg-blue-50'
      case 'completed':
        return 'text-green-600 bg-green-50'
      case 'error':
        return 'text-red-600 bg-red-50'
      default:
        return 'text-gray-600 bg-gray-50'
    }
  }

  const getStatusText = (status: string) => {
    switch (status) {
      case 'processing':
        return '处理中'
      case 'completed':
        return '已完成'
      case 'error':
        return '错误'
      default:
        return status
    }
  }

  const getModeText = (mode: string) => {
    switch (mode) {
      case 'image':
        return '图片检测'
      case 'video':
        return '视频检测'
      case 'stream':
        return '实时检测'
      default:
        return mode
    }
  }

  const getModelInfo = (type: string, version: string) => {
    const modelType = MODEL_TYPES.find(m => m.id === type)
    if (!modelType) return null
    return modelType.versions.find(v => v.version === version)
  }

  // 添加状态徽章组件
  const StatusBadge = ({ status }: { status: string }) => {
    const getStatusColor = (status: string) => {
      switch (status) {
        case 'loading':
          return 'bg-gray-100 text-gray-600'
        case 'processing':
          return 'bg-blue-100 text-blue-600'
        case 'completed':
          return 'bg-green-100 text-green-600'
        case 'error':
          return 'bg-red-100 text-red-600'
        default:
          return 'bg-gray-100 text-gray-600'
      }
    }

    const getStatusText = (status: string) => {
      switch (status) {
        case 'loading':
          return '准备中'
        case 'processing':
          return '检测中'
        case 'completed':
          return '已完成'
        case 'error':
          return '出错'
        default:
          return '未知'
      }
    }

    return (
      <span className={`px-2 py-0.5 rounded-full text-sm ${getStatusColor(status)}`}>
        {getStatusText(status)}
      </span>
    )
  }

  return (
    <div className="container mx-auto py-6 space-y-4">
      <div className="flex justify-between items-center mb-2">
        <div className="flex items-center space-x-2">
          <Link href="/" className="mr-4 transition-transform hover:scale-105">
            <Button variant="outline" size="icon" className="shadow-sm">
              <ArrowLeft className="h-4 w-4" />
            </Button>
          </Link>
          <h1 className="text-3xl font-bold">表情检测详情</h1>
        </div>
        {detection.status === 'completed' && (
          <Button
            onClick={handleGenerateReport}
            className="px-4 py-2 bg-gradient-to-r from-blue-500 to-blue-600 text-white rounded-md shadow-md hover:from-blue-600 hover:to-blue-700 transition-colors"
          >
            <FileText className="w-4 h-4 mr-2" />
            生成检测报告
          </Button>
        )}
      </div>

      {error ? (
        <div className="p-6 bg-red-50 border border-red-200 rounded-lg text-red-700 text-center">
          <h3 className="text-xl font-semibold mb-2">检测加载失败</h3>
          <p>{error}</p>
          <Button variant="outline" className="mt-4" onClick={() => router.push('/')}>
            返回首页
          </Button>
        </div>
      ) : (
        <>
          {/* 检测信息区域 - 横贯整个宽度 */}
          <div className="bg-white rounded-lg shadow-md p-4 mb-4">
            <div className="flex justify-between items-start mb-3">
              <h2 className="text-xl font-semibold">检测信息</h2>
            </div>

            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div className="space-y-1">
                <p className="text-gray-500 text-sm">检测场景</p>
                <p className="font-medium">{getSceneName(detection?.scene) || '加载中...'}</p>
              </div>
              <div className="space-y-1">
                <p className="text-gray-500 text-sm">检测模式</p>
                <p className="font-medium capitalize">{detection?.mode || '加载中...'}</p>
              </div>
              <div className="space-y-1">
                <p className="text-gray-500 text-sm">检测时间</p>
                <p className="font-medium">
                  {detection?.timestamp
                    ? new Date(detection.timestamp).toLocaleString('zh-CN')
                    : '加载中...'}
                </p>
              </div>
              <div className="space-y-1">
                <p className="text-gray-500 text-sm">检测状态</p>
                <StatusBadge status={detection?.status || 'loading'} />
              </div>
              {detection?.model && (
                <>
                  <div className="space-y-1">
                    <p className="text-gray-500 text-sm">模型类型</p>
                    <p className="font-medium">{detection.model.type}</p>
                  </div>
                  <div className="space-y-1">
                    <p className="text-gray-500 text-sm">模型版本</p>
                    <p className="font-medium">{detection.model.version}</p>
                  </div>
                </>
              )}
              <div className="space-y-1">
                <p className="text-gray-500 text-sm">检测 ID</p>
                <p className="font-medium truncate">{detection.id || '加载中...'}</p>
              </div>
            </div>
          </div>

          {/* 主内容区域 - 左右分区 */}
          {detection.status === 'completed' && detection.results && (
            <div
              className="grid grid-cols-1 lg:grid-cols-2 gap-4"
              style={{ height: 'calc(100vh - 300px)' }}
            >
              {/* 左侧 - 视频预览区域 */}
              <div className="bg-white rounded-lg shadow-md p-4 h-full flex flex-col">
                <h2 className="text-xl font-semibold mb-3">检测预览</h2>

                <div className="flex-1 border border-gray-200 rounded-lg overflow-hidden bg-gray-50 flex items-center justify-center">
                  {detection.mode === 'image' && previewUrl ? (
                    <img src={previewUrl} alt="检测图像" className="w-full h-auto object-contain" />
                  ) : detection.mode === 'video' && previewUrl ? (
                    <video
                      ref={videoRef}
                      src={previewUrl}
                      controls
                      className="w-full h-full object-contain"
                    />
                  ) : detection.mode === 'stream' ? (
                    <div className="p-4 text-center w-full h-full">
                      <p className="text-gray-500 mb-2">摄像头实时检测</p>
                      <div
                        id="camera-container"
                        className="relative w-full h-full bg-gray-900 rounded-md overflow-hidden flex items-center justify-center"
                      >
                        <p className="text-white">摄像头预览将在检测开始后显示</p>
                      </div>
                    </div>
                  ) : (
                    <div className="flex flex-col items-center justify-center p-6 w-full h-full">
                      <Loader2 className="w-10 h-10 text-gray-400 animate-spin mb-2" />
                      <p className="text-gray-500">加载预览...</p>
                    </div>
                  )}
                </div>
              </div>

              {/* 右侧 - 检测结果表格 */}
              <ResultsSection
                results={detection.results}
                scene={detection.scene}
                interpretations={detection.interpretations}
                onUpdateInterpretations={handleUpdateInterpretations}
                videoRef={videoRef}
                previewUrl={previewUrl}
                mode={detection.mode}
              />
            </div>
          )}
        </>
      )}
    </div>
  )
}
