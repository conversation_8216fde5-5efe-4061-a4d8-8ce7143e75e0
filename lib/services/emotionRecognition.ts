import { EmotionDetectionResult, EmotionType } from '../types/emotion'
import { v4 as uuidv4 } from 'uuid'

const emotions: EmotionType[] = ['高兴', '生气', '厌恶', '恐惧', '悲伤', '惊讶']

export function recognizeEmotion(): Promise<string> {
  return new Promise(resolve => {
    setTimeout(() => {
      const randomEmotion = emotions[Math.floor(Math.random() * emotions.length)]
      resolve(randomEmotion)
    }, 500)
  })
}

export function getEmotionDetectionResult(): Promise<EmotionDetectionResult> {
  return new Promise(resolve => {
    setTimeout(() => {
      const randomEmotion = emotions[Math.floor(Math.random() * emotions.length)]
      const confidence = Math.round((0.7 + Math.random() * 0.3) * 100) / 100 // Between 0.7 and 1.0
      const duration = Math.floor(Math.random() * 5000) + 1000 // Between 1-6 seconds

      resolve({
        id: uuidv4(),
        emotion: randomEmotion,
        confidence,
        startTime: new Date(),
        duration,
      })
    }, 300)
  })
}

// Mock function to get a list of recent emotion detection results
export function getMockEmotionHistory(count: number = 10): EmotionDetectionResult[] {
  const results: EmotionDetectionResult[] = []
  const now = new Date()

  for (let i = 0; i < count; i++) {
    const randomEmotion = emotions[Math.floor(Math.random() * emotions.length)]
    const confidence = Math.round((0.7 + Math.random() * 0.3) * 100) / 100
    const duration = Math.floor(Math.random() * 5000) + 1000
    const timeOffset = i * (Math.floor(Math.random() * 30000) + 10000) // Random time between entries

    results.push({
      id: uuidv4(),
      emotion: randomEmotion,
      confidence,
      startTime: new Date(now.getTime() - timeOffset),
      duration,
    })
  }

  return results.sort((a, b) => b.startTime.getTime() - a.startTime.getTime())
}
