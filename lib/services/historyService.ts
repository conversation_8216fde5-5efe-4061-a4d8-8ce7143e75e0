export interface HistoryVideo {
  id: string;
  title: string;
  date: string;
  duration: string;
  thumbnailUrl: string;
}

const mockHistoryVideos: HistoryVideo[] = [
  {
    id: '1',
    title: '会议演讲',
    date: '2023-05-15',
    duration: '10:30',
    thumbnailUrl: '/placeholder.svg?height=120&width=200',
  },
  {
    id: '2',
    title: '产品演示',
    date: '2023-05-14',
    duration: '15:45',
    thumbnailUrl: '/placeholder.svg?height=120&width=200',
  },
  {
    id: '3',
    title: '团队讨论',
    date: '2023-05-13',
    duration: '20:15',
    thumbnailUrl: '/placeholder.svg?height=120&width=200',
  },
];

export function getHistoryVideos(): Promise<HistoryVideo[]> {
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve(mockHistoryVideos);
    }, 500);
  });
}

export function getVideoById(id: string): Promise<HistoryVideo | undefined> {
  return new Promise((resolve) => {
    setTimeout(() => {
      const video = mockHistoryVideos.find(v => v.id === id);
      resolve(video);
    }, 300);
  });
}

