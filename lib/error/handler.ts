import { NextResponse } from 'next/server';
import { AppError, ValidationError, AuthenticationError } from './types';

// 判断是否是预期内的错误
function isExpectedError(error: unknown): boolean {
  // 验证错误（如：密码错误、输入格式错误等）
  if (error instanceof ValidationError) {
    return true;
  }
  // 认证错误（如：未登录、会话过期等）
  if (error instanceof AuthenticationError) {
    return true;
  }
  // 其他 AppError，但状态码小于 500（客户端错误）
  if (error instanceof AppError && error.statusCode < 500) {
    return true;
  }
  return false;
}

export function handleError(error: unknown) {
  // 对于预期外的错误，记录详细日志
  if (!isExpectedError(error)) {
    console.error('Unexpected error:', {
      error,
      stack: error instanceof Error ? error.stack : undefined,
      timestamp: new Date().toISOString(),
    });
  } else {
    // 对于预期内的错误，只记录基本信息用于分析
    console.info('Expected error:', {
      type: error instanceof Error ? error.constructor.name : typeof error,
      message: error instanceof Error ? error.message : String(error),
      timestamp: new Date().toISOString(),
    });
  }

  if (error instanceof AppError) {
    return NextResponse.json(
      {
        error: {
          message: error.message,
          code: error.code,
          ...(error instanceof ValidationError && { details: error.details }),
        },
      },
      { status: error.statusCode }
    );
  }

  // 处理未知错误
  const internalError = {
    error: {
      message: process.env.NODE_ENV === 'development' 
        ? (error instanceof Error ? error.message : String(error))
        : '服务器内部错误',
      code: 'INTERNAL_SERVER_ERROR',
    },
  };

  return NextResponse.json(internalError, { status: 500 });
}

type RouteHandler = (
  request: Request,
  context: { params: Record<string, string | string[]> }
) => Promise<Response>;

export function withErrorHandler(handler: RouteHandler): RouteHandler {
  return async (request: Request, context: { params: Record<string, string | string[]> }) => {
    try {
      return await handler(request, context);
    } catch (error) {
      return handleError(error);
    }
  };
}
