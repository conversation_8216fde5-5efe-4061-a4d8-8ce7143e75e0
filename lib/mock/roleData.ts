import { faker } from '@faker-js/faker/locale/zh_CN'

export interface Permission {
  id: string
  name: string
  description: string
  module: string
}

export interface Role {
  id: string
  name: string
  code: string
  description: string
  permissions: Permission[]
  userCount: number
  createdAt: string
  updatedAt: string
}

// 预定义的权限列表
export const predefinedPermissions: Permission[] = [
  {
    id: '1',
    name: '用户管理',
    description: '管理系统用户',
    module: 'user',
  },
  {
    id: '2',
    name: '角色管理',
    description: '管理系统角色',
    module: 'role',
  },
  {
    id: '3',
    name: '数据标注',
    description: '执行数据标注任务',
    module: 'annotation',
  },
  {
    id: '4',
    name: '模型训练',
    description: '执行模型训练任务',
    module: 'training',
  },
  {
    id: '5',
    name: '系统设置',
    description: '管理系统配置',
    module: 'settings',
  },
]

// 预定义的角色
const predefinedRoles: Role[] = [
  {
    id: '1',
    name: '系统管理员',
    code: 'admin',
    description: '系统超级管理员，拥有所有权限',
    permissions: predefinedPermissions,
    userCount: 1,
    createdAt: '2024-01-01 00:00:00',
    updatedAt: '2024-01-01 00:00:00',
  },
  {
    id: '2',
    name: '标注员',
    code: 'annotator',
    description: '数据标注人员',
    permissions: [predefinedPermissions[2]], // 只有数据标注权限
    userCount: 5,
    createdAt: '2024-01-02 00:00:00',
    updatedAt: '2024-01-02 00:00:00',
  },
]

// 生成随机角色数据
export function generateMockRoles(count: number = 10): Role[] {
  const mockRoles = [...predefinedRoles]

  for (let i = 0; i < count - predefinedRoles.length; i++) {
    const randomPermissions = faker.helpers.arrayElements(predefinedPermissions, {
      min: 1,
      max: predefinedPermissions.length,
    })

    const role: Role = {
      id: faker.string.uuid(),
      name: `${faker.commerce.department()}${faker.helpers.arrayElement(['主管', '经理', '专员'])}`,
      code: faker.helpers.slugify(faker.lorem.word()).toLowerCase(),
      description: faker.lorem.sentence(),
      permissions: randomPermissions,
      userCount: faker.number.int({ min: 0, max: 20 }),
      createdAt:
        faker.date.past().toISOString().split('T')[0] +
        ' ' +
        faker.date.past().toTimeString().split(' ')[0],
      updatedAt:
        faker.date.recent().toISOString().split('T')[0] +
        ' ' +
        faker.date.recent().toTimeString().split(' ')[0],
    }

    mockRoles.push(role)
  }

  return mockRoles
}

// 模拟数据库中的角色数据
export const mockRoles = generateMockRoles()

// 模拟分页查询
export function queryRoles(params: {
  page?: number
  pageSize?: number
  name?: string
  code?: string
}) {
  const { page = 1, pageSize = 10, name, code } = params

  let filteredRoles = [...mockRoles]

  // 按名称筛选
  if (name) {
    filteredRoles = filteredRoles.filter(role =>
      role.name.toLowerCase().includes(name.toLowerCase())
    )
  }

  // 按标识筛选
  if (code) {
    filteredRoles = filteredRoles.filter(role =>
      role.code.toLowerCase().includes(code.toLowerCase())
    )
  }

  // 计算总数
  const total = filteredRoles.length

  // 分页
  const start = (page - 1) * pageSize
  const end = start + pageSize
  const data = filteredRoles.slice(start, end)

  return {
    data,
    total,
    page,
    pageSize,
  }
}
