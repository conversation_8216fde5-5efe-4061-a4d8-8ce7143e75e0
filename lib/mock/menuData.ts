import { faker } from '@faker-js/faker/locale/zh_CN'

export interface MenuItem {
  id: string
  name: string
  path: string
  icon?: string
  parentId?: string
  order: number
  type: 'menu' | 'page' | 'button'
  permission?: string
  status: 'enabled' | 'disabled'
  children?: MenuItem[]
  createdAt: string
  updatedAt: string
}

// 预定义的菜单数据
export const predefinedMenus: MenuItem[] = [
  {
    id: '1',
    name: '系统管理',
    path: '/admin',
    icon: 'Settings',
    order: 1,
    type: 'menu',
    status: 'enabled',
    createdAt: '2024-01-01 00:00:00',
    updatedAt: '2024-01-01 00:00:00',
    children: [
      {
        id: '1-1',
        name: '用户管理',
        path: '/admin/user/management',
        icon: 'Users',
        parentId: '1',
        order: 1,
        type: 'menu',
        permission: 'user:view',
        status: 'enabled',
        createdAt: '2024-01-01 00:00:00',
        updatedAt: '2024-01-01 00:00:00',
        children: [
          {
            id: '1-1-1',
            name: '新建用户',
            path: '/admin/user/management?action=create',
            parentId: '1-1',
            order: 1,
            type: 'button',
            permission: 'user:create',
            status: 'enabled',
            createdAt: '2024-01-01 00:00:00',
            updatedAt: '2024-01-01 00:00:00',
          },
          {
            id: '1-1-2',
            name: '编辑用户',
            path: '/admin/user/management?action=edit',
            parentId: '1-1',
            order: 2,
            type: 'button',
            permission: 'user:edit',
            status: 'enabled',
            createdAt: '2024-01-01 00:00:00',
            updatedAt: '2024-01-01 00:00:00',
          },
        ],
      },
      {
        id: '1-2',
        name: '角色管理',
        path: '/admin/user/roles',
        icon: 'Shield',
        parentId: '1',
        order: 2,
        type: 'menu',
        permission: 'role:view',
        status: 'enabled',
        createdAt: '2024-01-01 00:00:00',
        updatedAt: '2024-01-01 00:00:00',
      },
      {
        id: '1-3',
        name: '菜单管理',
        path: '/admin/user/menus',
        icon: 'Menu',
        parentId: '1',
        order: 3,
        type: 'menu',
        permission: 'menu:view',
        status: 'enabled',
        createdAt: '2024-01-01 00:00:00',
        updatedAt: '2024-01-01 00:00:00',
      },
    ],
  },
  {
    id: '2',
    name: '数据标注',
    path: '/admin/annotation',
    icon: 'Tag',
    order: 2,
    type: 'menu',
    status: 'enabled',
    createdAt: '2024-01-01 00:00:00',
    updatedAt: '2024-01-01 00:00:00',
    children: [
      {
        id: '2-1',
        name: '数据集管理',
        path: '/admin/annotation/collection',
        icon: 'Database',
        parentId: '2',
        order: 1,
        type: 'menu',
        permission: 'collection:view',
        status: 'enabled',
        createdAt: '2024-01-01 00:00:00',
        updatedAt: '2024-01-01 00:00:00',
      },
      {
        id: '2-2',
        name: '标注工作台',
        path: '/admin/annotation/workbench',
        icon: 'PenTool',
        parentId: '2',
        order: 2,
        type: 'menu',
        permission: 'annotation:view',
        status: 'enabled',
        createdAt: '2024-01-01 00:00:00',
        updatedAt: '2024-01-01 00:00:00',
      },
    ],
  },
]

// 将树形结构扁平化为列表
export function flattenMenus(menus: MenuItem[]): MenuItem[] {
  const result: MenuItem[] = []

  function flatten(items: MenuItem[]) {
    items.forEach(item => {
      const { children, ...rest } = item
      result.push(rest)
      if (children) {
        flatten(children)
      }
    })
  }

  flatten(menus)
  return result
}

// 将扁平列表转换为树形结构
export function buildMenuTree(items: MenuItem[]): MenuItem[] {
  const itemMap = new Map<string, MenuItem>()
  const result: MenuItem[] = []

  // 首先创建所有节点的映射
  items.forEach(item => {
    itemMap.set(item.id, { ...item, children: [] })
  })

  // 构建树形结构
  items.forEach(item => {
    const node = itemMap.get(item.id)!
    if (item.parentId) {
      const parent = itemMap.get(item.parentId)
      if (parent) {
        if (!parent.children) {
          parent.children = []
        }
        parent.children.push(node)
      }
    } else {
      result.push(node)
    }
  })

  // 对每一级菜单进行排序
  function sortMenus(menus: MenuItem[]) {
    menus.sort((a, b) => a.order - b.order)
    menus.forEach(menu => {
      if (menu.children && menu.children.length > 0) {
        sortMenus(menu.children)
      }
    })
  }

  sortMenus(result)
  return result
}

// 模拟数据库中的菜单数据
export const mockMenus = flattenMenus(predefinedMenus)

// 模拟分页查询
export function queryMenus(params: {
  page?: number
  pageSize?: number
  name?: string
  type?: 'menu' | 'page' | 'button'
  status?: 'enabled' | 'disabled'
}) {
  const { page = 1, pageSize = 10, name, type, status } = params

  let filteredMenus = [...mockMenus]

  // 按名称筛选
  if (name) {
    filteredMenus = filteredMenus.filter(menu =>
      menu.name.toLowerCase().includes(name.toLowerCase())
    )
  }

  // 按类型筛选
  if (type) {
    filteredMenus = filteredMenus.filter(menu => menu.type === type)
  }

  // 按状态筛选
  if (status) {
    filteredMenus = filteredMenus.filter(menu => menu.status === status)
  }

  // 计算总数
  const total = filteredMenus.length

  // 分页
  const start = (page - 1) * pageSize
  const end = start + pageSize
  const data = filteredMenus.slice(start, end)

  return {
    data,
    total,
    page,
    pageSize,
  }
}
