/**
 * 环境检测工具
 */

// 检测是否为 Electron 环境
export function isElectron(): boolean {
  return (
    typeof window !== 'undefined' &&
    typeof (window as any).process === 'object' &&
    (window as any).process?.type === 'renderer'
  )
}

// 检测是否为 Web 环境
export function isWeb(): boolean {
  return !isElectron()
}

// 检测是否为开发环境
export function isDevelopment(): boolean {
  return process.env.NODE_ENV === 'development'
}

// 获取平台信息
export function getPlatform(): string {
  if (isElectron()) {
    return (window as any).electronAPI?.platform || 'unknown'
  }
  return 'web'
}
