import { isElectron } from './environment'

/**
 * 确认对话框工具
 * 在 Electron 环境中使用系统级确认弹窗
 * 在 Web 环境中使用浏览器 confirm
 */

export interface ConfirmOptions {
  title?: string
  message: string
  confirmText?: string
  cancelText?: string
  type?: 'info' | 'warning' | 'error' | 'question'
}

/**
 * 显示确认对话框
 */
export async function showConfirmDialog(options: ConfirmOptions): Promise<boolean> {
  const {
    title = '确认',
    message,
    confirmText = '确定',
    cancelText = '取消',
    type = 'question',
  } = options

  if (isElectron()) {
    // Electron 环境：使用系统级确认弹窗
    return showElectronConfirmDialog({
      title,
      message,
      confirmText,
      cancelText,
      type,
    })
  } else {
    // Web 环境：使用浏览器 confirm
    return showWebConfirmDialog({
      title,
      message,
      confirmText,
      cancelText,
      type,
    })
  }
}

/**
 * Electron 环境下的确认对话框
 */
async function showElectronConfirmDialog(options: ConfirmOptions): Promise<boolean> {
  try {
    // 通过 preload 脚本调用 Electron 的确认对话框
    const result = await (window as any).electronAPI?.showConfirmDialog?.(options)
    return result === true
  } catch (error) {
    console.error('Electron 确认对话框调用失败:', error)
    // 降级到 Web 确认对话框
    return showWebConfirmDialog(options)
  }
}

/**
 * Web 环境下的确认对话框
 */
function showWebConfirmDialog(options: ConfirmOptions): boolean {
  const { message } = options
  return window.confirm(message)
}

/**
 * 删除确认对话框的快捷方法
 */
export async function showDeleteConfirmDialog(
  itemName: string = '选中项',
  count: number = 1
): Promise<boolean> {
  const message =
    count === 1
      ? `确定要删除 ${itemName} 吗？此操作不可撤销。`
      : `确定要删除选中的 ${count} 个 ${itemName} 吗？此操作不可撤销。`

  return showConfirmDialog({
    title: '删除确认',
    message,
    confirmText: '删除',
    cancelText: '取消',
    type: 'warning',
  })
}

/**
 * 批量操作确认对话框
 */
export async function showBatchConfirmDialog(
  action: string,
  itemName: string,
  count: number
): Promise<boolean> {
  const message = `确定要对选中的 ${count} 个 ${itemName} 执行 ${action} 操作吗？`

  return showConfirmDialog({
    title: `${action}确认`,
    message,
    confirmText: action,
    cancelText: '取消',
    type: 'info',
  })
}
