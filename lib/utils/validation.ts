import { ValidationError } from '../error/types'

export type ValidationRule<T> = {
  validate: (value: T) => boolean
  message: string
}

export type ValidationRules<T> = {
  [K in keyof T]?: ValidationRule<T[K]>[]
}

export function validateInput<T extends Record<string, any>>(
  data: T,
  rules: ValidationRules<T>
): void {
  const errors: Record<string, string[]> = {}

  for (const [field, fieldRules] of Object.entries(rules)) {
    if (!fieldRules) continue

    const value = data[field]
    const fieldErrors = fieldRules
      .filter((rule: ValidationRule<any>) => !rule.validate(value))
      .map((rule: ValidationRule<any>) => rule.message)

    if (fieldErrors.length > 0) {
      errors[field] = fieldErrors
    }
  }

  if (Object.keys(errors).length > 0) {
    throw new ValidationError('输入验证失败', errors)
  }
}

// 预定义的验证规则
export const rules = {
  required: (message = '此字段是必填的'): ValidationRule<any> => ({
    validate: value => value !== undefined && value !== null && value !== '',
    message
  }),

  minLength: (min: number, message = `最少需要 ${min} 个字符`): ValidationRule<string> => ({
    validate: value => !value || value.length >= min,
    message
  }),

  maxLength: (max: number, message = `最多允许 ${max} 个字符`): ValidationRule<string> => ({
    validate: value => !value || value.length <= max,
    message
  }),

  email: (message = '请输入有效的邮箱地址'): ValidationRule<string> => ({
    validate: value => !value || /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value),
    message
  }),

  pattern: (regex: RegExp, message: string): ValidationRule<string> => ({
    validate: value => !value || regex.test(value),
    message
  })
}
