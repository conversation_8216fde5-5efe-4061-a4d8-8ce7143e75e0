import { spawn } from 'child_process'
import path from 'path'

interface TrainingTask {
  id: number
  modelId: number
  datasetId: number
  epoch: number
  learningRate: number
  datasetPath: string
}

class TaskQueue {
  private queue: TrainingTask[] = []
  private isProcessing: boolean = false
  private currentTask: TrainingTask | null = null
  private maxConcurrent: number = 1 // 最大同时运行任务数，默认为1

  // 单例模式
  private static instance: TaskQueue

  public static getInstance(): TaskQueue {
    if (!TaskQueue.instance) {
      TaskQueue.instance = new TaskQueue()
    }
    return TaskQueue.instance
  }

  private constructor() {}

  // 添加任务到队列
  public async addTask(task: TrainingTask): Promise<void> {
    this.queue.push(task)
    await this.processNextTask()
  }

  // 获取队列状态
  public getStatus() {
    return {
      queueLength: this.queue.length,
      isProcessing: this.isProcessing,
      currentTask: this.currentTask,
    }
  }

  // 处理下一个任务
  private async processNextTask(): Promise<void> {
    if (this.isProcessing || this.queue.length === 0) {
      return
    }

    this.isProcessing = true
    this.currentTask = this.queue.shift() || null

    if (!this.currentTask) {
      this.isProcessing = false
      return
    }

    try {
      await this.runTrainingTask(this.currentTask)
    } catch (error) {
      console.error('Error processing task:', error)
    } finally {
      this.isProcessing = false
      this.currentTask = null
      // 继续处理队列中的下一个任务
      await this.processNextTask()
    }
  }

  // 运行训练任务
  private async runTrainingTask(task: TrainingTask): Promise<void> {
    return new Promise((resolve, reject) => {
      const pythonProcess = spawn('python', [
        path.join(process.cwd(), 'ml_service/train.py'),
        JSON.stringify({
          task_id: task.id,
          model_id: task.modelId,
          dataset_path: task.datasetPath,
          epochs: task.epoch,
          learning_rate: task.learningRate,
        }),
      ])

      // 处理Python脚本的输出
      pythonProcess.stdout.on('data', async data => {
        try {
          console.log('Python Output:', data.toString())
          const output = JSON.parse(data.toString())
        } catch (e) {
          console.error('Error parsing Python output:', e)
        }
      })

      // 处理错误
      pythonProcess.stderr.on('data', async data => {
        console.error(`Python Error: ${data}`)
      })

      // 处理进程结束
      pythonProcess.on('close', code => {
        if (code === 0) {
          resolve()
        } else {
          reject(new Error(`Process exited with code ${code}`))
        }
      })
    })
  }
}

export default TaskQueue
