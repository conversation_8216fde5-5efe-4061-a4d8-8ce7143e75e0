/**
 * Electron API 类型声明
 */

declare global {
  interface Window {
    electronAPI?: {
      // 应用信息
      getAppVersion: () => Promise<string>
      getUserDataPath: () => Promise<string>

      // 窗口控制
      windowMinimize: () => Promise<void>
      windowMaximize: () => Promise<void>
      windowClose: () => Promise<void>
      isWindowMaximized: () => Promise<boolean>

      // 平台信息
      platform: string

      // 文件操作
      readFile: (filePath: string) => Promise<string>
      writeFile: (filePath: string, data: string) => Promise<void>

      // 通知
      showNotification: (title: string, body: string) => Promise<void>

      // 确认对话框
      showConfirmDialog: (options: {
        title?: string
        message: string
        confirmText?: string
        cancelText?: string
        type?: 'info' | 'warning' | 'error' | 'question'
      }) => Promise<boolean>
    }
  }
}

export {}
