/**
 * 媒体类型枚举
 */
export enum MediaType {
  IMAGE = 'IMAGE', // 图片
  VIDEO = 'VIDEO', // 视频
}

export class MediaTypeHelper {
  static isImage(itemType: string): boolean {
    const type = itemType.toUpperCase() as MediaType
    return type === MediaType.IMAGE
  }

  static isVideo(itemType: string): boolean {
    const type = itemType.toUpperCase() as MediaType
    return type === MediaType.VIDEO
  }

  private static readonly descriptions: Record<MediaType, string> = {
    [MediaType.IMAGE]: '图片',
    [MediaType.VIDEO]: '视频',
  }

  /**
   * 获取媒体类型的描述
   * @param mediaType 媒体类型字符串
   * @returns 媒体类型的描述
   */
  static getDescByString(mediaType: string): string {
    const type = mediaType.toUpperCase() as MediaType
    return this.descriptions[type] || ''
  }
}

/**
 * 数据来源枚举
 */
export enum DataItemSource {
  UPLOAD = 'UPLOAD', // 上传
  COLLECTION = 'COLLECTION', // 采集
  ANNOTATION = 'ANNOTATION', // 标注
}

export class DataItemSourceHelper {
  private static readonly descriptions: Record<DataItemSource, string> = {
    [DataItemSource.UPLOAD]: '上传',
    [DataItemSource.COLLECTION]: '采集',
    [DataItemSource.ANNOTATION]: '标注',
  }

  /**
   * 获取数据来源的描述
   * @param dataSource 数据来源字符串
   * @returns 数据来源的描述
   */
  static getDescByString(dataSource: string): string {
    const type = dataSource.toUpperCase() as DataItemSource
    return this.descriptions[type] || ''
  }
}

export enum Emotion {
  ANGER = 'ANGER',
  DISGUST = 'DISGUST',
  FEAR = 'FEAR',
  HAPPY = 'HAPPY',
  NEUTRAL = 'NEUTRAL',
  SAD = 'SAD',
  SURPRISE = 'SURPRISE',
}

export class EmotionHelper {
  private static readonly descriptions: Record<Emotion, string> = {
    [Emotion.ANGER]: '愤怒',
    [Emotion.DISGUST]: '厌恶',
    [Emotion.FEAR]: '恐惧',
    [Emotion.HAPPY]: '高兴',
    [Emotion.NEUTRAL]: '中性',
    [Emotion.SAD]: '悲伤',
    [Emotion.SURPRISE]: '惊讶',
  }

  /**
   * 获取情绪的描述
   * @param emotion 情绪字符串
   * @returns 情绪的描述
   */
  static getDescByString(emotion: string): string {
    const type = emotion.toUpperCase() as Emotion
    return this.descriptions[type] || ''
  }

  private static readonly colors: Record<Emotion, string> = {
    [Emotion.ANGER]: 'text-red-600 bg-red-50 border-red-200',
    [Emotion.DISGUST]: 'text-gray-600 bg-gray-50 border-gray-200',
    [Emotion.FEAR]: 'text-gray-600 bg-gray-50 border-gray-200',
    [Emotion.HAPPY]: 'text-yellow-600 bg-yellow-50 border-yellow-200',
    [Emotion.NEUTRAL]: 'text-gray-600 bg-gray-50 border-gray-200',
    [Emotion.SAD]: 'text-blue-600 bg-blue-50 border-blue-200',
    [Emotion.SURPRISE]: 'text-purple-600 bg-purple-50 border-purple-200',
  }

  /**
   * 获取情绪的颜色
   * @param emotion 情绪字符串
   * @returns 情绪的颜色
   */
  static getColorByString(emotion: string): string {
    const type = emotion.toUpperCase() as Emotion
    return this.colors[type] || ''
  }
}

/**
 * 数据状态枚举
 */
export enum DataItemStatus {
  ANNOTATED = 'ANNOTATED',
  UNANNOTATED = 'UNANNOTATED',
}

export class DataItemStatusHelper {
  private static readonly descriptions: Record<DataItemStatus, string> = {
    [DataItemStatus.ANNOTATED]: '已标注',
    [DataItemStatus.UNANNOTATED]: '未标注',
  }

  static isAnnotated(status: string): boolean {
    const type = status.toUpperCase() as DataItemStatus
    return type === DataItemStatus.ANNOTATED
  }

  /**
   * 获取数据状态的描述
   * @param dataItemStatus 数据状态字符串
   * @returns 数据状态的描述
   */
  static getDescByString(dataItemStatus: string): string {
    const type = dataItemStatus.toUpperCase() as DataItemStatus
    return this.descriptions[type] || ''
  }

  private static readonly colors: Record<DataItemStatus, string> = {
    [DataItemStatus.ANNOTATED]: 'text-green-600 bg-green-50 border-green-200',
    [DataItemStatus.UNANNOTATED]: 'text-red-600 bg-red-50 border-red-200',
  }

  /**
   * 获取数据状态的颜色
   * @param dataItemStatus 数据状态字符串
   * @returns 数据状态的颜色
   */
  static getColorByString(dataItemStatus: string): string {
    const type = dataItemStatus.toUpperCase() as DataItemStatus
    return this.colors[type] || ''
  }
}
