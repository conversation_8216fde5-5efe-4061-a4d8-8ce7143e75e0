const { contextBridge, ipc<PERSON>enderer } = require('electron')

// 向渲染进程暴露安全的 API
contextBridge.exposeInMainWorld('electronAPI', {
  // 应用信息
  getAppVersion: () => ipcRenderer.invoke('get-app-version'),
  getUserDataPath: () => ipcRenderer.invoke('get-user-data-path'),

  // 窗口控制
  windowMinimize: () => ipcRenderer.invoke('window-minimize'),
  windowMaximize: () => ipcRenderer.invoke('window-maximize'),
  windowClose: () => ipcRenderer.invoke('window-close'),
  isWindowMaximized: () => ipcRenderer.invoke('is-window-maximized'),

  // 平台信息
  platform: process.platform,

  // 文件操作
  readFile: filePath => ipcRenderer.invoke('read-file', filePath),
  writeFile: (filePath, data) => ipcRenderer.invoke('write-file', filePath, data),

  // 通知
  showNotification: (title, body) => ipcRenderer.invoke('show-notification', title, body),

  // 确认对话框
  showConfirmDialog: (options) => ipcRenderer.invoke('show-confirm-dialog', options),
})

// 监听来自主进程的消息
window.addEventListener('DOMContentLoaded', () => {
  console.log('Electron Preload Script Loaded')
})
