let userConfig = undefined
try {
  userConfig = await import('./v0-user-next.config')
} catch (e) {
  // ignore error
}

// 检查是否为 Electron 构建模式
const isElectronBuild = process.env.ELECTRON_BUILD === 'true'

/** @type {import('next').NextConfig} */
const nextConfig = {
  reactStrictMode: false,
  eslint: {
    ignoreDuringBuilds: true,
  },
  typescript: {
    ignoreBuildErrors: true,
  },
  images: {
    unoptimized: true,
  },
  experimental: {
    webpackBuildWorker: true,
    parallelServerBuildTraces: true,
    parallelServerCompiles: true,
  },
  // 只在 Electron 构建时使用 export 模式
  ...(isElectronBuild && {
    output: 'export',
    trailingSlash: true,
    assetPrefix: './',
    distDir: 'out',
  }),
  async rewrites() {
    return [
      {
        source: '/api/annotation/resource',
        destination: 'http://localhost:8080/api/annotation/resource',
      },
      {
        source: '/api/:path*',
        destination: 'http://localhost:8080/api/:path*',
      },
      {
        source: '/me-detection/:path*',
        destination: 'http://localhost:8000/me-detection/:path*',
      },
    ]
  },
}

mergeConfig(nextConfig, userConfig)

function mergeConfig(nextConfig, userConfig) {
  if (!userConfig) {
    return
  }

  for (const key in userConfig) {
    if (typeof nextConfig[key] === 'object' && !Array.isArray(nextConfig[key])) {
      nextConfig[key] = {
        ...nextConfig[key],
        ...userConfig[key],
      }
    } else {
      nextConfig[key] = userConfig[key]
    }
  }
}

export default nextConfig
